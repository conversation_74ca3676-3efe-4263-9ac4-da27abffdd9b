'use client';

import React from 'react';
import Link from 'next/link';
import Image from 'next/image';
import { Clock, Star, Play } from 'lucide-react';
import { motion, useInView } from 'framer-motion';
import { cn, getImageUrl, formatRating } from '@/lib/utils';

interface MovieItem {
  _id: string;
  imdbId: string;
  title: string;
  year?: number;
  posterUrl?: string;
  imdbRating?: number;
  runtime?: string;
}

interface QuickPicksProps {
  movies: MovieItem[];
  className?: string;
  style?: React.CSSProperties;
}

const QuickPicks: React.FC<QuickPicksProps> = ({
  movies,
  className,
  style
}) => {
  const sectionRef = React.useRef<HTMLDivElement>(null);
  const isInView = useInView(sectionRef, { once: true, margin: "-100px" });

  if (movies.length === 0) return null;

  return (
    <motion.section
      ref={sectionRef}
      initial={{ opacity: 0, y: 50 }}
      animate={isInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 50 }}
      transition={{ duration: 0.8 }}
      className={cn("relative", className)}
      style={style}
    >
      <div className="max-w-[2560px] mx-auto px-6 lg:px-12">
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          animate={isInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 30 }}
          transition={{ duration: 0.6, delay: 0.2 }}
          className="text-center mb-12"
        >
          <div className="flex items-center justify-center space-x-3 mb-4">
            <div className="p-3 bg-gradient-to-br from-cyan-500 to-blue-600 rounded-xl">
              <Clock className="w-6 h-6 text-white" />
            </div>
            <h2 className="text-4xl lg:text-5xl font-black text-white">
              Quick Picks
            </h2>
          </div>
          <p className="text-gray-400 text-lg max-w-2xl mx-auto">
            Perfect for a quick movie night - under 2 hours
          </p>
        </motion.div>

        <div className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-5 gap-4">
          {movies.slice(0, 10).map((item, index) => (
            <motion.div
              key={item._id}
              initial={{ opacity: 0, scale: 0.9 }}
              animate={isInView ? { opacity: 1, scale: 1 } : { opacity: 0, scale: 0.9 }}
              transition={{ duration: 0.5, delay: 0.1 * index }}
              className="group"
            >
              <Link href={`/watch/movie/${item.imdbId}`}>
                <div className="relative w-full h-72 rounded-xl overflow-hidden bg-gray-900 group-hover:scale-105 transition-all duration-500 shadow-lg hover:shadow-2xl">
                  <div className="absolute top-2 left-2 z-10 px-2 py-1 bg-cyan-500/80 rounded-full text-xs font-semibold text-white backdrop-blur-sm">
                    ⚡ Quick
                  </div>

                  <Image
                    src={getImageUrl(item.posterUrl)}
                    alt={item.title}
                    fill
                    className="object-cover transition-transform duration-500 group-hover:scale-110"
                  />
                  <div className="absolute inset-0 bg-gradient-to-t from-black via-transparent to-transparent opacity-60 group-hover:opacity-80 transition-opacity duration-300" />
                  
                  <div className="absolute inset-0 flex items-center justify-center opacity-0 group-hover:opacity-100 transition-all duration-300">
                    <div className="p-3 bg-white/20 rounded-full backdrop-blur-sm border border-white/30">
                      <Play className="w-6 h-6 text-white fill-current" />
                    </div>
                  </div>

                  <div className="absolute bottom-0 left-0 right-0 p-3 space-y-1">
                    <h4 className="text-white font-bold text-sm leading-tight line-clamp-2">
                      {item.title}
                    </h4>
                    <div className="flex items-center justify-between">
                      <span className="text-gray-300 text-xs">
                        {item.runtime || `${item.year}`}
                      </span>
                      {item.imdbRating && (
                        <div className="flex items-center space-x-1 px-1.5 py-0.5 bg-yellow-500/20 rounded-full">
                          <Star className="w-2.5 h-2.5 text-yellow-400 fill-current" />
                          <span className="text-yellow-400 text-xs font-bold">
                            {formatRating(item.imdbRating)}
                          </span>
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              </Link>
            </motion.div>
          ))}
        </div>
      </div>
    </motion.section>
  );
};

export default QuickPicks;
