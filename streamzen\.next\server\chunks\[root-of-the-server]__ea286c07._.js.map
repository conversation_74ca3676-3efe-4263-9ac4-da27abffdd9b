{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 60, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/New%20folder%20%2825%29/streamzen/src/app/api/admin/sync-episodes/route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server';\n\n// GET method for easy browser testing\nexport async function GET(request: NextRequest) {\n  return await runSync();\n}\n\nexport async function POST(request: NextRequest) {\n  return await runSync();\n}\n\nasync function runSync() {\n  try {\n    console.log('🔧 Manual VidSrc Episodes Sync Triggered');\n\n    // Call the sync endpoint\n    const baseUrl = process.env.NEXTAUTH_URL || 'http://localhost:3000';\n    const response = await fetch(`${baseUrl}/api/sync/vidsrc-episodes`, {\n      method: 'POST',\n      headers: {\n        'Content-Type': 'application/json',\n      },\n    });\n\n    if (response.ok) {\n      const result = await response.json();\n      return NextResponse.json({\n        success: true,\n        message: 'Manual sync completed successfully',\n        ...result\n      });\n    } else {\n      const error = await response.text();\n      return NextResponse.json(\n        {\n          success: false,\n          error: 'Manual sync failed',\n          details: error\n        },\n        { status: response.status }\n      );\n    }\n  } catch (error) {\n    console.error('❌ Manual sync error:', error);\n    return NextResponse.json(\n      {\n        success: false,\n        error: 'Manual sync failed',\n        message: error instanceof Error ? error.message : 'Unknown error'\n      },\n      { status: 500 }\n    );\n  }\n}\n"], "names": [], "mappings": ";;;;AAAA;;AAGO,eAAe,IAAI,OAAoB;IAC5C,OAAO,MAAM;AACf;AAEO,eAAe,KAAK,OAAoB;IAC7C,OAAO,MAAM;AACf;AAEA,eAAe;IACb,IAAI;QACF,QAAQ,GAAG,CAAC;QAEZ,yBAAyB;QACzB,MAAM,UAAU,QAAQ,GAAG,CAAC,YAAY,IAAI;QAC5C,MAAM,WAAW,MAAM,MAAM,GAAG,QAAQ,yBAAyB,CAAC,EAAE;YAClE,QAAQ;YACR,SAAS;gBACP,gBAAgB;YAClB;QACF;QAEA,IAAI,SAAS,EAAE,EAAE;YACf,MAAM,SAAS,MAAM,SAAS,IAAI;YAClC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;gBACvB,SAAS;gBACT,SAAS;gBACT,GAAG,MAAM;YACX;QACF,OAAO;YACL,MAAM,QAAQ,MAAM,SAAS,IAAI;YACjC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBACE,SAAS;gBACT,OAAO;gBACP,SAAS;YACX,GACA;gBAAE,QAAQ,SAAS,MAAM;YAAC;QAE9B;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,wBAAwB;QACtC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YACE,SAAS;YACT,OAAO;YACP,SAAS,iBAAiB,QAAQ,MAAM,OAAO,GAAG;QACpD,GACA;YAAE,QAAQ;QAAI;IAElB;AACF", "debugId": null}}]}