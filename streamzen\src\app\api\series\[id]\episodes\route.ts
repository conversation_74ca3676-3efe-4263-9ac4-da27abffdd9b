import { NextRequest, NextResponse } from 'next/server';
import ContentService from '@/lib/contentService';

const contentService = ContentService.getInstance();

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params;
    const { searchParams } = new URL(request.url);
    const season = searchParams.get('season') ? parseInt(searchParams.get('season')!) : undefined;
    
    const episodes = await contentService.getSeriesEpisodes(id, season);
    
    return NextResponse.json(episodes);
  } catch (error) {
    console.error('Error fetching episodes:', error);
    return NextResponse.json(
      { error: 'Failed to fetch episodes' },
      { status: 500 }
    );
  }
}
