(globalThis.TURBOPACK = globalThis.TURBOPACK || []).push([typeof document === "object" ? document.currentScript : undefined, {

"[project]/src/models/SyncStatus.ts [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/src_models_SyncStatus_ts_64f319f2._.js",
  "static/chunks/src_models_SyncStatus_ts_b50cce25._.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/src/models/SyncStatus.ts [app-client] (ecmascript)");
    });
});
}}),
}]);