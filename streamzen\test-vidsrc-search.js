const axios = require('axios');

async function testVidSrcForImdbId() {
  const targetImdbId = 'tt2828240'; // Zindagi Gulzar Hai
  const targetSeries = 'Zindagi Gulzar Hai';
  
  console.log(`🔍 Searching for "${targetSeries}" (${targetImdbId}) in VidSrc latest episodes...`);
  console.log('=' .repeat(80));
  
  let found = false;
  let totalEpisodes = 0;
  
  try {
    // Check pages 1-15 of VidSrc latest episodes
    for (let page = 1; page <= 15; page++) {
      console.log(`📄 Checking page ${page}...`);
      
      try {
        const url = `https://vidsrc.xyz/episodes/latest/page-${page}.json`;
        const response = await axios.get(url, {
          timeout: 10000,
          headers: {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
            'Accept': 'application/json',
          }
        });
        
        const episodes = response.data.result || [];
        totalEpisodes += episodes.length;
        
        console.log(`   📺 Found ${episodes.length} episodes on page ${page}`);
        
        // Check if our target series is in this page
        const matchingEpisodes = episodes.filter(ep => 
          ep.imdb_id === targetImdbId || 
          ep.show_title?.toLowerCase().includes('zindagi gulzar hai')
        );
        
        if (matchingEpisodes.length > 0) {
          found = true;
          console.log(`   ✅ FOUND "${targetSeries}" on page ${page}!`);
          console.log('   📝 Episode details:');
          matchingEpisodes.forEach(ep => {
            console.log(`      - S${ep.season}E${ep.episode}: ${ep.title || 'No title'}`);
            console.log(`        IMDb ID: ${ep.imdb_id}`);
            console.log(`        Show: ${ep.show_title}`);
            console.log(`        Date: ${ep.date || 'No date'}`);
          });
        }
        
        // Show sample data from first page
        if (page === 1 && episodes.length > 0) {
          console.log('   📋 Sample episodes from page 1:');
          episodes.slice(0, 3).forEach(ep => {
            console.log(`      - ${ep.show_title} S${ep.season}E${ep.episode} (${ep.imdb_id})`);
          });
        }
        
        // Small delay to be respectful to the API
        await new Promise(resolve => setTimeout(resolve, 500));
        
      } catch (error) {
        console.log(`   ❌ Error on page ${page}: ${error.message}`);
      }
    }
    
    console.log('=' .repeat(80));
    console.log(`📊 SEARCH RESULTS:`);
    console.log(`   Total episodes checked: ${totalEpisodes}`);
    console.log(`   Target series found: ${found ? '✅ YES' : '❌ NO'}`);
    
    if (!found) {
      console.log(`\n🚨 CONCLUSION: "${targetSeries}" (${targetImdbId}) is NOT in VidSrc latest episodes!`);
      console.log(`   This means the episodes page is NOT using VidSrc latest episodes data.`);
      console.log(`   The episodes are coming from a different source.`);
    } else {
      console.log(`\n✅ CONCLUSION: "${targetSeries}" (${targetImdbId}) IS in VidSrc latest episodes!`);
      console.log(`   The episodes page might be using VidSrc data.`);
    }
    
  } catch (error) {
    console.error('❌ Test failed:', error.message);
  }
}

// Run the test
testVidSrcForImdbId();
