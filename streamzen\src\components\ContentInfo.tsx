import React from 'react';
import Image from 'next/image';
import { Star, Calendar, Clock, User, Users } from 'lucide-react';
import { getImageUrl, formatRating } from '@/lib/utils';

interface ContentInfoProps {
  content: {
    title: string;
    year?: number;
    rating?: string;
    runtime?: string;
    imdbRating?: number;
    description?: string;
    genres?: string[];
    director?: string;
    creator?: string;
    cast?: string[];
    language?: string;
    country?: string;
    posterUrl?: string;
    type: 'movie' | 'series';
    totalSeasons?: number;
    status?: string;
  };
}

const ContentInfo: React.FC<ContentInfoProps> = ({ content }) => {
  return (
    <div className="glass-elevated rounded-3xl overflow-hidden">
      <div className="grid grid-cols-1 lg:grid-cols-4 gap-8 p-8">
        {/* Poster */}
        <div className="lg:col-span-1">
          <div className="relative aspect-[2/3] rounded-2xl overflow-hidden glass border border-white/10 shadow-2xl">
            <Image
              src={getImageUrl(content.posterUrl)}
              alt={content.title}
              fill
              className="object-cover"
              sizes="(max-width: 768px) 100vw, (max-width: 1200px) 25vw, 20vw"
            />
            <div className="absolute inset-0 bg-gradient-to-t from-black/20 to-transparent" />
          </div>
        </div>

        {/* Content Details */}
        <div className="lg:col-span-3">
          <div className="space-y-6">
            {/* Title and Basic Info */}
            <div>
              <h1 className="text-4xl md:text-5xl font-black text-white mb-6 tracking-tight">
                {content.title}
              </h1>
              
              <div className="flex flex-wrap items-center gap-6 mb-8">
                {content.year && (
                  <div className="glass px-4 py-2 rounded-xl flex items-center space-x-2">
                    <Calendar size={18} className="text-blue-400" />
                    <span className="text-white font-medium">{content.year}</span>
                  </div>
                )}

                {content.runtime && (
                  <div className="glass px-4 py-2 rounded-xl flex items-center space-x-2">
                    <Clock size={18} className="text-green-400" />
                    <span className="text-white font-medium">{content.runtime}</span>
                  </div>
                )}

                {content.rating && (
                  <div className="glass px-4 py-2 rounded-xl bg-yellow-500/20 border border-yellow-500/30">
                    <span className="text-yellow-300 font-bold text-sm">{content.rating}</span>
                  </div>
                )}

                {content.imdbRating && (
                  <div className="glass px-4 py-2 rounded-xl flex items-center space-x-2">
                    <Star size={18} className="text-red-500 fill-current" />
                    <span className="text-white font-bold">{formatRating(content.imdbRating)}</span>
                  </div>
                )}

                {content.language && (
                  <div className="glass px-4 py-2 rounded-xl flex items-center space-x-2">
                    <span className="text-purple-400 font-medium text-sm">🌐</span>
                    <span className="text-white font-medium">{content.language}</span>
                  </div>
                )}

                {content.country && (
                  <div className="glass px-4 py-2 rounded-xl flex items-center space-x-2">
                    <span className="text-orange-400 font-medium text-sm">🏳️</span>
                    <span className="text-white font-medium">{content.country}</span>
                  </div>
                )}
              </div>

              {/* Series-specific info */}
              {content.type === 'series' && (
                <div className="flex flex-wrap items-center gap-4 text-gray-300 mb-4">
                  {content.totalSeasons && (
                    <span>{content.totalSeasons} Season{content.totalSeasons > 1 ? 's' : ''}</span>
                  )}
                  {content.status && (
                    <div className="px-2 py-1 bg-blue-600 rounded text-sm capitalize">
                      {content.status}
                    </div>
                  )}
                </div>
              )}
            </div>

            {/* Genres */}
            {content.genres && content.genres.length > 0 && (
              <div>
                <h3 className="text-white font-bold text-xl mb-4">Genres</h3>
                <div className="flex flex-wrap gap-3">
                  {content.genres.map((genre) => (
                    <span
                      key={genre}
                      className="glass px-4 py-2 rounded-xl text-sm text-white font-medium border border-white/10 hover:border-white/20 transition-all duration-300"
                    >
                      {genre}
                    </span>
                  ))}
                </div>
              </div>
            )}

            {/* Description */}
            {content.description && (
              <div>
                <h3 className="text-white font-bold text-xl mb-4">Synopsis</h3>
                <p className="text-gray-300 leading-relaxed text-lg">
                  {content.description}
                </p>
              </div>
            )}

            {/* Director/Creator */}
            {(content.director || content.creator) && (
              <div>
                <h3 className="text-white font-semibold mb-2">
                  {content.type === 'movie' ? 'Director' : 'Creator'}
                </h3>
                <div className="flex items-center space-x-2 text-gray-300">
                  <User size={16} />
                  <span>{content.director || content.creator}</span>
                </div>
              </div>
            )}

            {/* Cast */}
            {content.cast && content.cast.length > 0 && (
              <div>
                <h3 className="text-white font-semibold mb-2">Cast</h3>
                <div className="flex items-start space-x-2 text-gray-300">
                  <Users size={16} className="mt-1 flex-shrink-0" />
                  <div className="flex flex-wrap gap-1">
                    {content.cast.slice(0, 10).map((actor, index) => (
                      <span key={actor}>
                        {actor}
                        {index < Math.min(content.cast!.length - 1, 9) && ', '}
                      </span>
                    ))}
                    {content.cast.length > 10 && (
                      <span className="text-gray-400">
                        and {content.cast.length - 10} more...
                      </span>
                    )}
                  </div>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default ContentInfo;
