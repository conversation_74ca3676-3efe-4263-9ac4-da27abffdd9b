import React from 'react';
import Image from 'next/image';
import { Star, Calendar, Clock, User, Users, Play, Heart, Share2, Bookmark, Award, Globe, Sparkles, TrendingUp } from 'lucide-react';
import { motion } from 'framer-motion';
import { getImageUrl, formatRating, cn } from '@/lib/utils';

interface ContentInfoProps {
  content: {
    title: string;
    year?: number;
    rating?: string;
    runtime?: string;
    imdbRating?: number;
    description?: string;
    genres?: string[];
    director?: string;
    creator?: string;
    cast?: string[];
    language?: string;
    country?: string;
    posterUrl?: string;
    type: 'movie' | 'series';
    totalSeasons?: number;
    status?: string;
  };
}

const ContentInfo: React.FC<ContentInfoProps> = ({ content }) => {
  return (
    <motion.div
      className="bg-gradient-to-br from-gray-900/50 via-black/50 to-gray-900/50 backdrop-blur-xl rounded-3xl overflow-hidden border border-white/10 shadow-2xl"
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.6 }}
    >
      <div className="grid grid-cols-1 lg:grid-cols-4 gap-8 p-8">
        {/* Enhanced Poster */}
        <motion.div
          className="lg:col-span-1"
          initial={{ opacity: 0, x: -20 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ duration: 0.6, delay: 0.2 }}
        >
          <div className="relative aspect-[2/3] rounded-2xl overflow-hidden bg-gradient-to-br from-gray-800 to-gray-900 border border-white/20 shadow-2xl group">
            <Image
              src={getImageUrl(content.posterUrl)}
              alt={content.title}
              fill
              className="object-cover transition-transform duration-500 group-hover:scale-105"
              sizes="(max-width: 768px) 100vw, (max-width: 1200px) 25vw, 20vw"
            />
            <div className="absolute inset-0 bg-gradient-to-t from-black/40 via-transparent to-black/20" />

            {/* Floating Action Buttons */}
            <div className="absolute top-4 right-4 flex flex-col space-y-2 opacity-0 group-hover:opacity-100 transition-all duration-300">
              <motion.button
                className="p-2 bg-black/60 backdrop-blur-sm rounded-full text-white hover:bg-red-500 transition-all duration-300"
                whileHover={{ scale: 1.1 }}
                whileTap={{ scale: 0.9 }}
              >
                <Heart size={16} />
              </motion.button>
              <motion.button
                className="p-2 bg-black/60 backdrop-blur-sm rounded-full text-white hover:bg-blue-500 transition-all duration-300"
                whileHover={{ scale: 1.1 }}
                whileTap={{ scale: 0.9 }}
              >
                <Bookmark size={16} />
              </motion.button>
              <motion.button
                className="p-2 bg-black/60 backdrop-blur-sm rounded-full text-white hover:bg-green-500 transition-all duration-300"
                whileHover={{ scale: 1.1 }}
                whileTap={{ scale: 0.9 }}
              >
                <Share2 size={16} />
              </motion.button>
            </div>

            {/* Rating Badge */}
            {content.imdbRating && (
              <motion.div
                className="absolute bottom-4 left-4 flex items-center space-x-1 bg-black/80 backdrop-blur-sm px-3 py-1 rounded-full"
                initial={{ scale: 0 }}
                animate={{ scale: 1 }}
                transition={{ delay: 0.5, type: "spring" }}
              >
                <Star size={14} className="text-yellow-400 fill-current" />
                <span className="text-white font-semibold text-sm">{content.imdbRating}</span>
              </motion.div>
            )}
          </div>
        </motion.div>

        {/* Enhanced Content Details */}
        <motion.div
          className="lg:col-span-3"
          initial={{ opacity: 0, x: 20 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ duration: 0.6, delay: 0.3 }}
        >
          <div className="space-y-8">
            {/* Enhanced Title and Rating */}
            <div>
              <motion.h1
                className="text-4xl lg:text-6xl font-black text-transparent bg-clip-text bg-gradient-to-r from-white via-gray-200 to-gray-400 mb-6 leading-tight"
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: 0.4 }}
              >
                {content.title}
              </motion.h1>

              <motion.div
                className="flex flex-wrap items-center gap-4 mb-8"
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: 0.5 }}
              >
                {content.year && (
                  <motion.div
                    className="bg-gradient-to-r from-blue-500/20 to-blue-600/20 backdrop-blur-sm border border-blue-500/30 px-4 py-2 rounded-xl flex items-center space-x-2"
                    whileHover={{ scale: 1.05 }}
                  >
                    <Calendar size={18} className="text-blue-400" />
                    <span className="text-white font-medium">{content.year}</span>
                  </motion.div>
                )}

                {content.runtime && (
                  <motion.div
                    className="bg-gradient-to-r from-green-500/20 to-green-600/20 backdrop-blur-sm border border-green-500/30 px-4 py-2 rounded-xl flex items-center space-x-2"
                    whileHover={{ scale: 1.05 }}
                  >
                    <Clock size={18} className="text-green-400" />
                    <span className="text-white font-medium">{content.runtime}</span>
                  </motion.div>
                )}

                {content.rating && (
                  <motion.div
                    className="bg-gradient-to-r from-yellow-500/20 to-yellow-600/20 backdrop-blur-sm border border-yellow-500/30 px-4 py-2 rounded-xl"
                    whileHover={{ scale: 1.05 }}
                  >
                    <span className="text-yellow-300 font-bold text-sm">{content.rating}</span>
                  </motion.div>
                )}

                {content.imdbRating && (
                  <motion.div
                    className="bg-gradient-to-r from-yellow-500/20 to-orange-500/20 backdrop-blur-sm border border-yellow-500/30 px-4 py-2 rounded-xl flex items-center space-x-2"
                    whileHover={{ scale: 1.05 }}
                  >
                    <Star size={18} className="text-yellow-400 fill-current" />
                    <span className="text-yellow-300 font-bold">{formatRating(content.imdbRating)}</span>
                    <span className="text-gray-400 text-sm">/10</span>
                  </motion.div>
                )}

                {content.type === 'series' && content.totalSeasons && (
                  <motion.div
                    className="bg-gradient-to-r from-purple-500/20 to-purple-600/20 backdrop-blur-sm border border-purple-500/30 px-4 py-2 rounded-xl flex items-center space-x-2"
                    whileHover={{ scale: 1.05 }}
                  >
                    <TrendingUp size={18} className="text-purple-400" />
                    <span className="text-white font-medium">{content.totalSeasons} Season{content.totalSeasons > 1 ? 's' : ''}</span>
                  </motion.div>
                )}

                {content.language && (
                  <div className="glass px-4 py-2 rounded-xl flex items-center space-x-2">
                    <span className="text-purple-400 font-medium text-sm">🌐</span>
                    <span className="text-white font-medium">{content.language}</span>
                  </div>
                )}

                {content.country && (
                  <div className="glass px-4 py-2 rounded-xl flex items-center space-x-2">
                    <span className="text-orange-400 font-medium text-sm">🏳️</span>
                    <span className="text-white font-medium">{content.country}</span>
                  </div>
                )}
              </div>

              {/* Series-specific info */}
              {content.type === 'series' && (
                <div className="flex flex-wrap items-center gap-4 text-gray-300 mb-4">
                  {content.totalSeasons && (
                    <span>{content.totalSeasons} Season{content.totalSeasons > 1 ? 's' : ''}</span>
                  )}
                  {content.status && (
                    <div className="px-2 py-1 bg-blue-600 rounded text-sm capitalize">
                      {content.status}
                    </div>
                  )}
                </div>
              )}
            </div>

            {/* Genres */}
            {content.genres && content.genres.length > 0 && (
              <div>
                <h3 className="text-white font-bold text-xl mb-4">Genres</h3>
                <div className="flex flex-wrap gap-3">
                  {content.genres.map((genre) => (
                    <span
                      key={genre}
                      className="glass px-4 py-2 rounded-xl text-sm text-white font-medium border border-white/10 hover:border-white/20 transition-all duration-300"
                    >
                      {genre}
                    </span>
                  ))}
                </div>
              </div>
            )}

            {/* Description */}
            {content.description && (
              <div>
                <h3 className="text-white font-bold text-xl mb-4">Synopsis</h3>
                <p className="text-gray-300 leading-relaxed text-lg">
                  {content.description}
                </p>
              </div>
            )}

            {/* Director/Creator */}
            {(content.director || content.creator) && (
              <div>
                <h3 className="text-white font-semibold mb-2">
                  {content.type === 'movie' ? 'Director' : 'Creator'}
                </h3>
                <div className="flex items-center space-x-2 text-gray-300">
                  <User size={16} />
                  <span>{content.director || content.creator}</span>
                </div>
              </div>
            )}

            {/* Cast */}
            {content.cast && content.cast.length > 0 && (
              <div>
                <h3 className="text-white font-semibold mb-2">Cast</h3>
                <div className="flex items-start space-x-2 text-gray-300">
                  <Users size={16} className="mt-1 flex-shrink-0" />
                  <div className="flex flex-wrap gap-1">
                    {content.cast.slice(0, 10).map((actor, index) => (
                      <span key={actor}>
                        {actor}
                        {index < Math.min(content.cast!.length - 1, 9) && ', '}
                      </span>
                    ))}
                    {content.cast.length > 10 && (
                      <span className="text-gray-400">
                        and {content.cast.length - 10} more...
                      </span>
                    )}
                  </div>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default ContentInfo;
