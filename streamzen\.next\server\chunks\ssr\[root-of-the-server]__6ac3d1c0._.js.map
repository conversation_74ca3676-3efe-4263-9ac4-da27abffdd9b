{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 24, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/New%20folder%20%2825%29/streamzen/src/lib/api.ts"], "sourcesContent": ["const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || (typeof window !== 'undefined' ? window.location.origin : 'http://localhost:3000');\n\nexport interface Movie {\n  _id: string;\n  imdbId: string;\n  tmdbId?: string;\n  title: string;\n  year: number;\n  rating?: string;\n  runtime?: string;\n  imdbRating?: number;\n  imdbVotes?: string;\n  popularity?: number;\n  popularityDelta?: number;\n  posterUrl?: string;\n  trailerUrl?: string;\n  trailerRuntime?: string;\n  trailerLikes?: string;\n  description?: string;\n  genres?: string[];\n  director?: string;\n  cast?: string[];\n  language?: string;\n  country?: string;\n  embedUrl: string;\n  embedUrlTmdb?: string;\n  quality?: string;\n  createdAt: string;\n  updatedAt: string;\n}\n\nexport interface Series {\n  _id: string;\n  imdbId: string;\n  tmdbId?: string;\n  title: string;\n  startYear: number;\n  endYear?: number;\n  rating?: string;\n  imdbRating?: number;\n  imdbVotes?: string;\n  popularity?: number;\n  popularityDelta?: number;\n  posterUrl?: string;\n  trailerUrl?: string;\n  description?: string;\n  genres?: string[];\n  creator?: string;\n  cast?: string[];\n  language?: string;\n  country?: string;\n  totalSeasons?: number;\n  status?: string;\n  embedUrl: string;\n  embedUrlTmdb?: string;\n  createdAt: string;\n  updatedAt: string;\n}\n\nexport interface Episode {\n  _id: string;\n  imdbId: string;\n  tmdbId?: string;\n  seriesTitle: string;\n  season: number;\n  episode: number;\n  episodeTitle?: string;\n  airDate?: string;\n  runtime?: string;\n  imdbRating?: number;\n  description?: string;\n  embedUrl: string;\n  embedUrlTmdb?: string;\n  quality?: string;\n  createdAt: string;\n  updatedAt: string;\n}\n\nexport interface PaginatedResponse<T> {\n  data: T[];\n  pagination: {\n    page: number;\n    limit: number;\n    total: number;\n    pages: number;\n  };\n}\n\nexport interface ContentFilters {\n  genre?: string;\n  year?: number;\n  language?: string;\n  country?: string;\n  rating?: string;\n  quality?: string;\n  sortBy?: 'title' | 'year' | 'imdbRating' | 'popularity' | 'createdAt';\n  sortOrder?: 'asc' | 'desc';\n  page?: number;\n  limit?: number;\n  search?: string;\n}\n\nexport interface GenreCount {\n  genre: string;\n  count: number;\n}\n\nexport interface LanguageCount {\n  language: string;\n  count: number;\n}\n\nexport interface CountryCount {\n  country: string;\n  count: number;\n}\n\nexport interface FilterOptions {\n  genres: GenreCount[];\n  languages: LanguageCount[];\n  countries: CountryCount[];\n  years: number[];\n  ratings: string[];\n  qualities: string[];\n}\n\nclass ApiClient {\n  private baseUrl: string;\n\n  constructor(baseUrl: string = API_BASE_URL) {\n    this.baseUrl = baseUrl;\n  }\n\n  private async request<T>(endpoint: string, options?: RequestInit): Promise<T> {\n    // For server-side rendering, use absolute URL\n    const baseUrl = this.baseUrl || (typeof window !== 'undefined' ? window.location.origin : 'http://localhost:3000');\n    const url = endpoint.startsWith('http') ? endpoint : `${baseUrl}${endpoint}`;\n\n    try {\n      const response = await fetch(url, {\n        headers: {\n          'Content-Type': 'application/json',\n          ...options?.headers,\n        },\n        ...options,\n      });\n\n      if (!response.ok) {\n        throw new Error(`HTTP error! status: ${response.status}`);\n      }\n\n      return await response.json();\n    } catch (error) {\n      console.error(`API request failed: ${url}`, error);\n      throw error;\n    }\n  }\n\n  // Movies\n  async getMovies(filters: ContentFilters = {}): Promise<PaginatedResponse<Movie>> {\n    const params = new URLSearchParams();\n    Object.entries(filters).forEach(([key, value]) => {\n      if (value !== undefined && value !== null) {\n        params.append(key, value.toString());\n      }\n    });\n    \n    return this.request<PaginatedResponse<Movie>>(`/api/movies?${params.toString()}`);\n  }\n\n  async getMovie(id: string): Promise<Movie> {\n    return this.request<Movie>(`/api/movies/${id}`);\n  }\n\n  // Series\n  async getSeries(filters: ContentFilters = {}): Promise<PaginatedResponse<Series>> {\n    const params = new URLSearchParams();\n    Object.entries(filters).forEach(([key, value]) => {\n      if (value !== undefined && value !== null) {\n        params.append(key, value.toString());\n      }\n    });\n    \n    return this.request<PaginatedResponse<Series>>(`/api/series?${params.toString()}`);\n  }\n\n  async getSeriesById(id: string): Promise<Series> {\n    return this.request<Series>(`/api/series/${id}`);\n  }\n\n  async getSeriesEpisodes(id: string, season?: number): Promise<Episode[]> {\n    const params = season ? `?season=${season}` : '';\n    return this.request<Episode[]>(`/api/series/${id}/episodes${params}`);\n  }\n\n  // Episodes\n  async getEpisodes(filters: ContentFilters = {}): Promise<PaginatedResponse<Episode>> {\n    const params = new URLSearchParams();\n    Object.entries(filters).forEach(([key, value]) => {\n      if (value !== undefined && value !== null) {\n        params.append(key, value.toString());\n      }\n    });\n    \n    return this.request<PaginatedResponse<Episode>>(`/api/episodes?${params.toString()}`);\n  }\n\n  // Requests\n  async createBulkRequest(imdbIds: string[], contentType: 'auto' | 'movie' | 'series' = 'auto'): Promise<{ requestId: string; status: string; totalCount: number; message: string }> {\n    return this.request('/api/requests', {\n      method: 'POST',\n      body: JSON.stringify({ imdbIds, contentType }),\n    });\n  }\n\n  async getRequestStatus(requestId: string): Promise<any> {\n    return this.request(`/api/requests/${requestId}`);\n  }\n\n  // Filter Options\n  async getMovieFilterOptions(): Promise<FilterOptions> {\n    return this.request<FilterOptions>('/api/movies/filters');\n  }\n\n  async getSeriesFilterOptions(): Promise<FilterOptions> {\n    return this.request<FilterOptions>('/api/series/filters');\n  }\n\n  async getEpisodeFilterOptions(): Promise<FilterOptions> {\n    return this.request<FilterOptions>('/api/episodes/filters');\n  }\n\n  // Search\n  async search(query: string, type: 'all' | 'movies' | 'series' | 'episodes' = 'all', page: number = 1, limit: number = 20): Promise<any> {\n    const params = new URLSearchParams({\n      q: query,\n      type,\n      page: page.toString(),\n      limit: limit.toString()\n    });\n    return this.request(`/api/search?${params.toString()}`);\n  }\n\n  async getSearchSuggestions(query: string, limit: number = 8): Promise<any> {\n    const params = new URLSearchParams({\n      q: query,\n      limit: limit.toString()\n    });\n    return this.request(`/api/search/suggestions?${params.toString()}`);\n  }\n\n  // Sync\n  async syncContent(): Promise<{ success: boolean; message: string; counts: { movies: number; series: number; episodes: number } }> {\n    return this.request('/api/sync', {\n      method: 'POST',\n    });\n  }\n}\n\nexport const apiClient = new ApiClient();\nexport default ApiClient;\n"], "names": [], "mappings": ";;;;AAAA,MAAM,eAAe,QAAQ,GAAG,CAAC,mBAAmB,IAAI,CAAC,6EAAyD,uBAAuB;AA8HzI,MAAM;IACI,QAAgB;IAExB,YAAY,UAAkB,YAAY,CAAE;QAC1C,IAAI,CAAC,OAAO,GAAG;IACjB;IAEA,MAAc,QAAW,QAAgB,EAAE,OAAqB,EAAc;QAC5E,8CAA8C;QAC9C,MAAM,UAAU,IAAI,CAAC,OAAO,IAAI,CAAC,6EAAyD,uBAAuB;QACjH,MAAM,MAAM,SAAS,UAAU,CAAC,UAAU,WAAW,GAAG,UAAU,UAAU;QAE5E,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,KAAK;gBAChC,SAAS;oBACP,gBAAgB;oBAChB,GAAG,SAAS,OAAO;gBACrB;gBACA,GAAG,OAAO;YACZ;YAEA,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,IAAI,MAAM,CAAC,oBAAoB,EAAE,SAAS,MAAM,EAAE;YAC1D;YAEA,OAAO,MAAM,SAAS,IAAI;QAC5B,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,CAAC,oBAAoB,EAAE,KAAK,EAAE;YAC5C,MAAM;QACR;IACF;IAEA,SAAS;IACT,MAAM,UAAU,UAA0B,CAAC,CAAC,EAAqC;QAC/E,MAAM,SAAS,IAAI;QACnB,OAAO,OAAO,CAAC,SAAS,OAAO,CAAC,CAAC,CAAC,KAAK,MAAM;YAC3C,IAAI,UAAU,aAAa,UAAU,MAAM;gBACzC,OAAO,MAAM,CAAC,KAAK,MAAM,QAAQ;YACnC;QACF;QAEA,OAAO,IAAI,CAAC,OAAO,CAA2B,CAAC,YAAY,EAAE,OAAO,QAAQ,IAAI;IAClF;IAEA,MAAM,SAAS,EAAU,EAAkB;QACzC,OAAO,IAAI,CAAC,OAAO,CAAQ,CAAC,YAAY,EAAE,IAAI;IAChD;IAEA,SAAS;IACT,MAAM,UAAU,UAA0B,CAAC,CAAC,EAAsC;QAChF,MAAM,SAAS,IAAI;QACnB,OAAO,OAAO,CAAC,SAAS,OAAO,CAAC,CAAC,CAAC,KAAK,MAAM;YAC3C,IAAI,UAAU,aAAa,UAAU,MAAM;gBACzC,OAAO,MAAM,CAAC,KAAK,MAAM,QAAQ;YACnC;QACF;QAEA,OAAO,IAAI,CAAC,OAAO,CAA4B,CAAC,YAAY,EAAE,OAAO,QAAQ,IAAI;IACnF;IAEA,MAAM,cAAc,EAAU,EAAmB;QAC/C,OAAO,IAAI,CAAC,OAAO,CAAS,CAAC,YAAY,EAAE,IAAI;IACjD;IAEA,MAAM,kBAAkB,EAAU,EAAE,MAAe,EAAsB;QACvE,MAAM,SAAS,SAAS,CAAC,QAAQ,EAAE,QAAQ,GAAG;QAC9C,OAAO,IAAI,CAAC,OAAO,CAAY,CAAC,YAAY,EAAE,GAAG,SAAS,EAAE,QAAQ;IACtE;IAEA,WAAW;IACX,MAAM,YAAY,UAA0B,CAAC,CAAC,EAAuC;QACnF,MAAM,SAAS,IAAI;QACnB,OAAO,OAAO,CAAC,SAAS,OAAO,CAAC,CAAC,CAAC,KAAK,MAAM;YAC3C,IAAI,UAAU,aAAa,UAAU,MAAM;gBACzC,OAAO,MAAM,CAAC,KAAK,MAAM,QAAQ;YACnC;QACF;QAEA,OAAO,IAAI,CAAC,OAAO,CAA6B,CAAC,cAAc,EAAE,OAAO,QAAQ,IAAI;IACtF;IAEA,WAAW;IACX,MAAM,kBAAkB,OAAiB,EAAE,cAA2C,MAAM,EAAuF;QACjL,OAAO,IAAI,CAAC,OAAO,CAAC,iBAAiB;YACnC,QAAQ;YACR,MAAM,KAAK,SAAS,CAAC;gBAAE;gBAAS;YAAY;QAC9C;IACF;IAEA,MAAM,iBAAiB,SAAiB,EAAgB;QACtD,OAAO,IAAI,CAAC,OAAO,CAAC,CAAC,cAAc,EAAE,WAAW;IAClD;IAEA,iBAAiB;IACjB,MAAM,wBAAgD;QACpD,OAAO,IAAI,CAAC,OAAO,CAAgB;IACrC;IAEA,MAAM,yBAAiD;QACrD,OAAO,IAAI,CAAC,OAAO,CAAgB;IACrC;IAEA,MAAM,0BAAkD;QACtD,OAAO,IAAI,CAAC,OAAO,CAAgB;IACrC;IAEA,SAAS;IACT,MAAM,OAAO,KAAa,EAAE,OAAiD,KAAK,EAAE,OAAe,CAAC,EAAE,QAAgB,EAAE,EAAgB;QACtI,MAAM,SAAS,IAAI,gBAAgB;YACjC,GAAG;YACH;YACA,MAAM,KAAK,QAAQ;YACnB,OAAO,MAAM,QAAQ;QACvB;QACA,OAAO,IAAI,CAAC,OAAO,CAAC,CAAC,YAAY,EAAE,OAAO,QAAQ,IAAI;IACxD;IAEA,MAAM,qBAAqB,KAAa,EAAE,QAAgB,CAAC,EAAgB;QACzE,MAAM,SAAS,IAAI,gBAAgB;YACjC,GAAG;YACH,OAAO,MAAM,QAAQ;QACvB;QACA,OAAO,IAAI,CAAC,OAAO,CAAC,CAAC,wBAAwB,EAAE,OAAO,QAAQ,IAAI;IACpE;IAEA,OAAO;IACP,MAAM,cAA4H;QAChI,OAAO,IAAI,CAAC,OAAO,CAAC,aAAa;YAC/B,QAAQ;QACV;IACF;AACF;AAEO,MAAM,YAAY,IAAI;uCACd", "debugId": null}}, {"offset": {"line": 150, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/New%20folder%20%2825%29/streamzen/src/components/VideoPlayer.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/VideoPlayer.tsx <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/VideoPlayer.tsx <module evaluation>\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAAkS,GAC/T,gEACA", "debugId": null}}, {"offset": {"line": 164, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/New%20folder%20%2825%29/streamzen/src/components/VideoPlayer.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/VideoPlayer.tsx from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/VideoPlayer.tsx\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAA8Q,GAC3S,4CACA", "debugId": null}}, {"offset": {"line": 178, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 188, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/New%20folder%20%2825%29/streamzen/src/components/ContentInfo.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/ContentInfo.tsx <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/ContentInfo.tsx <module evaluation>\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAAkS,GAC/T,gEACA", "debugId": null}}, {"offset": {"line": 202, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/New%20folder%20%2825%29/streamzen/src/components/ContentInfo.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/ContentInfo.tsx from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/ContentInfo.tsx\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAA8Q,GAC3S,4CACA", "debugId": null}}, {"offset": {"line": 216, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 226, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/New%20folder%20%2825%29/streamzen/src/components/EpisodeSelector.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/EpisodeSelector.tsx <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/EpisodeSelector.tsx <module evaluation>\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAAsS,GACnU,oEACA", "debugId": null}}, {"offset": {"line": 240, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/New%20folder%20%2825%29/streamzen/src/components/EpisodeSelector.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/EpisodeSelector.tsx from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/EpisodeSelector.tsx\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAAkR,GAC/S,gDACA", "debugId": null}}, {"offset": {"line": 351, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/New%20folder%20%2825%29/streamzen/src/lib/vidsrc.ts"], "sourcesContent": ["import axios from 'axios';\n\nconst VIDSRC_BASE_URL = process.env.VIDSRC_BASE_URL || 'https://vidsrc.xyz';\n\n// Multiple streaming sources for better reliability\nexport const STREAMING_SOURCES = {\n  vidsrc_xyz: {\n    name: 'VidSrc XYZ',\n    baseUrl: 'https://vidsrc.xyz',\n    quality: 'HD',\n    priority: 1\n  },\n  autoembed: {\n    name: 'AutoEmbed',\n    baseUrl: 'https://player.autoembed.cc',\n    quality: 'Premium HD',\n    priority: 2\n  },\n  vidsrc_icu: {\n    name: 'VidSrc ICU',\n    baseUrl: 'https://vidsrc.icu',\n    quality: 'HD',\n    priority: 3\n  },\n  vidsrc_cc_v2: {\n    name: 'VidSrc CC v2',\n    baseUrl: 'https://vidsrc.cc/v2',\n    quality: 'HD',\n    priority: 4\n  },\n  vidsrc_cc_v3: {\n    name: 'VidSrc CC v3',\n    baseUrl: 'https://vidsrc.cc/v3',\n    quality: 'HD',\n    priority: 5\n  }\n};\n\nexport interface VidSrcMovieData {\n  imdb_id: string;\n  tmdb_id?: string;\n  title: string;\n  embed_url: string;\n  embed_url_tmdb?: string;\n  quality?: string;\n}\n\nexport interface VidSrcSeriesData {\n  imdb_id: string;\n  tmdb_id?: string;\n  show_title: string;\n  embed_url: string;\n  embed_url_tmdb?: string;\n}\n\nexport interface VidSrcEpisodeData {\n  imdb_id: string;\n  tmdb_id?: string;\n  show_title: string;\n  season: string;\n  episode: string;\n  embed_url: string;\n  embed_url_tmdb?: string;\n  quality?: string;\n}\n\nexport interface VidSrcLatestResponse<T> {\n  result: T[];\n}\n\nclass VidSrcAPI {\n  private static instance: VidSrcAPI;\n\n  static getInstance(): VidSrcAPI {\n    if (!VidSrcAPI.instance) {\n      VidSrcAPI.instance = new VidSrcAPI();\n    }\n    return VidSrcAPI.instance;\n  }\n\n  /**\n   * Generate movie embed URLs for all sources\n   */\n  generateAllMovieEmbedUrls(imdbId: string, tmdbId?: string): Array<{\n    source: string;\n    name: string;\n    url: string;\n    quality: string;\n    priority: number;\n  }> {\n    const cleanImdbId = imdbId.replace('tt', '');\n    const urls = [];\n\n    // VidSrc XYZ\n    urls.push({\n      source: 'vidsrc_xyz',\n      name: STREAMING_SOURCES.vidsrc_xyz.name,\n      url: `${STREAMING_SOURCES.vidsrc_xyz.baseUrl}/embed/movie?imdb=${imdbId}`,\n      quality: STREAMING_SOURCES.vidsrc_xyz.quality,\n      priority: STREAMING_SOURCES.vidsrc_xyz.priority\n    });\n\n    // AutoEmbed\n    urls.push({\n      source: 'autoembed',\n      name: STREAMING_SOURCES.autoembed.name,\n      url: `${STREAMING_SOURCES.autoembed.baseUrl}/embed/movie/${imdbId}`,\n      quality: STREAMING_SOURCES.autoembed.quality,\n      priority: STREAMING_SOURCES.autoembed.priority\n    });\n\n    // VidSrc ICU\n    urls.push({\n      source: 'vidsrc_icu',\n      name: STREAMING_SOURCES.vidsrc_icu.name,\n      url: `${STREAMING_SOURCES.vidsrc_icu.baseUrl}/embed/movie/${imdbId}`,\n      quality: STREAMING_SOURCES.vidsrc_icu.quality,\n      priority: STREAMING_SOURCES.vidsrc_icu.priority\n    });\n\n    // VidSrc CC v2\n    urls.push({\n      source: 'vidsrc_cc_v2',\n      name: STREAMING_SOURCES.vidsrc_cc_v2.name,\n      url: `${STREAMING_SOURCES.vidsrc_cc_v2.baseUrl}/embed/movie/${imdbId}`,\n      quality: STREAMING_SOURCES.vidsrc_cc_v2.quality,\n      priority: STREAMING_SOURCES.vidsrc_cc_v2.priority\n    });\n\n    // VidSrc CC v3\n    urls.push({\n      source: 'vidsrc_cc_v3',\n      name: STREAMING_SOURCES.vidsrc_cc_v3.name,\n      url: `${STREAMING_SOURCES.vidsrc_cc_v3.baseUrl}/embed/movie/${imdbId}`,\n      quality: STREAMING_SOURCES.vidsrc_cc_v3.quality,\n      priority: STREAMING_SOURCES.vidsrc_cc_v3.priority\n    });\n\n    return urls.sort((a, b) => a.priority - b.priority);\n  }\n\n  /**\n   * Generate movie embed URL (legacy method for backward compatibility)\n   */\n  generateMovieEmbedUrl(imdbId: string, options?: {\n    tmdbId?: string;\n    subUrl?: string;\n    dsLang?: string;\n    autoplay?: boolean;\n  }): string {\n    const baseUrl = `${VIDSRC_BASE_URL}/embed/movie`;\n    const params = new URLSearchParams();\n\n    if (options?.tmdbId) {\n      params.append('tmdb', options.tmdbId);\n    } else {\n      params.append('imdb', imdbId);\n    }\n\n    if (options?.subUrl) {\n      params.append('sub_url', encodeURIComponent(options.subUrl));\n    }\n\n    if (options?.dsLang) {\n      params.append('ds_lang', options.dsLang);\n    }\n\n    if (options?.autoplay !== undefined) {\n      params.append('autoplay', options.autoplay ? '1' : '0');\n    }\n\n    return `${baseUrl}?${params.toString()}`;\n  }\n\n  /**\n   * Generate episode embed URLs for all sources\n   */\n  generateAllEpisodeEmbedUrls(imdbId: string, season: number, episode: number, tmdbId?: string): Array<{\n    source: string;\n    name: string;\n    url: string;\n    quality: string;\n    priority: number;\n  }> {\n    const cleanImdbId = imdbId.replace('tt', '');\n    const urls = [];\n\n    // VidSrc XYZ\n    urls.push({\n      source: 'vidsrc_xyz',\n      name: STREAMING_SOURCES.vidsrc_xyz.name,\n      url: `${STREAMING_SOURCES.vidsrc_xyz.baseUrl}/embed/tv?imdb=${imdbId}&season=${season}&episode=${episode}`,\n      quality: STREAMING_SOURCES.vidsrc_xyz.quality,\n      priority: STREAMING_SOURCES.vidsrc_xyz.priority\n    });\n\n    // AutoEmbed\n    urls.push({\n      source: 'autoembed',\n      name: STREAMING_SOURCES.autoembed.name,\n      url: `${STREAMING_SOURCES.autoembed.baseUrl}/embed/tv/${imdbId}/${season}/${episode}`,\n      quality: STREAMING_SOURCES.autoembed.quality,\n      priority: STREAMING_SOURCES.autoembed.priority\n    });\n\n    // VidSrc ICU\n    urls.push({\n      source: 'vidsrc_icu',\n      name: STREAMING_SOURCES.vidsrc_icu.name,\n      url: `${STREAMING_SOURCES.vidsrc_icu.baseUrl}/embed/tv/${imdbId}/${season}/${episode}`,\n      quality: STREAMING_SOURCES.vidsrc_icu.quality,\n      priority: STREAMING_SOURCES.vidsrc_icu.priority\n    });\n\n    // VidSrc CC v2\n    urls.push({\n      source: 'vidsrc_cc_v2',\n      name: STREAMING_SOURCES.vidsrc_cc_v2.name,\n      url: `${STREAMING_SOURCES.vidsrc_cc_v2.baseUrl}/embed/tv/${imdbId}/${season}/${episode}`,\n      quality: STREAMING_SOURCES.vidsrc_cc_v2.quality,\n      priority: STREAMING_SOURCES.vidsrc_cc_v2.priority\n    });\n\n    // VidSrc CC v3\n    urls.push({\n      source: 'vidsrc_cc_v3',\n      name: STREAMING_SOURCES.vidsrc_cc_v3.name,\n      url: `${STREAMING_SOURCES.vidsrc_cc_v3.baseUrl}/embed/tv/${imdbId}/${season}/${episode}`,\n      quality: STREAMING_SOURCES.vidsrc_cc_v3.quality,\n      priority: STREAMING_SOURCES.vidsrc_cc_v3.priority\n    });\n\n    return urls.sort((a, b) => a.priority - b.priority);\n  }\n\n  /**\n   * Generate series embed URL (legacy method for backward compatibility)\n   */\n  generateSeriesEmbedUrl(imdbId: string, options?: {\n    tmdbId?: string;\n    dsLang?: string;\n  }): string {\n    const baseUrl = `${VIDSRC_BASE_URL}/embed/tv`;\n    const params = new URLSearchParams();\n\n    if (options?.tmdbId) {\n      params.append('tmdb', options.tmdbId);\n    } else {\n      params.append('imdb', imdbId);\n    }\n\n    if (options?.dsLang) {\n      params.append('ds_lang', options.dsLang);\n    }\n\n    return `${baseUrl}?${params.toString()}`;\n  }\n\n  /**\n   * Generate episode embed URL\n   */\n  generateEpisodeEmbedUrl(imdbId: string, season: number, episode: number, options?: {\n    tmdbId?: string;\n    subUrl?: string;\n    dsLang?: string;\n    autoplay?: boolean;\n    autonext?: boolean;\n  }): string {\n    const baseUrl = `${VIDSRC_BASE_URL}/embed/tv`;\n    const params = new URLSearchParams();\n    \n    if (options?.tmdbId) {\n      params.append('tmdb', options.tmdbId);\n    } else {\n      params.append('imdb', imdbId);\n    }\n    \n    params.append('season', season.toString());\n    params.append('episode', episode.toString());\n    \n    if (options?.subUrl) {\n      params.append('sub_url', encodeURIComponent(options.subUrl));\n    }\n    \n    if (options?.dsLang) {\n      params.append('ds_lang', options.dsLang);\n    }\n    \n    if (options?.autoplay !== undefined) {\n      params.append('autoplay', options.autoplay ? '1' : '0');\n    }\n    \n    if (options?.autonext !== undefined) {\n      params.append('autonext', options.autonext ? '1' : '0');\n    }\n    \n    return `${baseUrl}?${params.toString()}`;\n  }\n\n  /**\n   * Fetch latest movies from VidSrc\n   */\n  async getLatestMovies(page: number = 1): Promise<VidSrcMovieData[]> {\n    try {\n      const url = `${VIDSRC_BASE_URL}/movies/latest/page-${page}.json`;\n      const response = await axios.get<VidSrcLatestResponse<VidSrcMovieData>>(url);\n      return response.data.result || [];\n    } catch (error) {\n      console.error(`Error fetching latest movies from VidSrc (page ${page}):`, error);\n      return [];\n    }\n  }\n\n  /**\n   * Fetch latest TV shows from VidSrc\n   */\n  async getLatestSeries(page: number = 1): Promise<VidSrcSeriesData[]> {\n    try {\n      const url = `${VIDSRC_BASE_URL}/tvshows/latest/page-${page}.json`;\n      const response = await axios.get<VidSrcLatestResponse<VidSrcSeriesData>>(url);\n      return response.data.result || [];\n    } catch (error) {\n      console.error(`Error fetching latest series from VidSrc (page ${page}):`, error);\n      return [];\n    }\n  }\n\n  /**\n   * Fetch latest episodes from VidSrc\n   */\n  async getLatestEpisodes(page: number = 1): Promise<VidSrcEpisodeData[]> {\n    try {\n      const url = `${VIDSRC_BASE_URL}/episodes/latest/page-${page}.json`;\n      const response = await axios.get<VidSrcLatestResponse<VidSrcEpisodeData>>(url);\n      return response.data.result || [];\n    } catch (error) {\n      console.error(`Error fetching latest episodes from VidSrc (page ${page}):`, error);\n      return [];\n    }\n  }\n\n  /**\n   * SIMPLE: Get episodes for a specific series from VidSrc (for embed URLs only)\n   * This is now used only to get streaming links, not for episode discovery\n   */\n  async getSeriesEpisodes(imdbId: string): Promise<Array<{\n    season: number;\n    episode: number;\n    embed_url: string;\n    embed_url_tmdb?: string;\n  }>> {\n    try {\n      console.log(`🔍 ADVANCED: Comprehensive episode search for series: ${imdbId}`);\n\n      const allEpisodes = new Map<string, {\n        season: number;\n        episode: number;\n        embed_url: string;\n        embed_url_tmdb?: string;\n      }>();\n\n      // Strategy 1: Search through ALL latest episodes (not just 15 pages)\n      console.log(`📡 Strategy 1: Scanning latest episodes across all pages...`);\n      let foundEpisodesInLatest = 0;\n      for (let page = 1; page <= 50; page++) { // Increased to 50 pages for comprehensive search\n        try {\n          const episodes = await this.getLatestEpisodes(page);\n          if (episodes.length === 0) break; // No more episodes\n\n          const seriesEpisodes = episodes.filter(episode => episode.imdb_id === imdbId);\n\n          seriesEpisodes.forEach(episode => {\n            const key = `S${episode.season}E${episode.episode}`;\n            if (!allEpisodes.has(key)) {\n              allEpisodes.set(key, {\n                season: episode.season,\n                episode: episode.episode,\n                embed_url: episode.embed_url,\n                embed_url_tmdb: episode.embed_url_tmdb\n              });\n              foundEpisodesInLatest++;\n            }\n          });\n\n          if (seriesEpisodes.length > 0) {\n            console.log(`📺 Page ${page}: Found ${seriesEpisodes.length} episodes`);\n          }\n\n          // If no episodes found in last 10 pages, likely reached the end\n          if (page > 10 && seriesEpisodes.length === 0) {\n            let emptyPages = 0;\n            for (let checkPage = page - 9; checkPage <= page; checkPage++) {\n              const checkEpisodes = await this.getLatestEpisodes(checkPage);\n              if (checkEpisodes.filter(ep => ep.imdb_id === imdbId).length === 0) {\n                emptyPages++;\n              }\n            }\n            if (emptyPages >= 8) break; // Stop if 8/10 recent pages are empty\n          }\n        } catch (error) {\n          console.error(`Error fetching episodes page ${page}:`, error);\n        }\n      }\n\n      console.log(`✅ Strategy 1 Complete: Found ${foundEpisodesInLatest} episodes in latest pages`);\n\n      // Strategy 2: Search through series-specific pages (if available)\n      console.log(`📡 Strategy 2: Searching series-specific endpoints...`);\n      let foundEpisodesInSeries = 0;\n      for (let page = 1; page <= 20; page++) {\n        try {\n          const seriesEpisodes = await this.getLatestSeries(page);\n          const matchingSeries = seriesEpisodes.filter(series => series.imdb_id === imdbId);\n\n          if (matchingSeries.length > 0) {\n            console.log(`📺 Series page ${page}: Found matching series, checking for episode data`);\n            // If series data includes episode information, extract it\n            // This is a placeholder for potential series-specific episode data\n          }\n        } catch (error) {\n          // Series endpoint might not exist, continue\n        }\n      }\n\n      // Strategy 3: Systematic season-by-season verification\n      console.log(`📡 Strategy 3: Systematic season verification...`);\n      const episodesBySeason = new Map<number, number[]>();\n\n      // Group found episodes by season\n      allEpisodes.forEach((episode, key) => {\n        if (!episodesBySeason.has(episode.season)) {\n          episodesBySeason.set(episode.season, []);\n        }\n        episodesBySeason.get(episode.season)!.push(episode.episode);\n      });\n\n      // Analyze each season for gaps and missing episodes\n      for (const [season, episodes] of episodesBySeason) {\n        episodes.sort((a, b) => a - b);\n        const minEp = Math.min(...episodes);\n        const maxEp = Math.max(...episodes);\n        const missingEpisodes: number[] = [];\n\n        for (let ep = 1; ep <= maxEp; ep++) {\n          if (!episodes.includes(ep)) {\n            missingEpisodes.push(ep);\n          }\n        }\n\n        if (missingEpisodes.length > 0) {\n          console.log(`⚠️ Season ${season}: Missing episodes ${missingEpisodes.join(', ')} (Have: ${episodes.join(', ')})`);\n\n          // Strategy 3a: Search for missing episodes specifically\n          console.log(`🔍 Searching for missing episodes in Season ${season}...`);\n          // This would involve more targeted searches if VidSrc had episode-specific endpoints\n        }\n      }\n\n      // Convert Map back to array and sort\n      const uniqueEpisodes = Array.from(allEpisodes.values()).sort((a, b) => {\n        if (a.season !== b.season) return a.season - b.season;\n        return a.episode - b.episode;\n      });\n\n      // Final analysis\n      const totalSeasons = episodesBySeason.size;\n      const totalEpisodes = uniqueEpisodes.length;\n      const seasonRanges = Array.from(episodesBySeason.keys()).sort((a, b) => a - b);\n      const minSeason = seasonRanges[0] || 0;\n      const maxSeason = seasonRanges[seasonRanges.length - 1] || 0;\n\n      console.log(`🎯 COMPREHENSIVE SEARCH COMPLETE:`);\n      console.log(`   📺 Total Episodes Found: ${totalEpisodes}`);\n      console.log(`   🗂️ Seasons Found: ${totalSeasons} (S${minSeason}-S${maxSeason})`);\n      console.log(`   📊 Episodes per Season:`);\n\n      episodesBySeason.forEach((episodes, season) => {\n        episodes.sort((a, b) => a - b);\n        const gaps = [];\n        const maxEp = Math.max(...episodes);\n        for (let ep = 1; ep <= maxEp; ep++) {\n          if (!episodes.includes(ep)) gaps.push(ep);\n        }\n        console.log(`      S${season}: ${episodes.length} episodes (${episodes.join(', ')})${gaps.length > 0 ? ` - Missing: ${gaps.join(', ')}` : ' - Complete'}`);\n      });\n\n      return uniqueEpisodes;\n\n    } catch (error) {\n      console.error(`Error in comprehensive episode search for series ${imdbId}:`, error);\n      return [];\n    }\n  }\n\n  /**\n   * Sync latest content from VidSrc to our database\n   */\n  async syncLatestContent(pages: number = 5): Promise<{\n    movies: VidSrcMovieData[];\n    series: VidSrcSeriesData[];\n    episodes: VidSrcEpisodeData[];\n  }> {\n    const movies: VidSrcMovieData[] = [];\n    const series: VidSrcSeriesData[] = [];\n    const episodes: VidSrcEpisodeData[] = [];\n\n    // Fetch multiple pages in parallel\n    const moviePromises = Array.from({ length: pages }, (_, i) => this.getLatestMovies(i + 1));\n    const seriesPromises = Array.from({ length: pages }, (_, i) => this.getLatestSeries(i + 1));\n    const episodePromises = Array.from({ length: pages }, (_, i) => this.getLatestEpisodes(i + 1));\n\n    try {\n      const [movieResults, seriesResults, episodeResults] = await Promise.all([\n        Promise.all(moviePromises),\n        Promise.all(seriesPromises),\n        Promise.all(episodePromises)\n      ]);\n\n      // Flatten results\n      movieResults.forEach(pageMovies => movies.push(...pageMovies));\n      seriesResults.forEach(pageSeries => series.push(...pageSeries));\n      episodeResults.forEach(pageEpisodes => episodes.push(...pageEpisodes));\n\n      return { movies, series, episodes };\n    } catch (error) {\n      console.error('Error syncing latest content from VidSrc:', error);\n      return { movies, series, episodes };\n    }\n  }\n}\n\nexport default VidSrcAPI;\n"], "names": [], "mappings": ";;;;AAAA;;AAEA,MAAM,kBAAkB,QAAQ,GAAG,CAAC,eAAe,IAAI;AAGhD,MAAM,oBAAoB;IAC/B,YAAY;QACV,MAAM;QACN,SAAS;QACT,SAAS;QACT,UAAU;IACZ;IACA,WAAW;QACT,MAAM;QACN,SAAS;QACT,SAAS;QACT,UAAU;IACZ;IACA,YAAY;QACV,MAAM;QACN,SAAS;QACT,SAAS;QACT,UAAU;IACZ;IACA,cAAc;QACZ,MAAM;QACN,SAAS;QACT,SAAS;QACT,UAAU;IACZ;IACA,cAAc;QACZ,MAAM;QACN,SAAS;QACT,SAAS;QACT,UAAU;IACZ;AACF;AAkCA,MAAM;IACJ,OAAe,SAAoB;IAEnC,OAAO,cAAyB;QAC9B,IAAI,CAAC,UAAU,QAAQ,EAAE;YACvB,UAAU,QAAQ,GAAG,IAAI;QAC3B;QACA,OAAO,UAAU,QAAQ;IAC3B;IAEA;;GAEC,GACD,0BAA0B,MAAc,EAAE,MAAe,EAMtD;QACD,MAAM,cAAc,OAAO,OAAO,CAAC,MAAM;QACzC,MAAM,OAAO,EAAE;QAEf,aAAa;QACb,KAAK,IAAI,CAAC;YACR,QAAQ;YACR,MAAM,kBAAkB,UAAU,CAAC,IAAI;YACvC,KAAK,GAAG,kBAAkB,UAAU,CAAC,OAAO,CAAC,kBAAkB,EAAE,QAAQ;YACzE,SAAS,kBAAkB,UAAU,CAAC,OAAO;YAC7C,UAAU,kBAAkB,UAAU,CAAC,QAAQ;QACjD;QAEA,YAAY;QACZ,KAAK,IAAI,CAAC;YACR,QAAQ;YACR,MAAM,kBAAkB,SAAS,CAAC,IAAI;YACtC,KAAK,GAAG,kBAAkB,SAAS,CAAC,OAAO,CAAC,aAAa,EAAE,QAAQ;YACnE,SAAS,kBAAkB,SAAS,CAAC,OAAO;YAC5C,UAAU,kBAAkB,SAAS,CAAC,QAAQ;QAChD;QAEA,aAAa;QACb,KAAK,IAAI,CAAC;YACR,QAAQ;YACR,MAAM,kBAAkB,UAAU,CAAC,IAAI;YACvC,KAAK,GAAG,kBAAkB,UAAU,CAAC,OAAO,CAAC,aAAa,EAAE,QAAQ;YACpE,SAAS,kBAAkB,UAAU,CAAC,OAAO;YAC7C,UAAU,kBAAkB,UAAU,CAAC,QAAQ;QACjD;QAEA,eAAe;QACf,KAAK,IAAI,CAAC;YACR,QAAQ;YACR,MAAM,kBAAkB,YAAY,CAAC,IAAI;YACzC,KAAK,GAAG,kBAAkB,YAAY,CAAC,OAAO,CAAC,aAAa,EAAE,QAAQ;YACtE,SAAS,kBAAkB,YAAY,CAAC,OAAO;YAC/C,UAAU,kBAAkB,YAAY,CAAC,QAAQ;QACnD;QAEA,eAAe;QACf,KAAK,IAAI,CAAC;YACR,QAAQ;YACR,MAAM,kBAAkB,YAAY,CAAC,IAAI;YACzC,KAAK,GAAG,kBAAkB,YAAY,CAAC,OAAO,CAAC,aAAa,EAAE,QAAQ;YACtE,SAAS,kBAAkB,YAAY,CAAC,OAAO;YAC/C,UAAU,kBAAkB,YAAY,CAAC,QAAQ;QACnD;QAEA,OAAO,KAAK,IAAI,CAAC,CAAC,GAAG,IAAM,EAAE,QAAQ,GAAG,EAAE,QAAQ;IACpD;IAEA;;GAEC,GACD,sBAAsB,MAAc,EAAE,OAKrC,EAAU;QACT,MAAM,UAAU,GAAG,gBAAgB,YAAY,CAAC;QAChD,MAAM,SAAS,IAAI;QAEnB,IAAI,SAAS,QAAQ;YACnB,OAAO,MAAM,CAAC,QAAQ,QAAQ,MAAM;QACtC,OAAO;YACL,OAAO,MAAM,CAAC,QAAQ;QACxB;QAEA,IAAI,SAAS,QAAQ;YACnB,OAAO,MAAM,CAAC,WAAW,mBAAmB,QAAQ,MAAM;QAC5D;QAEA,IAAI,SAAS,QAAQ;YACnB,OAAO,MAAM,CAAC,WAAW,QAAQ,MAAM;QACzC;QAEA,IAAI,SAAS,aAAa,WAAW;YACnC,OAAO,MAAM,CAAC,YAAY,QAAQ,QAAQ,GAAG,MAAM;QACrD;QAEA,OAAO,GAAG,QAAQ,CAAC,EAAE,OAAO,QAAQ,IAAI;IAC1C;IAEA;;GAEC,GACD,4BAA4B,MAAc,EAAE,MAAc,EAAE,OAAe,EAAE,MAAe,EAMzF;QACD,MAAM,cAAc,OAAO,OAAO,CAAC,MAAM;QACzC,MAAM,OAAO,EAAE;QAEf,aAAa;QACb,KAAK,IAAI,CAAC;YACR,QAAQ;YACR,MAAM,kBAAkB,UAAU,CAAC,IAAI;YACvC,KAAK,GAAG,kBAAkB,UAAU,CAAC,OAAO,CAAC,eAAe,EAAE,OAAO,QAAQ,EAAE,OAAO,SAAS,EAAE,SAAS;YAC1G,SAAS,kBAAkB,UAAU,CAAC,OAAO;YAC7C,UAAU,kBAAkB,UAAU,CAAC,QAAQ;QACjD;QAEA,YAAY;QACZ,KAAK,IAAI,CAAC;YACR,QAAQ;YACR,MAAM,kBAAkB,SAAS,CAAC,IAAI;YACtC,KAAK,GAAG,kBAAkB,SAAS,CAAC,OAAO,CAAC,UAAU,EAAE,OAAO,CAAC,EAAE,OAAO,CAAC,EAAE,SAAS;YACrF,SAAS,kBAAkB,SAAS,CAAC,OAAO;YAC5C,UAAU,kBAAkB,SAAS,CAAC,QAAQ;QAChD;QAEA,aAAa;QACb,KAAK,IAAI,CAAC;YACR,QAAQ;YACR,MAAM,kBAAkB,UAAU,CAAC,IAAI;YACvC,KAAK,GAAG,kBAAkB,UAAU,CAAC,OAAO,CAAC,UAAU,EAAE,OAAO,CAAC,EAAE,OAAO,CAAC,EAAE,SAAS;YACtF,SAAS,kBAAkB,UAAU,CAAC,OAAO;YAC7C,UAAU,kBAAkB,UAAU,CAAC,QAAQ;QACjD;QAEA,eAAe;QACf,KAAK,IAAI,CAAC;YACR,QAAQ;YACR,MAAM,kBAAkB,YAAY,CAAC,IAAI;YACzC,KAAK,GAAG,kBAAkB,YAAY,CAAC,OAAO,CAAC,UAAU,EAAE,OAAO,CAAC,EAAE,OAAO,CAAC,EAAE,SAAS;YACxF,SAAS,kBAAkB,YAAY,CAAC,OAAO;YAC/C,UAAU,kBAAkB,YAAY,CAAC,QAAQ;QACnD;QAEA,eAAe;QACf,KAAK,IAAI,CAAC;YACR,QAAQ;YACR,MAAM,kBAAkB,YAAY,CAAC,IAAI;YACzC,KAAK,GAAG,kBAAkB,YAAY,CAAC,OAAO,CAAC,UAAU,EAAE,OAAO,CAAC,EAAE,OAAO,CAAC,EAAE,SAAS;YACxF,SAAS,kBAAkB,YAAY,CAAC,OAAO;YAC/C,UAAU,kBAAkB,YAAY,CAAC,QAAQ;QACnD;QAEA,OAAO,KAAK,IAAI,CAAC,CAAC,GAAG,IAAM,EAAE,QAAQ,GAAG,EAAE,QAAQ;IACpD;IAEA;;GAEC,GACD,uBAAuB,MAAc,EAAE,OAGtC,EAAU;QACT,MAAM,UAAU,GAAG,gBAAgB,SAAS,CAAC;QAC7C,MAAM,SAAS,IAAI;QAEnB,IAAI,SAAS,QAAQ;YACnB,OAAO,MAAM,CAAC,QAAQ,QAAQ,MAAM;QACtC,OAAO;YACL,OAAO,MAAM,CAAC,QAAQ;QACxB;QAEA,IAAI,SAAS,QAAQ;YACnB,OAAO,MAAM,CAAC,WAAW,QAAQ,MAAM;QACzC;QAEA,OAAO,GAAG,QAAQ,CAAC,EAAE,OAAO,QAAQ,IAAI;IAC1C;IAEA;;GAEC,GACD,wBAAwB,MAAc,EAAE,MAAc,EAAE,OAAe,EAAE,OAMxE,EAAU;QACT,MAAM,UAAU,GAAG,gBAAgB,SAAS,CAAC;QAC7C,MAAM,SAAS,IAAI;QAEnB,IAAI,SAAS,QAAQ;YACnB,OAAO,MAAM,CAAC,QAAQ,QAAQ,MAAM;QACtC,OAAO;YACL,OAAO,MAAM,CAAC,QAAQ;QACxB;QAEA,OAAO,MAAM,CAAC,UAAU,OAAO,QAAQ;QACvC,OAAO,MAAM,CAAC,WAAW,QAAQ,QAAQ;QAEzC,IAAI,SAAS,QAAQ;YACnB,OAAO,MAAM,CAAC,WAAW,mBAAmB,QAAQ,MAAM;QAC5D;QAEA,IAAI,SAAS,QAAQ;YACnB,OAAO,MAAM,CAAC,WAAW,QAAQ,MAAM;QACzC;QAEA,IAAI,SAAS,aAAa,WAAW;YACnC,OAAO,MAAM,CAAC,YAAY,QAAQ,QAAQ,GAAG,MAAM;QACrD;QAEA,IAAI,SAAS,aAAa,WAAW;YACnC,OAAO,MAAM,CAAC,YAAY,QAAQ,QAAQ,GAAG,MAAM;QACrD;QAEA,OAAO,GAAG,QAAQ,CAAC,EAAE,OAAO,QAAQ,IAAI;IAC1C;IAEA;;GAEC,GACD,MAAM,gBAAgB,OAAe,CAAC,EAA8B;QAClE,IAAI;YACF,MAAM,MAAM,GAAG,gBAAgB,oBAAoB,EAAE,KAAK,KAAK,CAAC;YAChE,MAAM,WAAW,MAAM,qIAAA,CAAA,UAAK,CAAC,GAAG,CAAwC;YACxE,OAAO,SAAS,IAAI,CAAC,MAAM,IAAI,EAAE;QACnC,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,CAAC,+CAA+C,EAAE,KAAK,EAAE,CAAC,EAAE;YAC1E,OAAO,EAAE;QACX;IACF;IAEA;;GAEC,GACD,MAAM,gBAAgB,OAAe,CAAC,EAA+B;QACnE,IAAI;YACF,MAAM,MAAM,GAAG,gBAAgB,qBAAqB,EAAE,KAAK,KAAK,CAAC;YACjE,MAAM,WAAW,MAAM,qIAAA,CAAA,UAAK,CAAC,GAAG,CAAyC;YACzE,OAAO,SAAS,IAAI,CAAC,MAAM,IAAI,EAAE;QACnC,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,CAAC,+CAA+C,EAAE,KAAK,EAAE,CAAC,EAAE;YAC1E,OAAO,EAAE;QACX;IACF;IAEA;;GAEC,GACD,MAAM,kBAAkB,OAAe,CAAC,EAAgC;QACtE,IAAI;YACF,MAAM,MAAM,GAAG,gBAAgB,sBAAsB,EAAE,KAAK,KAAK,CAAC;YAClE,MAAM,WAAW,MAAM,qIAAA,CAAA,UAAK,CAAC,GAAG,CAA0C;YAC1E,OAAO,SAAS,IAAI,CAAC,MAAM,IAAI,EAAE;QACnC,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,CAAC,iDAAiD,EAAE,KAAK,EAAE,CAAC,EAAE;YAC5E,OAAO,EAAE;QACX;IACF;IAEA;;;GAGC,GACD,MAAM,kBAAkB,MAAc,EAKlC;QACF,IAAI;YACF,QAAQ,GAAG,CAAC,CAAC,sDAAsD,EAAE,QAAQ;YAE7E,MAAM,cAAc,IAAI;YAOxB,qEAAqE;YACrE,QAAQ,GAAG,CAAC,CAAC,2DAA2D,CAAC;YACzE,IAAI,wBAAwB;YAC5B,IAAK,IAAI,OAAO,GAAG,QAAQ,IAAI,OAAQ;gBACrC,IAAI;oBACF,MAAM,WAAW,MAAM,IAAI,CAAC,iBAAiB,CAAC;oBAC9C,IAAI,SAAS,MAAM,KAAK,GAAG,OAAO,mBAAmB;oBAErD,MAAM,iBAAiB,SAAS,MAAM,CAAC,CAAA,UAAW,QAAQ,OAAO,KAAK;oBAEtE,eAAe,OAAO,CAAC,CAAA;wBACrB,MAAM,MAAM,CAAC,CAAC,EAAE,QAAQ,MAAM,CAAC,CAAC,EAAE,QAAQ,OAAO,EAAE;wBACnD,IAAI,CAAC,YAAY,GAAG,CAAC,MAAM;4BACzB,YAAY,GAAG,CAAC,KAAK;gCACnB,QAAQ,QAAQ,MAAM;gCACtB,SAAS,QAAQ,OAAO;gCACxB,WAAW,QAAQ,SAAS;gCAC5B,gBAAgB,QAAQ,cAAc;4BACxC;4BACA;wBACF;oBACF;oBAEA,IAAI,eAAe,MAAM,GAAG,GAAG;wBAC7B,QAAQ,GAAG,CAAC,CAAC,QAAQ,EAAE,KAAK,QAAQ,EAAE,eAAe,MAAM,CAAC,SAAS,CAAC;oBACxE;oBAEA,gEAAgE;oBAChE,IAAI,OAAO,MAAM,eAAe,MAAM,KAAK,GAAG;wBAC5C,IAAI,aAAa;wBACjB,IAAK,IAAI,YAAY,OAAO,GAAG,aAAa,MAAM,YAAa;4BAC7D,MAAM,gBAAgB,MAAM,IAAI,CAAC,iBAAiB,CAAC;4BACnD,IAAI,cAAc,MAAM,CAAC,CAAA,KAAM,GAAG,OAAO,KAAK,QAAQ,MAAM,KAAK,GAAG;gCAClE;4BACF;wBACF;wBACA,IAAI,cAAc,GAAG,OAAO,sCAAsC;oBACpE;gBACF,EAAE,OAAO,OAAO;oBACd,QAAQ,KAAK,CAAC,CAAC,6BAA6B,EAAE,KAAK,CAAC,CAAC,EAAE;gBACzD;YACF;YAEA,QAAQ,GAAG,CAAC,CAAC,6BAA6B,EAAE,sBAAsB,yBAAyB,CAAC;YAE5F,kEAAkE;YAClE,QAAQ,GAAG,CAAC,CAAC,qDAAqD,CAAC;YACnE,IAAI,wBAAwB;YAC5B,IAAK,IAAI,OAAO,GAAG,QAAQ,IAAI,OAAQ;gBACrC,IAAI;oBACF,MAAM,iBAAiB,MAAM,IAAI,CAAC,eAAe,CAAC;oBAClD,MAAM,iBAAiB,eAAe,MAAM,CAAC,CAAA,SAAU,OAAO,OAAO,KAAK;oBAE1E,IAAI,eAAe,MAAM,GAAG,GAAG;wBAC7B,QAAQ,GAAG,CAAC,CAAC,eAAe,EAAE,KAAK,kDAAkD,CAAC;oBACtF,0DAA0D;oBAC1D,mEAAmE;oBACrE;gBACF,EAAE,OAAO,OAAO;gBACd,4CAA4C;gBAC9C;YACF;YAEA,uDAAuD;YACvD,QAAQ,GAAG,CAAC,CAAC,gDAAgD,CAAC;YAC9D,MAAM,mBAAmB,IAAI;YAE7B,iCAAiC;YACjC,YAAY,OAAO,CAAC,CAAC,SAAS;gBAC5B,IAAI,CAAC,iBAAiB,GAAG,CAAC,QAAQ,MAAM,GAAG;oBACzC,iBAAiB,GAAG,CAAC,QAAQ,MAAM,EAAE,EAAE;gBACzC;gBACA,iBAAiB,GAAG,CAAC,QAAQ,MAAM,EAAG,IAAI,CAAC,QAAQ,OAAO;YAC5D;YAEA,oDAAoD;YACpD,KAAK,MAAM,CAAC,QAAQ,SAAS,IAAI,iBAAkB;gBACjD,SAAS,IAAI,CAAC,CAAC,GAAG,IAAM,IAAI;gBAC5B,MAAM,QAAQ,KAAK,GAAG,IAAI;gBAC1B,MAAM,QAAQ,KAAK,GAAG,IAAI;gBAC1B,MAAM,kBAA4B,EAAE;gBAEpC,IAAK,IAAI,KAAK,GAAG,MAAM,OAAO,KAAM;oBAClC,IAAI,CAAC,SAAS,QAAQ,CAAC,KAAK;wBAC1B,gBAAgB,IAAI,CAAC;oBACvB;gBACF;gBAEA,IAAI,gBAAgB,MAAM,GAAG,GAAG;oBAC9B,QAAQ,GAAG,CAAC,CAAC,UAAU,EAAE,OAAO,mBAAmB,EAAE,gBAAgB,IAAI,CAAC,MAAM,QAAQ,EAAE,SAAS,IAAI,CAAC,MAAM,CAAC,CAAC;oBAEhH,wDAAwD;oBACxD,QAAQ,GAAG,CAAC,CAAC,4CAA4C,EAAE,OAAO,GAAG,CAAC;gBACtE,qFAAqF;gBACvF;YACF;YAEA,qCAAqC;YACrC,MAAM,iBAAiB,MAAM,IAAI,CAAC,YAAY,MAAM,IAAI,IAAI,CAAC,CAAC,GAAG;gBAC/D,IAAI,EAAE,MAAM,KAAK,EAAE,MAAM,EAAE,OAAO,EAAE,MAAM,GAAG,EAAE,MAAM;gBACrD,OAAO,EAAE,OAAO,GAAG,EAAE,OAAO;YAC9B;YAEA,iBAAiB;YACjB,MAAM,eAAe,iBAAiB,IAAI;YAC1C,MAAM,gBAAgB,eAAe,MAAM;YAC3C,MAAM,eAAe,MAAM,IAAI,CAAC,iBAAiB,IAAI,IAAI,IAAI,CAAC,CAAC,GAAG,IAAM,IAAI;YAC5E,MAAM,YAAY,YAAY,CAAC,EAAE,IAAI;YACrC,MAAM,YAAY,YAAY,CAAC,aAAa,MAAM,GAAG,EAAE,IAAI;YAE3D,QAAQ,GAAG,CAAC,CAAC,iCAAiC,CAAC;YAC/C,QAAQ,GAAG,CAAC,CAAC,4BAA4B,EAAE,eAAe;YAC1D,QAAQ,GAAG,CAAC,CAAC,sBAAsB,EAAE,aAAa,GAAG,EAAE,UAAU,EAAE,EAAE,UAAU,CAAC,CAAC;YACjF,QAAQ,GAAG,CAAC,CAAC,0BAA0B,CAAC;YAExC,iBAAiB,OAAO,CAAC,CAAC,UAAU;gBAClC,SAAS,IAAI,CAAC,CAAC,GAAG,IAAM,IAAI;gBAC5B,MAAM,OAAO,EAAE;gBACf,MAAM,QAAQ,KAAK,GAAG,IAAI;gBAC1B,IAAK,IAAI,KAAK,GAAG,MAAM,OAAO,KAAM;oBAClC,IAAI,CAAC,SAAS,QAAQ,CAAC,KAAK,KAAK,IAAI,CAAC;gBACxC;gBACA,QAAQ,GAAG,CAAC,CAAC,OAAO,EAAE,OAAO,EAAE,EAAE,SAAS,MAAM,CAAC,WAAW,EAAE,SAAS,IAAI,CAAC,MAAM,CAAC,EAAE,KAAK,MAAM,GAAG,IAAI,CAAC,YAAY,EAAE,KAAK,IAAI,CAAC,OAAO,GAAG,eAAe;YAC3J;YAEA,OAAO;QAET,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,CAAC,iDAAiD,EAAE,OAAO,CAAC,CAAC,EAAE;YAC7E,OAAO,EAAE;QACX;IACF;IAEA;;GAEC,GACD,MAAM,kBAAkB,QAAgB,CAAC,EAItC;QACD,MAAM,SAA4B,EAAE;QACpC,MAAM,SAA6B,EAAE;QACrC,MAAM,WAAgC,EAAE;QAExC,mCAAmC;QACnC,MAAM,gBAAgB,MAAM,IAAI,CAAC;YAAE,QAAQ;QAAM,GAAG,CAAC,GAAG,IAAM,IAAI,CAAC,eAAe,CAAC,IAAI;QACvF,MAAM,iBAAiB,MAAM,IAAI,CAAC;YAAE,QAAQ;QAAM,GAAG,CAAC,GAAG,IAAM,IAAI,CAAC,eAAe,CAAC,IAAI;QACxF,MAAM,kBAAkB,MAAM,IAAI,CAAC;YAAE,QAAQ;QAAM,GAAG,CAAC,GAAG,IAAM,IAAI,CAAC,iBAAiB,CAAC,IAAI;QAE3F,IAAI;YACF,MAAM,CAAC,cAAc,eAAe,eAAe,GAAG,MAAM,QAAQ,GAAG,CAAC;gBACtE,QAAQ,GAAG,CAAC;gBACZ,QAAQ,GAAG,CAAC;gBACZ,QAAQ,GAAG,CAAC;aACb;YAED,kBAAkB;YAClB,aAAa,OAAO,CAAC,CAAA,aAAc,OAAO,IAAI,IAAI;YAClD,cAAc,OAAO,CAAC,CAAA,aAAc,OAAO,IAAI,IAAI;YACnD,eAAe,OAAO,CAAC,CAAA,eAAgB,SAAS,IAAI,IAAI;YAExD,OAAO;gBAAE;gBAAQ;gBAAQ;YAAS;QACpC,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,6CAA6C;YAC3D,OAAO;gBAAE;gBAAQ;gBAAQ;YAAS;QACpC;IACF;AACF;uCAEe", "debugId": null}}, {"offset": {"line": 758, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/New%20folder%20%2825%29/streamzen/src/lib/seo.ts"], "sourcesContent": ["import { Metadata } from 'next';\nimport { IMovie } from '@/models/Movie';\nimport { ISeries } from '@/models/Series';\nimport { IEpisode } from '@/models/Episode';\n\nexport interface SEOConfig {\n  title: string;\n  description: string;\n  keywords?: string[];\n  canonical?: string;\n  ogImage?: string;\n  ogType?: 'website' | 'video.movie' | 'video.tv_show' | 'video.episode';\n  publishedTime?: string;\n  modifiedTime?: string;\n  authors?: string[];\n  section?: string;\n  tags?: string[];\n}\n\nexport class SEOGenerator {\n  private static baseUrl = process.env.NEXT_PUBLIC_BASE_URL || 'https://freemovieswatchnow.com';\n  private static siteName = 'freeMoviesWatchNow';\n  private static defaultDescription = 'Watch free movies and TV series online in HD quality. Stream the latest movies, TV shows, and episodes with multiple streaming sources.';\n\n  static generateMetadata(config: SEOConfig): Metadata {\n    const {\n      title,\n      description,\n      keywords = [],\n      canonical,\n      ogImage,\n      ogType = 'website',\n      publishedTime,\n      modifiedTime,\n      authors = [],\n      section,\n      tags = []\n    } = config;\n\n    const fullTitle = title.includes(this.siteName) ? title : `${title} | ${this.siteName}`;\n    const url = canonical ? `${this.baseUrl}${canonical}` : this.baseUrl;\n    const defaultImage = `${this.baseUrl}/og-default.jpg`;\n\n    return {\n      title: fullTitle,\n      description,\n      keywords: keywords.join(', '),\n      authors: authors.map(name => ({ name })),\n      creator: this.siteName,\n      publisher: this.siteName,\n      formatDetection: {\n        email: false,\n        address: false,\n        telephone: false,\n      },\n      metadataBase: new URL(this.baseUrl),\n      alternates: {\n        canonical: url,\n      },\n      openGraph: {\n        title: fullTitle,\n        description,\n        url,\n        siteName: this.siteName,\n        images: [\n          {\n            url: ogImage || defaultImage,\n            width: 1200,\n            height: 630,\n            alt: title,\n          },\n        ],\n        locale: 'en_US',\n        type: ogType,\n        ...(publishedTime && { publishedTime }),\n        ...(modifiedTime && { modifiedTime }),\n        ...(section && { section }),\n        ...(tags.length > 0 && { tags }),\n      },\n      twitter: {\n        card: 'summary_large_image',\n        title: fullTitle,\n        description,\n        images: [ogImage || defaultImage],\n        creator: '@freemovieswatchnow',\n        site: '@freemovieswatchnow',\n      },\n      robots: {\n        index: true,\n        follow: true,\n        googleBot: {\n          index: true,\n          follow: true,\n          'max-video-preview': -1,\n          'max-image-preview': 'large',\n          'max-snippet': -1,\n        },\n      },\n      verification: {\n        google: process.env.GOOGLE_VERIFICATION_ID,\n        yandex: process.env.YANDEX_VERIFICATION_ID,\n        yahoo: process.env.YAHOO_VERIFICATION_ID,\n      },\n    };\n  }\n\n  static generateMovieMetadata(movie: IMovie): Metadata {\n    const title = `Watch ${movie.title} (${movie.year}) Online Free`;\n    const description = `Watch ${movie.title} (${movie.year}) online free in HD quality. ${movie.description || `Starring ${movie.cast?.slice(0, 3).join(', ') || 'top actors'}. Stream now on ${this.siteName}.`}`;\n    \n    const keywords = [\n      movie.title,\n      `${movie.title} ${movie.year}`,\n      `watch ${movie.title}`,\n      `${movie.title} online`,\n      `${movie.title} free`,\n      'watch movies online',\n      'free movies',\n      'HD movies',\n      ...(movie.genres || []),\n      ...(movie.cast?.slice(0, 5) || []),\n      movie.director,\n      movie.language,\n      movie.country,\n    ].filter(Boolean);\n\n    return this.generateMetadata({\n      title,\n      description,\n      keywords,\n      canonical: `/watch/movie/${movie.imdbId}`,\n      ogImage: movie.posterUrl,\n      ogType: 'video.movie',\n      publishedTime: movie.createdAt ? new Date(movie.createdAt).toISOString() : undefined,\n      modifiedTime: movie.updatedAt ? new Date(movie.updatedAt).toISOString() : undefined,\n      authors: [movie.director].filter(Boolean),\n      section: 'Movies',\n      tags: movie.genres,\n    });\n  }\n\n  static generateSeriesMetadata(series: ISeries): Metadata {\n    const title = `Watch ${series.title} (${series.startYear}${series.endYear ? `-${series.endYear}` : ''}) Online Free`;\n    const description = `Watch ${series.title} TV series online free in HD quality. ${series.description || `${series.totalSeasons} seasons available. Starring ${series.cast?.slice(0, 3).join(', ') || 'top actors'}. Stream all episodes now on ${this.siteName}.`}`;\n    \n    const keywords = [\n      series.title,\n      `${series.title} ${series.startYear}`,\n      `watch ${series.title}`,\n      `${series.title} online`,\n      `${series.title} free`,\n      `${series.title} episodes`,\n      'watch series online',\n      'free TV shows',\n      'HD series',\n      ...(series.genres || []),\n      ...(series.cast?.slice(0, 5) || []),\n      series.language,\n      series.country,\n    ].filter(Boolean);\n\n    return this.generateMetadata({\n      title,\n      description,\n      keywords,\n      canonical: `/watch/series/${series.imdbId}`,\n      ogImage: series.posterUrl,\n      ogType: 'video.tv_show',\n      publishedTime: series.createdAt ? new Date(series.createdAt).toISOString() : undefined,\n      modifiedTime: series.updatedAt ? new Date(series.updatedAt).toISOString() : undefined,\n      section: 'TV Series',\n      tags: series.genres,\n    });\n  }\n\n  static generateEpisodeMetadata(episode: IEpisode, series?: ISeries): Metadata {\n    const episodeTitle = episode.episodeTitle || `Episode ${episode.episode}`;\n    const title = `Watch ${episode.seriesTitle} S${episode.season}E${episode.episode} - ${episodeTitle} Online Free`;\n    const description = `Watch ${episode.seriesTitle} Season ${episode.season} Episode ${episode.episode} \"${episodeTitle}\" online free in HD quality. ${episode.description || `Latest episode from ${episode.seriesTitle}. Stream now on ${this.siteName}.`}`;\n    \n    const keywords = [\n      episode.seriesTitle,\n      `${episode.seriesTitle} S${episode.season}E${episode.episode}`,\n      `${episode.seriesTitle} season ${episode.season}`,\n      `watch ${episode.seriesTitle}`,\n      episodeTitle,\n      'watch episodes online',\n      'free episodes',\n      'HD episodes',\n      ...(episode.genres || series?.genres || []),\n    ].filter(Boolean);\n\n    return this.generateMetadata({\n      title,\n      description,\n      keywords,\n      canonical: `/watch/series/${episode.imdbId}?season=${episode.season}&episode=${episode.episode}`,\n      ogImage: series?.posterUrl,\n      ogType: 'video.episode',\n      publishedTime: episode.createdAt ? new Date(episode.createdAt).toISOString() : undefined,\n      modifiedTime: episode.updatedAt ? new Date(episode.updatedAt).toISOString() : undefined,\n      section: 'Episodes',\n      tags: episode.genres || series?.genres,\n    });\n  }\n\n  static generatePageMetadata(\n    title: string,\n    description: string,\n    path: string,\n    additionalKeywords: string[] = []\n  ): Metadata {\n    const keywords = [\n      'watch movies online',\n      'free movies',\n      'HD movies',\n      'TV series online',\n      'free episodes',\n      'streaming platform',\n      this.siteName,\n      ...additionalKeywords,\n    ];\n\n    return this.generateMetadata({\n      title,\n      description,\n      keywords,\n      canonical: path,\n    });\n  }\n}\n"], "names": [], "mappings": ";;;AAmBO,MAAM;IACX,OAAe,UAAU,QAAQ,GAAG,CAAC,oBAAoB,IAAI,iCAAiC;IAC9F,OAAe,WAAW,qBAAqB;IAC/C,OAAe,qBAAqB,0IAA0I;IAE9K,OAAO,iBAAiB,MAAiB,EAAY;QACnD,MAAM,EACJ,KAAK,EACL,WAAW,EACX,WAAW,EAAE,EACb,SAAS,EACT,OAAO,EACP,SAAS,SAAS,EAClB,aAAa,EACb,YAAY,EACZ,UAAU,EAAE,EACZ,OAAO,EACP,OAAO,EAAE,EACV,GAAG;QAEJ,MAAM,YAAY,MAAM,QAAQ,CAAC,IAAI,CAAC,QAAQ,IAAI,QAAQ,GAAG,MAAM,GAAG,EAAE,IAAI,CAAC,QAAQ,EAAE;QACvF,MAAM,MAAM,YAAY,GAAG,IAAI,CAAC,OAAO,GAAG,WAAW,GAAG,IAAI,CAAC,OAAO;QACpE,MAAM,eAAe,GAAG,IAAI,CAAC,OAAO,CAAC,eAAe,CAAC;QAErD,OAAO;YACL,OAAO;YACP;YACA,UAAU,SAAS,IAAI,CAAC;YACxB,SAAS,QAAQ,GAAG,CAAC,CAAA,OAAQ,CAAC;oBAAE;gBAAK,CAAC;YACtC,SAAS,IAAI,CAAC,QAAQ;YACtB,WAAW,IAAI,CAAC,QAAQ;YACxB,iBAAiB;gBACf,OAAO;gBACP,SAAS;gBACT,WAAW;YACb;YACA,cAAc,IAAI,IAAI,IAAI,CAAC,OAAO;YAClC,YAAY;gBACV,WAAW;YACb;YACA,WAAW;gBACT,OAAO;gBACP;gBACA;gBACA,UAAU,IAAI,CAAC,QAAQ;gBACvB,QAAQ;oBACN;wBACE,KAAK,WAAW;wBAChB,OAAO;wBACP,QAAQ;wBACR,KAAK;oBACP;iBACD;gBACD,QAAQ;gBACR,MAAM;gBACN,GAAI,iBAAiB;oBAAE;gBAAc,CAAC;gBACtC,GAAI,gBAAgB;oBAAE;gBAAa,CAAC;gBACpC,GAAI,WAAW;oBAAE;gBAAQ,CAAC;gBAC1B,GAAI,KAAK,MAAM,GAAG,KAAK;oBAAE;gBAAK,CAAC;YACjC;YACA,SAAS;gBACP,MAAM;gBACN,OAAO;gBACP;gBACA,QAAQ;oBAAC,WAAW;iBAAa;gBACjC,SAAS;gBACT,MAAM;YACR;YACA,QAAQ;gBACN,OAAO;gBACP,QAAQ;gBACR,WAAW;oBACT,OAAO;oBACP,QAAQ;oBACR,qBAAqB,CAAC;oBACtB,qBAAqB;oBACrB,eAAe,CAAC;gBAClB;YACF;YACA,cAAc;gBACZ,QAAQ,QAAQ,GAAG,CAAC,sBAAsB;gBAC1C,QAAQ,QAAQ,GAAG,CAAC,sBAAsB;gBAC1C,OAAO,QAAQ,GAAG,CAAC,qBAAqB;YAC1C;QACF;IACF;IAEA,OAAO,sBAAsB,KAAa,EAAY;QACpD,MAAM,QAAQ,CAAC,MAAM,EAAE,MAAM,KAAK,CAAC,EAAE,EAAE,MAAM,IAAI,CAAC,aAAa,CAAC;QAChE,MAAM,cAAc,CAAC,MAAM,EAAE,MAAM,KAAK,CAAC,EAAE,EAAE,MAAM,IAAI,CAAC,6BAA6B,EAAE,MAAM,WAAW,IAAI,CAAC,SAAS,EAAE,MAAM,IAAI,EAAE,MAAM,GAAG,GAAG,KAAK,SAAS,aAAa,gBAAgB,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,EAAE;QAE/M,MAAM,WAAW;YACf,MAAM,KAAK;YACX,GAAG,MAAM,KAAK,CAAC,CAAC,EAAE,MAAM,IAAI,EAAE;YAC9B,CAAC,MAAM,EAAE,MAAM,KAAK,EAAE;YACtB,GAAG,MAAM,KAAK,CAAC,OAAO,CAAC;YACvB,GAAG,MAAM,KAAK,CAAC,KAAK,CAAC;YACrB;YACA;YACA;eACI,MAAM,MAAM,IAAI,EAAE;eAClB,MAAM,IAAI,EAAE,MAAM,GAAG,MAAM,EAAE;YACjC,MAAM,QAAQ;YACd,MAAM,QAAQ;YACd,MAAM,OAAO;SACd,CAAC,MAAM,CAAC;QAET,OAAO,IAAI,CAAC,gBAAgB,CAAC;YAC3B;YACA;YACA;YACA,WAAW,CAAC,aAAa,EAAE,MAAM,MAAM,EAAE;YACzC,SAAS,MAAM,SAAS;YACxB,QAAQ;YACR,eAAe,MAAM,SAAS,GAAG,IAAI,KAAK,MAAM,SAAS,EAAE,WAAW,KAAK;YAC3E,cAAc,MAAM,SAAS,GAAG,IAAI,KAAK,MAAM,SAAS,EAAE,WAAW,KAAK;YAC1E,SAAS;gBAAC,MAAM,QAAQ;aAAC,CAAC,MAAM,CAAC;YACjC,SAAS;YACT,MAAM,MAAM,MAAM;QACpB;IACF;IAEA,OAAO,uBAAuB,MAAe,EAAY;QACvD,MAAM,QAAQ,CAAC,MAAM,EAAE,OAAO,KAAK,CAAC,EAAE,EAAE,OAAO,SAAS,GAAG,OAAO,OAAO,GAAG,CAAC,CAAC,EAAE,OAAO,OAAO,EAAE,GAAG,GAAG,aAAa,CAAC;QACpH,MAAM,cAAc,CAAC,MAAM,EAAE,OAAO,KAAK,CAAC,sCAAsC,EAAE,OAAO,WAAW,IAAI,GAAG,OAAO,YAAY,CAAC,6BAA6B,EAAE,OAAO,IAAI,EAAE,MAAM,GAAG,GAAG,KAAK,SAAS,aAAa,6BAA6B,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,EAAE;QAEnQ,MAAM,WAAW;YACf,OAAO,KAAK;YACZ,GAAG,OAAO,KAAK,CAAC,CAAC,EAAE,OAAO,SAAS,EAAE;YACrC,CAAC,MAAM,EAAE,OAAO,KAAK,EAAE;YACvB,GAAG,OAAO,KAAK,CAAC,OAAO,CAAC;YACxB,GAAG,OAAO,KAAK,CAAC,KAAK,CAAC;YACtB,GAAG,OAAO,KAAK,CAAC,SAAS,CAAC;YAC1B;YACA;YACA;eACI,OAAO,MAAM,IAAI,EAAE;eACnB,OAAO,IAAI,EAAE,MAAM,GAAG,MAAM,EAAE;YAClC,OAAO,QAAQ;YACf,OAAO,OAAO;SACf,CAAC,MAAM,CAAC;QAET,OAAO,IAAI,CAAC,gBAAgB,CAAC;YAC3B;YACA;YACA;YACA,WAAW,CAAC,cAAc,EAAE,OAAO,MAAM,EAAE;YAC3C,SAAS,OAAO,SAAS;YACzB,QAAQ;YACR,eAAe,OAAO,SAAS,GAAG,IAAI,KAAK,OAAO,SAAS,EAAE,WAAW,KAAK;YAC7E,cAAc,OAAO,SAAS,GAAG,IAAI,KAAK,OAAO,SAAS,EAAE,WAAW,KAAK;YAC5E,SAAS;YACT,MAAM,OAAO,MAAM;QACrB;IACF;IAEA,OAAO,wBAAwB,OAAiB,EAAE,MAAgB,EAAY;QAC5E,MAAM,eAAe,QAAQ,YAAY,IAAI,CAAC,QAAQ,EAAE,QAAQ,OAAO,EAAE;QACzE,MAAM,QAAQ,CAAC,MAAM,EAAE,QAAQ,WAAW,CAAC,EAAE,EAAE,QAAQ,MAAM,CAAC,CAAC,EAAE,QAAQ,OAAO,CAAC,GAAG,EAAE,aAAa,YAAY,CAAC;QAChH,MAAM,cAAc,CAAC,MAAM,EAAE,QAAQ,WAAW,CAAC,QAAQ,EAAE,QAAQ,MAAM,CAAC,SAAS,EAAE,QAAQ,OAAO,CAAC,EAAE,EAAE,aAAa,6BAA6B,EAAE,QAAQ,WAAW,IAAI,CAAC,oBAAoB,EAAE,QAAQ,WAAW,CAAC,gBAAgB,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,EAAE;QAE3P,MAAM,WAAW;YACf,QAAQ,WAAW;YACnB,GAAG,QAAQ,WAAW,CAAC,EAAE,EAAE,QAAQ,MAAM,CAAC,CAAC,EAAE,QAAQ,OAAO,EAAE;YAC9D,GAAG,QAAQ,WAAW,CAAC,QAAQ,EAAE,QAAQ,MAAM,EAAE;YACjD,CAAC,MAAM,EAAE,QAAQ,WAAW,EAAE;YAC9B;YACA;YACA;YACA;eACI,QAAQ,MAAM,IAAI,QAAQ,UAAU,EAAE;SAC3C,CAAC,MAAM,CAAC;QAET,OAAO,IAAI,CAAC,gBAAgB,CAAC;YAC3B;YACA;YACA;YACA,WAAW,CAAC,cAAc,EAAE,QAAQ,MAAM,CAAC,QAAQ,EAAE,QAAQ,MAAM,CAAC,SAAS,EAAE,QAAQ,OAAO,EAAE;YAChG,SAAS,QAAQ;YACjB,QAAQ;YACR,eAAe,QAAQ,SAAS,GAAG,IAAI,KAAK,QAAQ,SAAS,EAAE,WAAW,KAAK;YAC/E,cAAc,QAAQ,SAAS,GAAG,IAAI,KAAK,QAAQ,SAAS,EAAE,WAAW,KAAK;YAC9E,SAAS;YACT,MAAM,QAAQ,MAAM,IAAI,QAAQ;QAClC;IACF;IAEA,OAAO,qBACL,KAAa,EACb,WAAmB,EACnB,IAAY,EACZ,qBAA+B,EAAE,EACvB;QACV,MAAM,WAAW;YACf;YACA;YACA;YACA;YACA;YACA;YACA,IAAI,CAAC,QAAQ;eACV;SACJ;QAED,OAAO,IAAI,CAAC,gBAAgB,CAAC;YAC3B;YACA;YACA;YACA,WAAW;QACb;IACF;AACF", "debugId": null}}, {"offset": {"line": 962, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/New%20folder%20%2825%29/streamzen/src/lib/schema.ts"], "sourcesContent": ["import { IMovie } from '@/models/Movie';\nimport { ISeries } from '@/models/Series';\nimport { IEpisode } from '@/models/Episode';\n\nexport interface SchemaMarkup {\n  '@context': string;\n  '@type': string;\n  [key: string]: any;\n}\n\nexport class SchemaGenerator {\n  private static baseUrl = process.env.NEXT_PUBLIC_BASE_URL || 'https://freemovieswatchnow.com';\n  private static siteName = 'freeMoviesWatchNow';\n\n  static generateWebsiteSchema(): SchemaMarkup {\n    return {\n      '@context': 'https://schema.org',\n      '@type': 'WebSite',\n      name: this.siteName,\n      url: this.baseUrl,\n      description: 'Watch free movies and TV series online in HD quality. Stream the latest movies, TV shows, and episodes with multiple streaming sources.',\n      potentialAction: {\n        '@type': 'SearchAction',\n        target: {\n          '@type': 'EntryPoint',\n          urlTemplate: `${this.baseUrl}/search?q={search_term_string}`,\n        },\n        'query-input': 'required name=search_term_string',\n      },\n      publisher: {\n        '@type': 'Organization',\n        name: this.siteName,\n        url: this.baseUrl,\n        logo: {\n          '@type': 'ImageObject',\n          url: `${this.baseUrl}/logo.png`,\n        },\n      },\n    };\n  }\n\n  static generateMovieSchema(movie: IMovie): SchemaMarkup {\n    const schema: SchemaMarkup = {\n      '@context': 'https://schema.org',\n      '@type': 'Movie',\n      name: movie.title,\n      url: `${this.baseUrl}/watch/movie/${movie.imdbId}`,\n      description: movie.description,\n      image: movie.posterUrl,\n      datePublished: movie.year?.toString(),\n      genre: movie.genres,\n      duration: movie.runtime,\n      contentRating: movie.rating,\n      aggregateRating: movie.imdbRating ? {\n        '@type': 'AggregateRating',\n        ratingValue: movie.imdbRating,\n        ratingCount: movie.imdbVotes?.replace(/,/g, '') || '1000',\n        bestRating: '10',\n        worstRating: '1',\n      } : undefined,\n      director: movie.director ? {\n        '@type': 'Person',\n        name: movie.director,\n      } : undefined,\n      actor: movie.cast?.slice(0, 5).map(actor => ({\n        '@type': 'Person',\n        name: actor,\n      })),\n      productionCompany: {\n        '@type': 'Organization',\n        name: movie.country || 'Unknown',\n      },\n      inLanguage: movie.language,\n      keywords: [\n        movie.title,\n        `${movie.title} ${movie.year}`,\n        'watch online',\n        'free movie',\n        'HD movie',\n        ...(movie.genres || []),\n      ].join(', '),\n      offers: {\n        '@type': 'Offer',\n        price: '0',\n        priceCurrency: 'USD',\n        availability: 'https://schema.org/InStock',\n        url: `${this.baseUrl}/watch/movie/${movie.imdbId}`,\n      },\n      potentialAction: {\n        '@type': 'WatchAction',\n        target: {\n          '@type': 'EntryPoint',\n          urlTemplate: `${this.baseUrl}/watch/movie/${movie.imdbId}`,\n          actionPlatform: [\n            'https://schema.org/DesktopWebPlatform',\n            'https://schema.org/MobileWebPlatform',\n          ],\n        },\n        expectsAcceptanceOf: {\n          '@type': 'Offer',\n          price: '0',\n          priceCurrency: 'USD',\n          eligibleRegion: {\n            '@type': 'Country',\n            name: 'US',\n          },\n        },\n      },\n    };\n\n    // Remove undefined values\n    return JSON.parse(JSON.stringify(schema));\n  }\n\n  static generateSeriesSchema(series: ISeries): SchemaMarkup {\n    const schema: SchemaMarkup = {\n      '@context': 'https://schema.org',\n      '@type': 'TVSeries',\n      name: series.title,\n      url: `${this.baseUrl}/watch/series/${series.imdbId}`,\n      description: series.description,\n      image: series.posterUrl,\n      startDate: series.startYear?.toString(),\n      endDate: series.endYear?.toString(),\n      numberOfSeasons: series.totalSeasons,\n      numberOfEpisodes: series.totalEpisodes,\n      genre: series.genres,\n      contentRating: series.rating,\n      aggregateRating: series.imdbRating ? {\n        '@type': 'AggregateRating',\n        ratingValue: series.imdbRating,\n        ratingCount: series.imdbVotes?.replace(/,/g, '') || '1000',\n        bestRating: '10',\n        worstRating: '1',\n      } : undefined,\n      actor: series.cast?.slice(0, 5).map(actor => ({\n        '@type': 'Person',\n        name: actor,\n      })),\n      productionCompany: {\n        '@type': 'Organization',\n        name: series.country || 'Unknown',\n      },\n      inLanguage: series.language,\n      keywords: [\n        series.title,\n        `${series.title} ${series.startYear}`,\n        'watch online',\n        'free series',\n        'HD series',\n        'TV show',\n        ...(series.genres || []),\n      ].join(', '),\n      offers: {\n        '@type': 'Offer',\n        price: '0',\n        priceCurrency: 'USD',\n        availability: 'https://schema.org/InStock',\n        url: `${this.baseUrl}/watch/series/${series.imdbId}`,\n      },\n      potentialAction: {\n        '@type': 'WatchAction',\n        target: {\n          '@type': 'EntryPoint',\n          urlTemplate: `${this.baseUrl}/watch/series/${series.imdbId}`,\n          actionPlatform: [\n            'https://schema.org/DesktopWebPlatform',\n            'https://schema.org/MobileWebPlatform',\n          ],\n        },\n        expectsAcceptanceOf: {\n          '@type': 'Offer',\n          price: '0',\n          priceCurrency: 'USD',\n          eligibleRegion: {\n            '@type': 'Country',\n            name: 'US',\n          },\n        },\n      },\n    };\n\n    return JSON.parse(JSON.stringify(schema));\n  }\n\n  static generateEpisodeSchema(episode: IEpisode, series?: ISeries): SchemaMarkup {\n    const episodeTitle = episode.episodeTitle || `Episode ${episode.episode}`;\n    \n    const schema: SchemaMarkup = {\n      '@context': 'https://schema.org',\n      '@type': 'TVEpisode',\n      name: episodeTitle,\n      episodeNumber: episode.episode,\n      seasonNumber: episode.season,\n      url: `${this.baseUrl}/watch/series/${episode.imdbId}?season=${episode.season}&episode=${episode.episode}`,\n      description: episode.description || `${episode.seriesTitle} Season ${episode.season} Episode ${episode.episode}`,\n      image: series?.posterUrl,\n      datePublished: episode.airDate ? new Date(episode.airDate).toISOString() : undefined,\n      duration: episode.runtime,\n      partOfSeries: {\n        '@type': 'TVSeries',\n        name: episode.seriesTitle,\n        url: `${this.baseUrl}/watch/series/${episode.imdbId}`,\n      },\n      partOfSeason: {\n        '@type': 'TVSeason',\n        seasonNumber: episode.season,\n        partOfSeries: {\n          '@type': 'TVSeries',\n          name: episode.seriesTitle,\n        },\n      },\n      aggregateRating: episode.imdbRating ? {\n        '@type': 'AggregateRating',\n        ratingValue: episode.imdbRating,\n        bestRating: '10',\n        worstRating: '1',\n      } : undefined,\n      genre: episode.genres || series?.genres,\n      inLanguage: series?.language,\n      keywords: [\n        episode.seriesTitle,\n        episodeTitle,\n        `S${episode.season}E${episode.episode}`,\n        'watch online',\n        'free episode',\n        'HD episode',\n        ...(episode.genres || series?.genres || []),\n      ].join(', '),\n      offers: {\n        '@type': 'Offer',\n        price: '0',\n        priceCurrency: 'USD',\n        availability: 'https://schema.org/InStock',\n        url: `${this.baseUrl}/watch/series/${episode.imdbId}?season=${episode.season}&episode=${episode.episode}`,\n      },\n      potentialAction: {\n        '@type': 'WatchAction',\n        target: {\n          '@type': 'EntryPoint',\n          urlTemplate: `${this.baseUrl}/watch/series/${episode.imdbId}?season=${episode.season}&episode=${episode.episode}`,\n          actionPlatform: [\n            'https://schema.org/DesktopWebPlatform',\n            'https://schema.org/MobileWebPlatform',\n          ],\n        },\n      },\n    };\n\n    return JSON.parse(JSON.stringify(schema));\n  }\n\n  static generateBreadcrumbSchema(items: Array<{ name: string; url: string }>): SchemaMarkup {\n    return {\n      '@context': 'https://schema.org',\n      '@type': 'BreadcrumbList',\n      itemListElement: items.map((item, index) => ({\n        '@type': 'ListItem',\n        position: index + 1,\n        name: item.name,\n        item: `${this.baseUrl}${item.url}`,\n      })),\n    };\n  }\n\n  static generateCollectionPageSchema(\n    name: string,\n    description: string,\n    url: string,\n    items: Array<{ name: string; url: string; image?: string }>\n  ): SchemaMarkup {\n    return {\n      '@context': 'https://schema.org',\n      '@type': 'CollectionPage',\n      name,\n      description,\n      url: `${this.baseUrl}${url}`,\n      mainEntity: {\n        '@type': 'ItemList',\n        numberOfItems: items.length,\n        itemListElement: items.map((item, index) => ({\n          '@type': 'ListItem',\n          position: index + 1,\n          url: `${this.baseUrl}${item.url}`,\n          name: item.name,\n          ...(item.image && { image: item.image }),\n        })),\n      },\n    };\n  }\n}\n"], "names": [], "mappings": ";;;AAUO,MAAM;IACX,OAAe,UAAU,QAAQ,GAAG,CAAC,oBAAoB,IAAI,iCAAiC;IAC9F,OAAe,WAAW,qBAAqB;IAE/C,OAAO,wBAAsC;QAC3C,OAAO;YACL,YAAY;YACZ,SAAS;YACT,MAAM,IAAI,CAAC,QAAQ;YACnB,KAAK,IAAI,CAAC,OAAO;YACjB,aAAa;YACb,iBAAiB;gBACf,SAAS;gBACT,QAAQ;oBACN,SAAS;oBACT,aAAa,GAAG,IAAI,CAAC,OAAO,CAAC,8BAA8B,CAAC;gBAC9D;gBACA,eAAe;YACjB;YACA,WAAW;gBACT,SAAS;gBACT,MAAM,IAAI,CAAC,QAAQ;gBACnB,KAAK,IAAI,CAAC,OAAO;gBACjB,MAAM;oBACJ,SAAS;oBACT,KAAK,GAAG,IAAI,CAAC,OAAO,CAAC,SAAS,CAAC;gBACjC;YACF;QACF;IACF;IAEA,OAAO,oBAAoB,KAAa,EAAgB;QACtD,MAAM,SAAuB;YAC3B,YAAY;YACZ,SAAS;YACT,MAAM,MAAM,KAAK;YACjB,KAAK,GAAG,IAAI,CAAC,OAAO,CAAC,aAAa,EAAE,MAAM,MAAM,EAAE;YAClD,aAAa,MAAM,WAAW;YAC9B,OAAO,MAAM,SAAS;YACtB,eAAe,MAAM,IAAI,EAAE;YAC3B,OAAO,MAAM,MAAM;YACnB,UAAU,MAAM,OAAO;YACvB,eAAe,MAAM,MAAM;YAC3B,iBAAiB,MAAM,UAAU,GAAG;gBAClC,SAAS;gBACT,aAAa,MAAM,UAAU;gBAC7B,aAAa,MAAM,SAAS,EAAE,QAAQ,MAAM,OAAO;gBACnD,YAAY;gBACZ,aAAa;YACf,IAAI;YACJ,UAAU,MAAM,QAAQ,GAAG;gBACzB,SAAS;gBACT,MAAM,MAAM,QAAQ;YACtB,IAAI;YACJ,OAAO,MAAM,IAAI,EAAE,MAAM,GAAG,GAAG,IAAI,CAAA,QAAS,CAAC;oBAC3C,SAAS;oBACT,MAAM;gBACR,CAAC;YACD,mBAAmB;gBACjB,SAAS;gBACT,MAAM,MAAM,OAAO,IAAI;YACzB;YACA,YAAY,MAAM,QAAQ;YAC1B,UAAU;gBACR,MAAM,KAAK;gBACX,GAAG,MAAM,KAAK,CAAC,CAAC,EAAE,MAAM,IAAI,EAAE;gBAC9B;gBACA;gBACA;mBACI,MAAM,MAAM,IAAI,EAAE;aACvB,CAAC,IAAI,CAAC;YACP,QAAQ;gBACN,SAAS;gBACT,OAAO;gBACP,eAAe;gBACf,cAAc;gBACd,KAAK,GAAG,IAAI,CAAC,OAAO,CAAC,aAAa,EAAE,MAAM,MAAM,EAAE;YACpD;YACA,iBAAiB;gBACf,SAAS;gBACT,QAAQ;oBACN,SAAS;oBACT,aAAa,GAAG,IAAI,CAAC,OAAO,CAAC,aAAa,EAAE,MAAM,MAAM,EAAE;oBAC1D,gBAAgB;wBACd;wBACA;qBACD;gBACH;gBACA,qBAAqB;oBACnB,SAAS;oBACT,OAAO;oBACP,eAAe;oBACf,gBAAgB;wBACd,SAAS;wBACT,MAAM;oBACR;gBACF;YACF;QACF;QAEA,0BAA0B;QAC1B,OAAO,KAAK,KAAK,CAAC,KAAK,SAAS,CAAC;IACnC;IAEA,OAAO,qBAAqB,MAAe,EAAgB;QACzD,MAAM,SAAuB;YAC3B,YAAY;YACZ,SAAS;YACT,MAAM,OAAO,KAAK;YAClB,KAAK,GAAG,IAAI,CAAC,OAAO,CAAC,cAAc,EAAE,OAAO,MAAM,EAAE;YACpD,aAAa,OAAO,WAAW;YAC/B,OAAO,OAAO,SAAS;YACvB,WAAW,OAAO,SAAS,EAAE;YAC7B,SAAS,OAAO,OAAO,EAAE;YACzB,iBAAiB,OAAO,YAAY;YACpC,kBAAkB,OAAO,aAAa;YACtC,OAAO,OAAO,MAAM;YACpB,eAAe,OAAO,MAAM;YAC5B,iBAAiB,OAAO,UAAU,GAAG;gBACnC,SAAS;gBACT,aAAa,OAAO,UAAU;gBAC9B,aAAa,OAAO,SAAS,EAAE,QAAQ,MAAM,OAAO;gBACpD,YAAY;gBACZ,aAAa;YACf,IAAI;YACJ,OAAO,OAAO,IAAI,EAAE,MAAM,GAAG,GAAG,IAAI,CAAA,QAAS,CAAC;oBAC5C,SAAS;oBACT,MAAM;gBACR,CAAC;YACD,mBAAmB;gBACjB,SAAS;gBACT,MAAM,OAAO,OAAO,IAAI;YAC1B;YACA,YAAY,OAAO,QAAQ;YAC3B,UAAU;gBACR,OAAO,KAAK;gBACZ,GAAG,OAAO,KAAK,CAAC,CAAC,EAAE,OAAO,SAAS,EAAE;gBACrC;gBACA;gBACA;gBACA;mBACI,OAAO,MAAM,IAAI,EAAE;aACxB,CAAC,IAAI,CAAC;YACP,QAAQ;gBACN,SAAS;gBACT,OAAO;gBACP,eAAe;gBACf,cAAc;gBACd,KAAK,GAAG,IAAI,CAAC,OAAO,CAAC,cAAc,EAAE,OAAO,MAAM,EAAE;YACtD;YACA,iBAAiB;gBACf,SAAS;gBACT,QAAQ;oBACN,SAAS;oBACT,aAAa,GAAG,IAAI,CAAC,OAAO,CAAC,cAAc,EAAE,OAAO,MAAM,EAAE;oBAC5D,gBAAgB;wBACd;wBACA;qBACD;gBACH;gBACA,qBAAqB;oBACnB,SAAS;oBACT,OAAO;oBACP,eAAe;oBACf,gBAAgB;wBACd,SAAS;wBACT,MAAM;oBACR;gBACF;YACF;QACF;QAEA,OAAO,KAAK,KAAK,CAAC,KAAK,SAAS,CAAC;IACnC;IAEA,OAAO,sBAAsB,OAAiB,EAAE,MAAgB,EAAgB;QAC9E,MAAM,eAAe,QAAQ,YAAY,IAAI,CAAC,QAAQ,EAAE,QAAQ,OAAO,EAAE;QAEzE,MAAM,SAAuB;YAC3B,YAAY;YACZ,SAAS;YACT,MAAM;YACN,eAAe,QAAQ,OAAO;YAC9B,cAAc,QAAQ,MAAM;YAC5B,KAAK,GAAG,IAAI,CAAC,OAAO,CAAC,cAAc,EAAE,QAAQ,MAAM,CAAC,QAAQ,EAAE,QAAQ,MAAM,CAAC,SAAS,EAAE,QAAQ,OAAO,EAAE;YACzG,aAAa,QAAQ,WAAW,IAAI,GAAG,QAAQ,WAAW,CAAC,QAAQ,EAAE,QAAQ,MAAM,CAAC,SAAS,EAAE,QAAQ,OAAO,EAAE;YAChH,OAAO,QAAQ;YACf,eAAe,QAAQ,OAAO,GAAG,IAAI,KAAK,QAAQ,OAAO,EAAE,WAAW,KAAK;YAC3E,UAAU,QAAQ,OAAO;YACzB,cAAc;gBACZ,SAAS;gBACT,MAAM,QAAQ,WAAW;gBACzB,KAAK,GAAG,IAAI,CAAC,OAAO,CAAC,cAAc,EAAE,QAAQ,MAAM,EAAE;YACvD;YACA,cAAc;gBACZ,SAAS;gBACT,cAAc,QAAQ,MAAM;gBAC5B,cAAc;oBACZ,SAAS;oBACT,MAAM,QAAQ,WAAW;gBAC3B;YACF;YACA,iBAAiB,QAAQ,UAAU,GAAG;gBACpC,SAAS;gBACT,aAAa,QAAQ,UAAU;gBAC/B,YAAY;gBACZ,aAAa;YACf,IAAI;YACJ,OAAO,QAAQ,MAAM,IAAI,QAAQ;YACjC,YAAY,QAAQ;YACpB,UAAU;gBACR,QAAQ,WAAW;gBACnB;gBACA,CAAC,CAAC,EAAE,QAAQ,MAAM,CAAC,CAAC,EAAE,QAAQ,OAAO,EAAE;gBACvC;gBACA;gBACA;mBACI,QAAQ,MAAM,IAAI,QAAQ,UAAU,EAAE;aAC3C,CAAC,IAAI,CAAC;YACP,QAAQ;gBACN,SAAS;gBACT,OAAO;gBACP,eAAe;gBACf,cAAc;gBACd,KAAK,GAAG,IAAI,CAAC,OAAO,CAAC,cAAc,EAAE,QAAQ,MAAM,CAAC,QAAQ,EAAE,QAAQ,MAAM,CAAC,SAAS,EAAE,QAAQ,OAAO,EAAE;YAC3G;YACA,iBAAiB;gBACf,SAAS;gBACT,QAAQ;oBACN,SAAS;oBACT,aAAa,GAAG,IAAI,CAAC,OAAO,CAAC,cAAc,EAAE,QAAQ,MAAM,CAAC,QAAQ,EAAE,QAAQ,MAAM,CAAC,SAAS,EAAE,QAAQ,OAAO,EAAE;oBACjH,gBAAgB;wBACd;wBACA;qBACD;gBACH;YACF;QACF;QAEA,OAAO,KAAK,KAAK,CAAC,KAAK,SAAS,CAAC;IACnC;IAEA,OAAO,yBAAyB,KAA2C,EAAgB;QACzF,OAAO;YACL,YAAY;YACZ,SAAS;YACT,iBAAiB,MAAM,GAAG,CAAC,CAAC,MAAM,QAAU,CAAC;oBAC3C,SAAS;oBACT,UAAU,QAAQ;oBAClB,MAAM,KAAK,IAAI;oBACf,MAAM,GAAG,IAAI,CAAC,OAAO,GAAG,KAAK,GAAG,EAAE;gBACpC,CAAC;QACH;IACF;IAEA,OAAO,6BACL,IAAY,EACZ,WAAmB,EACnB,GAAW,EACX,KAA2D,EAC7C;QACd,OAAO;YACL,YAAY;YACZ,SAAS;YACT;YACA;YACA,KAAK,GAAG,IAAI,CAAC,OAAO,GAAG,KAAK;YAC5B,YAAY;gBACV,SAAS;gBACT,eAAe,MAAM,MAAM;gBAC3B,iBAAiB,MAAM,GAAG,CAAC,CAAC,MAAM,QAAU,CAAC;wBAC3C,SAAS;wBACT,UAAU,QAAQ;wBAClB,KAAK,GAAG,IAAI,CAAC,OAAO,GAAG,KAAK,GAAG,EAAE;wBACjC,MAAM,KAAK,IAAI;wBACf,GAAI,KAAK,KAAK,IAAI;4BAAE,OAAO,KAAK,KAAK;wBAAC,CAAC;oBACzC,CAAC;YACH;QACF;IACF;AACF", "debugId": null}}, {"offset": {"line": 1239, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/New%20folder%20%2825%29/streamzen/src/app/watch/series/%5Bid%5D/page.tsx"], "sourcesContent": ["import { notFound } from 'next/navigation';\nimport { Metadata } from 'next';\nimport { apiClient } from '@/lib/api';\nimport VideoPlayer from '@/components/VideoPlayer';\nimport ContentInfo from '@/components/ContentInfo';\nimport EpisodeSelector from '@/components/EpisodeSelector';\nimport VidSrcAPI from '@/lib/vidsrc';\nimport { SEOGenerator } from '@/lib/seo';\nimport { SchemaGenerator } from '@/lib/schema';\n\ninterface SeriesWatchPageProps {\n  params: Promise<{\n    id: string;\n  }>;\n  searchParams: Promise<{\n    season?: string;\n    episode?: string;\n  }>;\n}\n\nasync function getSeriesData(id: string, season?: number) {\n  try {\n    const [series, episodes] = await Promise.all([\n      apiClient.getSeriesById(id),\n      apiClient.getSeriesEpisodes(id, season)\n    ]);\n    return { series, episodes };\n  } catch (error) {\n    // Only log error if it's not a 404 (which is expected for non-existent content)\n    if (!error.message?.includes('404')) {\n      console.error('Error fetching series data:', error);\n    }\n    return { series: null, episodes: [] };\n  }\n}\n\nexport async function generateMetadata({ params, searchParams }: SeriesWatchPageProps): Promise<Metadata> {\n  const { id } = await params;\n  const { season, episode } = await searchParams;\n  const selectedSeason = season ? parseInt(season) : 1;\n  const selectedEpisode = episode ? parseInt(episode) : 1;\n\n  const { series, episodes } = await getSeriesData(id, selectedSeason);\n\n  if (!series) {\n    return {\n      title: 'Series Not Found | freeMoviesWatchNow',\n      description: 'The requested series could not be found.',\n    };\n  }\n\n  // If specific episode is requested, generate episode metadata\n  if (season && episode) {\n    const currentEpisode = episodes.find(\n      ep => ep.season === selectedSeason && ep.episode === selectedEpisode\n    );\n\n    if (currentEpisode) {\n      return SEOGenerator.generateEpisodeMetadata(currentEpisode, series);\n    }\n  }\n\n  // Otherwise generate series metadata\n  return SEOGenerator.generateSeriesMetadata(series);\n}\n\nexport default async function SeriesWatchPage({ params, searchParams }: SeriesWatchPageProps) {\n  const { id } = await params;\n  const { season, episode } = await searchParams;\n  const selectedSeason = season ? parseInt(season) : 1;\n  const selectedEpisode = episode ? parseInt(episode) : 1;\n\n  const { series, episodes } = await getSeriesData(id, selectedSeason);\n\n  if (!series) {\n    notFound();\n  }\n\n  // Find the current episode\n  const currentEpisode = episodes.find(\n    ep => ep.season === selectedSeason && ep.episode === selectedEpisode\n  );\n\n  const contentInfo = {\n    title: series.title,\n    year: series.startYear,\n    rating: series.rating,\n    imdbRating: series.imdbRating,\n    description: series.description,\n    genres: series.genres,\n    creator: series.creator,\n    cast: series.cast,\n    language: series.language,\n    country: series.country,\n    posterUrl: series.posterUrl,\n    type: 'series' as const,\n    totalSeasons: series.totalSeasons,\n    status: series.status\n  };\n\n  // Generate all streaming sources for the current episode\n  const vidsrc = VidSrcAPI.getInstance();\n  const streamingSources = vidsrc.generateAllEpisodeEmbedUrls(id, selectedSeason, selectedEpisode, series.tmdbId);\n\n  // Generate structured data\n  const isEpisodeView = season && episode && currentEpisode;\n  const schema = isEpisodeView\n    ? SchemaGenerator.generateEpisodeSchema(currentEpisode, series)\n    : SchemaGenerator.generateSeriesSchema(series);\n\n  const breadcrumbSchema = SchemaGenerator.generateBreadcrumbSchema([\n    { name: 'Home', url: '/' },\n    { name: 'TV Series', url: '/series' },\n    { name: series.title, url: `/watch/series/${series.imdbId}` },\n    ...(isEpisodeView ? [{\n      name: `S${selectedSeason}E${selectedEpisode}`,\n      url: `/watch/series/${series.imdbId}?season=${selectedSeason}&episode=${selectedEpisode}`\n    }] : [])\n  ]);\n\n  return (\n    <div className=\"min-h-screen bg-black\">\n      {/* Structured Data for SEO */}\n      <script\n        type=\"application/ld+json\"\n        dangerouslySetInnerHTML={{\n          __html: JSON.stringify([\n            SchemaGenerator.generateWebsiteSchema(),\n            schema,\n            breadcrumbSchema\n          ])\n        }}\n      />\n      <VideoPlayer\n        streamingSources={streamingSources}\n        title={`${series.title} - S${selectedSeason}E${selectedEpisode}`}\n        type=\"series\"\n      />\n\n      <div className=\"max-w-[2560px] mx-auto px-8 lg:px-24 py-12\">\n        <div className=\"grid grid-cols-1 xl:grid-cols-5 gap-16\">\n          {/* Content Info */}\n          <div className=\"xl:col-span-3\">\n            <ContentInfo content={contentInfo} />\n          </div>\n\n          {/* Episode Selector */}\n          <div className=\"xl:col-span-2\">\n            <EpisodeSelector\n              seriesId={id}\n              episodes={episodes}\n              currentSeason={selectedSeason}\n              currentEpisode={selectedEpisode}\n            />\n          </div>\n        </div>\n\n        {/* SEO Blog-Style Content */}\n        <div className=\"mt-16 max-w-4xl mx-auto\">\n          <article className=\"prose prose-invert prose-lg max-w-none\">\n            {isEpisodeView && currentEpisode ? (\n              <>\n                <h2 className=\"text-3xl font-bold text-white mb-6\">\n                  {series.title} Season {selectedSeason} Episode {selectedEpisode}\n                  {currentEpisode.episodeTitle && ` - ${currentEpisode.episodeTitle}`}\n                </h2>\n\n                <div className=\"text-gray-300 leading-relaxed space-y-4\">\n                  <p>\n                    Watch <strong>{series.title}</strong> Season {selectedSeason} Episode {selectedEpisode}\n                    {currentEpisode.episodeTitle && ` \"${currentEpisode.episodeTitle}\"`} online free in HD quality.\n                    {currentEpisode.description && ` ${currentEpisode.description}`}\n                  </p>\n\n                  {currentEpisode.airDate && (\n                    <p>\n                      This episode originally aired on <strong>{new Date(currentEpisode.airDate).toLocaleDateString()}</strong>.\n                    </p>\n                  )}\n\n                  <p>\n                    Continue following the story of <strong>{series.title}</strong>, a captivating {series.genres?.join(', ').toLowerCase()} series\n                    that has been entertaining audiences since {series.startYear}.\n                  </p>\n                </div>\n              </>\n            ) : (\n              <>\n                <h2 className=\"text-3xl font-bold text-white mb-6\">\n                  About {series.title} ({series.startYear}{series.endYear ? `-${series.endYear}` : ''})\n                </h2>\n\n                <div className=\"text-gray-300 leading-relaxed space-y-4\">\n                  <p>\n                    <strong>{series.title}</strong> is a {series.genres?.join(', ').toLowerCase()} series that premiered in {series.startYear}.\n                    {series.description && ` ${series.description}`}\n                  </p>\n\n                  {series.creator && (\n                    <p>\n                      Created by <strong>{series.creator}</strong>, this series has captivated audiences with its compelling storylines and character development.\n                    </p>\n                  )}\n\n                  {series.cast && series.cast.length > 0 && (\n                    <p>\n                      The series features an exceptional cast including <strong>{series.cast.slice(0, 5).join(', ')}</strong>\n                      {series.cast.length > 5 && ' and many more talented actors'}.\n                    </p>\n                  )}\n\n                  {series.totalSeasons && (\n                    <p>\n                      With <strong>{series.totalSeasons} season{series.totalSeasons > 1 ? 's' : ''}</strong> available,\n                      {series.title} offers hours of premium entertainment for binge-watching.\n                    </p>\n                  )}\n\n                  {series.imdbRating && (\n                    <p>\n                      Rated <strong>{series.imdbRating}/10</strong> on IMDb, {series.title} has received\n                      {series.imdbRating >= 8 ? ' critical acclaim' : series.imdbRating >= 7 ? ' positive reviews' : ' mixed reviews'}\n                      from viewers worldwide.\n                    </p>\n                  )}\n                </div>\n              </>\n            )}\n\n            <p className=\"text-gray-300 leading-relaxed mt-6\">\n              Watch <strong>{series.title}</strong> online free in HD quality on freeMoviesWatchNow. Our platform provides\n              multiple streaming sources and all episodes for the ultimate binge-watching experience.\n            </p>\n\n            <div className=\"mt-8 p-6 bg-gray-900/50 rounded-lg border border-gray-800\">\n              <h3 className=\"text-xl font-semibold text-white mb-4\">Why Choose freeMoviesWatchNow for TV Series?</h3>\n              <ul className=\"text-gray-300 space-y-2\">\n                <li>• All episodes available in HD quality</li>\n                <li>• Multiple streaming sources for reliability</li>\n                <li>• Easy episode navigation and season selection</li>\n                <li>• No ads interrupting your viewing experience</li>\n                <li>• Compatible with all devices and browsers</li>\n                <li>• Latest episodes added as soon as they air</li>\n              </ul>\n            </div>\n          </article>\n        </div>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;;AAAA;AAAA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;AAYA,eAAe,cAAc,EAAU,EAAE,MAAe;IACtD,IAAI;QACF,MAAM,CAAC,QAAQ,SAAS,GAAG,MAAM,QAAQ,GAAG,CAAC;YAC3C,iHAAA,CAAA,YAAS,CAAC,aAAa,CAAC;YACxB,iHAAA,CAAA,YAAS,CAAC,iBAAiB,CAAC,IAAI;SACjC;QACD,OAAO;YAAE;YAAQ;QAAS;IAC5B,EAAE,OAAO,OAAO;QACd,gFAAgF;QAChF,IAAI,CAAC,MAAM,OAAO,EAAE,SAAS,QAAQ;YACnC,QAAQ,KAAK,CAAC,+BAA+B;QAC/C;QACA,OAAO;YAAE,QAAQ;YAAM,UAAU,EAAE;QAAC;IACtC;AACF;AAEO,eAAe,iBAAiB,EAAE,MAAM,EAAE,YAAY,EAAwB;IACnF,MAAM,EAAE,EAAE,EAAE,GAAG,MAAM;IACrB,MAAM,EAAE,MAAM,EAAE,OAAO,EAAE,GAAG,MAAM;IAClC,MAAM,iBAAiB,SAAS,SAAS,UAAU;IACnD,MAAM,kBAAkB,UAAU,SAAS,WAAW;IAEtD,MAAM,EAAE,MAAM,EAAE,QAAQ,EAAE,GAAG,MAAM,cAAc,IAAI;IAErD,IAAI,CAAC,QAAQ;QACX,OAAO;YACL,OAAO;YACP,aAAa;QACf;IACF;IAEA,8DAA8D;IAC9D,IAAI,UAAU,SAAS;QACrB,MAAM,iBAAiB,SAAS,IAAI,CAClC,CAAA,KAAM,GAAG,MAAM,KAAK,kBAAkB,GAAG,OAAO,KAAK;QAGvD,IAAI,gBAAgB;YAClB,OAAO,iHAAA,CAAA,eAAY,CAAC,uBAAuB,CAAC,gBAAgB;QAC9D;IACF;IAEA,qCAAqC;IACrC,OAAO,iHAAA,CAAA,eAAY,CAAC,sBAAsB,CAAC;AAC7C;AAEe,eAAe,gBAAgB,EAAE,MAAM,EAAE,YAAY,EAAwB;IAC1F,MAAM,EAAE,EAAE,EAAE,GAAG,MAAM;IACrB,MAAM,EAAE,MAAM,EAAE,OAAO,EAAE,GAAG,MAAM;IAClC,MAAM,iBAAiB,SAAS,SAAS,UAAU;IACnD,MAAM,kBAAkB,UAAU,SAAS,WAAW;IAEtD,MAAM,EAAE,MAAM,EAAE,QAAQ,EAAE,GAAG,MAAM,cAAc,IAAI;IAErD,IAAI,CAAC,QAAQ;QACX,CAAA,GAAA,qLAAA,CAAA,WAAQ,AAAD;IACT;IAEA,2BAA2B;IAC3B,MAAM,iBAAiB,SAAS,IAAI,CAClC,CAAA,KAAM,GAAG,MAAM,KAAK,kBAAkB,GAAG,OAAO,KAAK;IAGvD,MAAM,cAAc;QAClB,OAAO,OAAO,KAAK;QACnB,MAAM,OAAO,SAAS;QACtB,QAAQ,OAAO,MAAM;QACrB,YAAY,OAAO,UAAU;QAC7B,aAAa,OAAO,WAAW;QAC/B,QAAQ,OAAO,MAAM;QACrB,SAAS,OAAO,OAAO;QACvB,MAAM,OAAO,IAAI;QACjB,UAAU,OAAO,QAAQ;QACzB,SAAS,OAAO,OAAO;QACvB,WAAW,OAAO,SAAS;QAC3B,MAAM;QACN,cAAc,OAAO,YAAY;QACjC,QAAQ,OAAO,MAAM;IACvB;IAEA,yDAAyD;IACzD,MAAM,SAAS,oHAAA,CAAA,UAAS,CAAC,WAAW;IACpC,MAAM,mBAAmB,OAAO,2BAA2B,CAAC,IAAI,gBAAgB,iBAAiB,OAAO,MAAM;IAE9G,2BAA2B;IAC3B,MAAM,gBAAgB,UAAU,WAAW;IAC3C,MAAM,SAAS,gBACX,oHAAA,CAAA,kBAAe,CAAC,qBAAqB,CAAC,gBAAgB,UACtD,oHAAA,CAAA,kBAAe,CAAC,oBAAoB,CAAC;IAEzC,MAAM,mBAAmB,oHAAA,CAAA,kBAAe,CAAC,wBAAwB,CAAC;QAChE;YAAE,MAAM;YAAQ,KAAK;QAAI;QACzB;YAAE,MAAM;YAAa,KAAK;QAAU;QACpC;YAAE,MAAM,OAAO,KAAK;YAAE,KAAK,CAAC,cAAc,EAAE,OAAO,MAAM,EAAE;QAAC;WACxD,gBAAgB;YAAC;gBACnB,MAAM,CAAC,CAAC,EAAE,eAAe,CAAC,EAAE,iBAAiB;gBAC7C,KAAK,CAAC,cAAc,EAAE,OAAO,MAAM,CAAC,QAAQ,EAAE,eAAe,SAAS,EAAE,iBAAiB;YAC3F;SAAE,GAAG,EAAE;KACR;IAED,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBACC,MAAK;gBACL,yBAAyB;oBACvB,QAAQ,KAAK,SAAS,CAAC;wBACrB,oHAAA,CAAA,kBAAe,CAAC,qBAAqB;wBACrC;wBACA;qBACD;gBACH;;;;;;0BAEF,8OAAC,iIAAA,CAAA,UAAW;gBACV,kBAAkB;gBAClB,OAAO,GAAG,OAAO,KAAK,CAAC,IAAI,EAAE,eAAe,CAAC,EAAE,iBAAiB;gBAChE,MAAK;;;;;;0BAGP,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;0CAEb,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC,iIAAA,CAAA,UAAW;oCAAC,SAAS;;;;;;;;;;;0CAIxB,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC,qIAAA,CAAA,UAAe;oCACd,UAAU;oCACV,UAAU;oCACV,eAAe;oCACf,gBAAgB;;;;;;;;;;;;;;;;;kCAMtB,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAQ,WAAU;;gCAChB,iBAAiB,+BAChB;;sDACE,8OAAC;4CAAG,WAAU;;gDACX,OAAO,KAAK;gDAAC;gDAAS;gDAAe;gDAAU;gDAC/C,eAAe,YAAY,IAAI,CAAC,GAAG,EAAE,eAAe,YAAY,EAAE;;;;;;;sDAGrE,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;;wDAAE;sEACK,8OAAC;sEAAQ,OAAO,KAAK;;;;;;wDAAU;wDAAS;wDAAe;wDAAU;wDACtE,eAAe,YAAY,IAAI,CAAC,EAAE,EAAE,eAAe,YAAY,CAAC,CAAC,CAAC;wDAAC;wDACnE,eAAe,WAAW,IAAI,CAAC,CAAC,EAAE,eAAe,WAAW,EAAE;;;;;;;gDAGhE,eAAe,OAAO,kBACrB,8OAAC;;wDAAE;sEACgC,8OAAC;sEAAQ,IAAI,KAAK,eAAe,OAAO,EAAE,kBAAkB;;;;;;wDAAY;;;;;;;8DAI7G,8OAAC;;wDAAE;sEAC+B,8OAAC;sEAAQ,OAAO,KAAK;;;;;;wDAAU;wDAAiB,OAAO,MAAM,EAAE,KAAK,MAAM;wDAAc;wDAC5E,OAAO,SAAS;wDAAC;;;;;;;;;;;;;;iEAKnE;;sDACE,8OAAC;4CAAG,WAAU;;gDAAqC;gDAC1C,OAAO,KAAK;gDAAC;gDAAG,OAAO,SAAS;gDAAE,OAAO,OAAO,GAAG,CAAC,CAAC,EAAE,OAAO,OAAO,EAAE,GAAG;gDAAG;;;;;;;sDAGtF,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;;sEACC,8OAAC;sEAAQ,OAAO,KAAK;;;;;;wDAAU;wDAAO,OAAO,MAAM,EAAE,KAAK,MAAM;wDAAc;wDAA2B,OAAO,SAAS;wDAAC;wDACzH,OAAO,WAAW,IAAI,CAAC,CAAC,EAAE,OAAO,WAAW,EAAE;;;;;;;gDAGhD,OAAO,OAAO,kBACb,8OAAC;;wDAAE;sEACU,8OAAC;sEAAQ,OAAO,OAAO;;;;;;wDAAU;;;;;;;gDAI/C,OAAO,IAAI,IAAI,OAAO,IAAI,CAAC,MAAM,GAAG,mBACnC,8OAAC;;wDAAE;sEACiD,8OAAC;sEAAQ,OAAO,IAAI,CAAC,KAAK,CAAC,GAAG,GAAG,IAAI,CAAC;;;;;;wDACvF,OAAO,IAAI,CAAC,MAAM,GAAG,KAAK;wDAAiC;;;;;;;gDAI/D,OAAO,YAAY,kBAClB,8OAAC;;wDAAE;sEACI,8OAAC;;gEAAQ,OAAO,YAAY;gEAAC;gEAAQ,OAAO,YAAY,GAAG,IAAI,MAAM;;;;;;;wDAAY;wDACrF,OAAO,KAAK;wDAAC;;;;;;;gDAIjB,OAAO,UAAU,kBAChB,8OAAC;;wDAAE;sEACK,8OAAC;;gEAAQ,OAAO,UAAU;gEAAC;;;;;;;wDAAY;wDAAW,OAAO,KAAK;wDAAC;wDACpE,OAAO,UAAU,IAAI,IAAI,sBAAsB,OAAO,UAAU,IAAI,IAAI,sBAAsB;wDAAiB;;;;;;;;;;;;;;;8CAQ1H,8OAAC;oCAAE,WAAU;;wCAAqC;sDAC1C,8OAAC;sDAAQ,OAAO,KAAK;;;;;;wCAAU;;;;;;;8CAIvC,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAG,WAAU;sDAAwC;;;;;;sDACtD,8OAAC;4CAAG,WAAU;;8DACZ,8OAAC;8DAAG;;;;;;8DACJ,8OAAC;8DAAG;;;;;;8DACJ,8OAAC;8DAAG;;;;;;8DACJ,8OAAC;8DAAG;;;;;;8DACJ,8OAAC;8DAAG;;;;;;8DACJ,8OAAC;8DAAG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQpB", "debugId": null}}]}