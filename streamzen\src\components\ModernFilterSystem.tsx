'use client';

import React, { useState, useEffect, useRef } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import { Filter, X, ChevronDown, Search, SlidersHorizontal, Star, Calendar, Globe, Tag, Sparkles } from 'lucide-react';
import { motion, AnimatePresence } from 'framer-motion';
import { apiClient, FilterOptions } from '@/lib/api';
import { cn } from '@/lib/utils';

interface ModernFilterSystemProps {
  currentFilters: Record<string, string | undefined>;
  basePath: string;
  contentType: 'movies' | 'series' | 'episodes';
}

const ModernFilterSystem: React.FC<ModernFilterSystemProps> = ({
  currentFilters,
  basePath,
  contentType
}) => {
  const router = useRouter();
  const searchParams = useSearchParams();
  const [filterOptions, setFilterOptions] = useState<FilterOptions | null>(null);
  const [loading, setLoading] = useState(true);
  const [activeDropdown, setActiveDropdown] = useState<string | null>(null);
  const [searchQuery, setSearchQuery] = useState(currentFilters.search || '');
  const dropdownRef = useRef<HTMLDivElement>(null);

  // Load filter options
  useEffect(() => {
    const loadFilterOptions = async () => {
      try {
        setLoading(true);
        let options: FilterOptions;
        
        switch (contentType) {
          case 'movies':
            options = await apiClient.getMovieFilterOptions();
            break;
          case 'series':
            options = await apiClient.getSeriesFilterOptions();
            break;
          case 'episodes':
            options = await apiClient.getEpisodeFilterOptions();
            break;
          default:
            options = { genres: [], languages: [], countries: [], years: [], ratings: [], qualities: [] };
        }
        
        setFilterOptions(options);
      } catch (error) {
        console.error('Error loading filter options:', error);
        setFilterOptions({ genres: [], languages: [], countries: [], years: [], ratings: [], qualities: [] });
      } finally {
        setLoading(false);
      }
    };

    loadFilterOptions();
  }, [contentType]);

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setActiveDropdown(null);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  const updateFilter = (key: string, value: string | null) => {
    const params = new URLSearchParams(searchParams.toString());

    if (value) {
      params.set(key, value);
    } else {
      params.delete(key);
    }

    // Reset to page 1 when filters change
    params.delete('page');

    router.push(`${basePath}?${params.toString()}`);
    setActiveDropdown(null);
  };

  const clearAllFilters = () => {
    router.push(basePath);
    setSearchQuery('');
  };

  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault();
    updateFilter('search', searchQuery || null);
  };

  const hasActiveFilters = Object.entries(currentFilters).some(
    ([key, value]) => value && key !== 'page' && key !== 'sortBy' && key !== 'sortOrder'
  );

  const getFilterIcon = (filterType: string) => {
    switch (filterType) {
      case 'genre': return Tag;
      case 'year': return Calendar;
      case 'language': case 'country': return Globe;
      case 'rating': return Star;
      default: return Filter;
    }
  };

  const getFilterColor = (filterType: string) => {
    switch (filterType) {
      case 'genre': return 'from-purple-500 to-pink-600';
      case 'year': return 'from-blue-500 to-cyan-600';
      case 'language': return 'from-green-500 to-teal-600';
      case 'country': return 'from-orange-500 to-red-600';
      case 'rating': return 'from-yellow-500 to-amber-600';
      case 'quality': return 'from-indigo-500 to-purple-600';
      default: return 'from-gray-500 to-gray-600';
    }
  };

  const FilterDropdown = ({ filterType, options, title }: { 
    filterType: string; 
    options: any[]; 
    title: string;
  }) => {
    const Icon = getFilterIcon(filterType);
    const colorClass = getFilterColor(filterType);
    const isActive = activeDropdown === filterType;
    const hasValue = currentFilters[filterType];

    return (
      <div className="relative">
        <motion.button
          whileHover={{ scale: 1.02 }}
          whileTap={{ scale: 0.98 }}
          onClick={() => setActiveDropdown(isActive ? null : filterType)}
          className={cn(
            "flex items-center space-x-2 px-4 py-2 rounded-xl border transition-all duration-300 backdrop-blur-sm",
            hasValue
              ? `bg-gradient-to-r ${colorClass} text-white border-transparent shadow-lg`
              : "bg-gray-900/50 hover:bg-gray-800/50 text-gray-300 hover:text-white border-gray-700 hover:border-gray-600"
          )}
        >
          <Icon className="w-4 h-4" />
          <span className="font-medium">
            {hasValue ? `${title}: ${hasValue}` : title}
          </span>
          <ChevronDown className={cn(
            "w-4 h-4 transition-transform duration-300",
            isActive && "rotate-180"
          )} />
        </motion.button>

        <AnimatePresence>
          {isActive && (
            <motion.div
              ref={dropdownRef}
              initial={{ opacity: 0, y: 10, scale: 0.95 }}
              animate={{ opacity: 1, y: 0, scale: 1 }}
              exit={{ opacity: 0, y: 10, scale: 0.95 }}
              transition={{ duration: 0.2 }}
              className="absolute top-full left-0 mt-2 w-64 max-h-80 bg-gray-900/95 backdrop-blur-xl border border-gray-700 rounded-xl shadow-2xl overflow-hidden z-50"
            >
              <div className="p-2 border-b border-gray-700">
                <div className="flex items-center justify-between">
                  <span className="text-white font-semibold">{title}</span>
                  {hasValue && (
                    <button
                      onClick={() => updateFilter(filterType, null)}
                      className="text-gray-400 hover:text-white transition-colors duration-200"
                    >
                      <X className="w-4 h-4" />
                    </button>
                  )}
                </div>
              </div>
              
              <div className="max-h-64 overflow-y-auto scrollbar-thin scrollbar-thumb-gray-600 scrollbar-track-transparent">
                {options.map((option) => {
                  const value = typeof option === 'string' ? option : option.genre || option;
                  const count = typeof option === 'object' && option.count ? option.count : null;
                  
                  return (
                    <button
                      key={value}
                      onClick={() => updateFilter(filterType, value)}
                      className={cn(
                        "w-full flex items-center justify-between px-4 py-3 text-left transition-all duration-200",
                        currentFilters[filterType] === value
                          ? "bg-gradient-to-r from-blue-500/20 to-purple-500/20 text-white"
                          : "text-gray-300 hover:text-white hover:bg-gray-800/50"
                      )}
                    >
                      <span className="font-medium">{value}</span>
                      {count && (
                        <span className="text-xs text-gray-400 bg-gray-800 px-2 py-1 rounded-full">
                          {count}
                        </span>
                      )}
                    </button>
                  );
                })}
              </div>
            </motion.div>
          )}
        </AnimatePresence>
      </div>
    );
  };

  const SortDropdown = () => {
    const sortOptions = [
      { value: 'createdAt', label: 'Recently Added' },
      { value: 'imdbRating', label: 'Highest Rated' },
      { value: 'year', label: 'Release Year' },
      { value: 'title', label: 'Title A-Z' },
      { value: 'popularity', label: 'Most Popular' }
    ];

    const isActive = activeDropdown === 'sort';
    const currentSort = currentFilters.sortBy || 'createdAt';
    const currentOrder = currentFilters.sortOrder || 'desc';
    const currentSortLabel = sortOptions.find(opt => opt.value === currentSort)?.label || 'Recently Added';

    return (
      <div className="relative">
        <motion.button
          whileHover={{ scale: 1.02 }}
          whileTap={{ scale: 0.98 }}
          onClick={() => setActiveDropdown(isActive ? null : 'sort')}
          className="flex items-center space-x-2 px-4 py-2 rounded-xl border bg-gray-900/50 hover:bg-gray-800/50 text-gray-300 hover:text-white border-gray-700 hover:border-gray-600 transition-all duration-300 backdrop-blur-sm"
        >
          <SlidersHorizontal className="w-4 h-4" />
          <span className="font-medium">Sort: {currentSortLabel}</span>
          <ChevronDown className={cn(
            "w-4 h-4 transition-transform duration-300",
            isActive && "rotate-180"
          )} />
        </motion.button>

        <AnimatePresence>
          {isActive && (
            <motion.div
              ref={dropdownRef}
              initial={{ opacity: 0, y: 10, scale: 0.95 }}
              animate={{ opacity: 1, y: 0, scale: 1 }}
              exit={{ opacity: 0, y: 10, scale: 0.95 }}
              transition={{ duration: 0.2 }}
              className="absolute top-full right-0 mt-2 w-56 bg-gray-900/95 backdrop-blur-xl border border-gray-700 rounded-xl shadow-2xl overflow-hidden z-50"
            >
              <div className="p-2 border-b border-gray-700">
                <span className="text-white font-semibold">Sort Options</span>
              </div>
              
              <div className="p-2">
                {sortOptions.map((option) => (
                  <button
                    key={option.value}
                    onClick={() => {
                      updateFilter('sortBy', option.value);
                      updateFilter('sortOrder', option.value === 'title' ? 'asc' : 'desc');
                    }}
                    className={cn(
                      "w-full flex items-center justify-between px-3 py-2 rounded-lg text-left transition-all duration-200",
                      currentSort === option.value
                        ? "bg-gradient-to-r from-blue-500/20 to-purple-500/20 text-white"
                        : "text-gray-300 hover:text-white hover:bg-gray-800/50"
                    )}
                  >
                    <span className="font-medium">{option.label}</span>
                    {currentSort === option.value && (
                      <span className="text-xs text-blue-400">
                        {currentOrder === 'desc' ? '↓' : '↑'}
                      </span>
                    )}
                  </button>
                ))}
              </div>
            </motion.div>
          )}
        </AnimatePresence>
      </div>
    );
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center py-8">
        <div className="flex items-center space-x-2 text-gray-400">
          <div className="w-4 h-4 border-2 border-gray-600 border-t-blue-500 rounded-full animate-spin" />
          <span>Loading filters...</span>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Search Bar */}
      <form onSubmit={handleSearch} className="relative">
        <div className="relative">
          <Search className="absolute left-4 top-1/2 -translate-y-1/2 w-5 h-5 text-gray-400" />
          <input
            type="text"
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            placeholder={`Search ${contentType}...`}
            className="w-full pl-12 pr-4 py-3 bg-gray-900/50 border border-gray-700 rounded-xl text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-300 backdrop-blur-sm"
          />
          {searchQuery && (
            <button
              type="button"
              onClick={() => {
                setSearchQuery('');
                updateFilter('search', null);
              }}
              className="absolute right-4 top-1/2 -translate-y-1/2 text-gray-400 hover:text-white transition-colors duration-200"
            >
              <X className="w-4 h-4" />
            </button>
          )}
        </div>
      </form>

      {/* Filter Chips */}
      <div className="flex flex-wrap gap-3">
        {/* Sort */}
        <SortDropdown />

        {/* Genre Filter */}
        {filterOptions?.genres && filterOptions.genres.length > 0 && (
          <FilterDropdown
            filterType="genre"
            options={filterOptions.genres}
            title="Genre"
          />
        )}

        {/* Year Filter */}
        {filterOptions?.years && filterOptions.years.length > 0 && (
          <FilterDropdown
            filterType="year"
            options={filterOptions.years}
            title="Year"
          />
        )}

        {/* Language Filter */}
        {filterOptions?.languages && filterOptions.languages.length > 0 && (
          <FilterDropdown
            filterType="language"
            options={filterOptions.languages}
            title="Language"
          />
        )}

        {/* Country Filter */}
        {filterOptions?.countries && filterOptions.countries.length > 0 && (
          <FilterDropdown
            filterType="country"
            options={filterOptions.countries}
            title="Country"
          />
        )}

        {/* Rating Filter */}
        {filterOptions?.ratings && filterOptions.ratings.length > 0 && (
          <FilterDropdown
            filterType="rating"
            options={filterOptions.ratings}
            title="Rating"
          />
        )}

        {/* Quality Filter */}
        {filterOptions?.qualities && filterOptions.qualities.length > 0 && (
          <FilterDropdown
            filterType="quality"
            options={filterOptions.qualities}
            title="Quality"
          />
        )}

        {/* Clear All Filters */}
        {hasActiveFilters && (
          <motion.button
            whileHover={{ scale: 1.02 }}
            whileTap={{ scale: 0.98 }}
            onClick={clearAllFilters}
            className="flex items-center space-x-2 px-4 py-2 bg-red-500/20 hover:bg-red-500/30 border border-red-500/30 hover:border-red-500/50 text-red-400 hover:text-red-300 rounded-xl transition-all duration-300 backdrop-blur-sm"
          >
            <X className="w-4 h-4" />
            <span className="font-medium">Clear All</span>
          </motion.button>
        )}
      </div>

      {/* Active Filters Display */}
      {hasActiveFilters && (
        <motion.div
          initial={{ opacity: 0, y: 10 }}
          animate={{ opacity: 1, y: 0 }}
          className="flex flex-wrap gap-2"
        >
          {Object.entries(currentFilters).map(([key, value]) => {
            if (!value || key === 'page' || key === 'sortBy' || key === 'sortOrder') return null;
            
            return (
              <motion.div
                key={key}
                initial={{ opacity: 0, scale: 0.8 }}
                animate={{ opacity: 1, scale: 1 }}
                exit={{ opacity: 0, scale: 0.8 }}
                className="flex items-center space-x-1 px-3 py-1 bg-blue-500/20 border border-blue-500/30 text-blue-300 rounded-full text-sm"
              >
                <span className="capitalize">{key}:</span>
                <span className="font-medium">{value}</span>
                <button
                  onClick={() => updateFilter(key, null)}
                  className="ml-1 text-blue-400 hover:text-blue-300 transition-colors duration-200"
                >
                  <X className="w-3 h-3" />
                </button>
              </motion.div>
            );
          })}
        </motion.div>
      )}
    </div>
  );
};

export default ModernFilterSystem;
