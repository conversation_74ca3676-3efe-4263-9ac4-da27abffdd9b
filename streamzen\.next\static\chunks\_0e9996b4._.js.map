{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/New%20folder%20%2825%29/streamzen/src/components/ui/Button.tsx"], "sourcesContent": ["import React from 'react';\nimport { cn } from '@/lib/utils';\n\ninterface ButtonProps extends React.ButtonHTMLAttributes<HTMLButtonElement> {\n  variant?: 'primary' | 'secondary' | 'ghost';\n  size?: 'sm' | 'md' | 'lg';\n  children: React.ReactNode;\n}\n\nconst Button: React.FC<ButtonProps> = ({\n  variant = 'primary',\n  size = 'md',\n  className,\n  children,\n  ...props\n}) => {\n  const baseClasses = 'inline-flex items-center justify-center rounded-lg font-medium transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-white/20';\n  \n  const variantClasses = {\n    primary: 'bg-white text-black hover:bg-gray-200 active:bg-gray-300',\n    secondary: 'bg-white/10 text-white hover:bg-white/20 active:bg-white/30 backdrop-blur-sm',\n    ghost: 'text-white hover:bg-white/10 active:bg-white/20'\n  };\n  \n  const sizeClasses = {\n    sm: 'px-3 py-1.5 text-sm',\n    md: 'px-4 py-2 text-base',\n    lg: 'px-6 py-3 text-lg'\n  };\n\n  return (\n    <button\n      className={cn(\n        baseClasses,\n        variantClasses[variant],\n        sizeClasses[size],\n        className\n      )}\n      {...props}\n    >\n      {children}\n    </button>\n  );\n};\n\nexport default Button;\n"], "names": [], "mappings": ";;;;AACA;;;AAQA,MAAM,SAAgC,CAAC,EACrC,UAAU,SAAS,EACnB,OAAO,IAAI,EACX,SAAS,EACT,QAAQ,EACR,GAAG,OACJ;IACC,MAAM,cAAc;IAEpB,MAAM,iBAAiB;QACrB,SAAS;QACT,WAAW;QACX,OAAO;IACT;IAEA,MAAM,cAAc;QAClB,IAAI;QACJ,IAAI;QACJ,IAAI;IACN;IAEA,qBACE,6LAAC;QACC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,aACA,cAAc,CAAC,QAAQ,EACvB,WAAW,CAAC,KAAK,EACjB;QAED,GAAG,KAAK;kBAER;;;;;;AAGP;KAlCM;uCAoCS", "debugId": null}}, {"offset": {"line": 49, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/New%20folder%20%2825%29/streamzen/src/components/InitializeButton.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState } from 'react';\nimport { Loader2, Database, CheckCircle, AlertCircle } from 'lucide-react';\nimport Button from './ui/Button';\n\nconst InitializeButton: React.FC = () => {\n  const [isInitializing, setIsInitializing] = useState(false);\n  const [status, setStatus] = useState<'idle' | 'loading' | 'success' | 'error'>('idle');\n  const [message, setMessage] = useState('');\n\n  const handleInitialize = async () => {\n    setIsInitializing(true);\n    setStatus('loading');\n    setMessage('Initializing database with real content...');\n\n    try {\n      const response = await fetch('/api/init', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n      });\n\n      const data = await response.json();\n\n      if (data.success) {\n        setStatus('success');\n        setMessage(`Database initialized successfully! Added ${data.counts.movies} movies, ${data.counts.series} series, and ${data.counts.episodes} episodes.`);\n        \n        // Refresh the page after a short delay\n        setTimeout(() => {\n          window.location.reload();\n        }, 2000);\n      } else {\n        setStatus('error');\n        setMessage(data.error || 'Failed to initialize database');\n      }\n    } catch (error) {\n      setStatus('error');\n      setMessage('Network error occurred while initializing database');\n      console.error('Initialization error:', error);\n    } finally {\n      setIsInitializing(false);\n    }\n  };\n\n  const getStatusIcon = () => {\n    switch (status) {\n      case 'loading':\n        return <Loader2 className=\"animate-spin\" size={20} />;\n      case 'success':\n        return <CheckCircle className=\"text-green-400\" size={20} />;\n      case 'error':\n        return <AlertCircle className=\"text-red-400\" size={20} />;\n      default:\n        return <Database size={20} />;\n    }\n  };\n\n  const getButtonText = () => {\n    switch (status) {\n      case 'loading':\n        return 'Initializing...';\n      case 'success':\n        return 'Initialized!';\n      case 'error':\n        return 'Try Again';\n      default:\n        return 'Initialize Database';\n    }\n  };\n\n  const getButtonVariant = () => {\n    switch (status) {\n      case 'success':\n        return 'primary' as const;\n      case 'error':\n        return 'secondary' as const;\n      default:\n        return 'primary' as const;\n    }\n  };\n\n  return (\n    <div className=\"max-w-md mx-auto\">\n      <Button\n        onClick={handleInitialize}\n        disabled={isInitializing || status === 'success'}\n        variant={getButtonVariant()}\n        size=\"lg\"\n        className=\"flex items-center space-x-2 w-full justify-center\"\n      >\n        {getStatusIcon()}\n        <span>{getButtonText()}</span>\n      </Button>\n      \n      {message && (\n        <div className={`mt-4 p-4 rounded-lg ${\n          status === 'success'\n            ? 'bg-green-900/20 border border-green-500/20 text-green-400'\n            : status === 'error'\n            ? 'bg-red-900/20 border border-red-500/20 text-red-400'\n            : 'bg-gray-800/20 border border-gray-500/20 text-gray-400'\n        }`}>\n          <p className=\"text-sm\">{message}</p>\n        </div>\n      )}\n      \n      {status === 'loading' && (\n        <div className=\"mt-4 text-center\">\n          <p className=\"text-gray-400 text-sm\">\n            This may take a few minutes as we scrape content from IMDb...\n          </p>\n        </div>\n      )}\n    </div>\n  );\n};\n\nexport default InitializeButton;\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AACA;;;AAJA;;;;AAMA,MAAM,mBAA6B;;IACjC,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACrD,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAA4C;IAC/E,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEvC,MAAM,mBAAmB;QACvB,kBAAkB;QAClB,UAAU;QACV,WAAW;QAEX,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,aAAa;gBACxC,QAAQ;gBACR,SAAS;oBACP,gBAAgB;gBAClB;YACF;YAEA,MAAM,OAAO,MAAM,SAAS,IAAI;YAEhC,IAAI,KAAK,OAAO,EAAE;gBAChB,UAAU;gBACV,WAAW,CAAC,yCAAyC,EAAE,KAAK,MAAM,CAAC,MAAM,CAAC,SAAS,EAAE,KAAK,MAAM,CAAC,MAAM,CAAC,aAAa,EAAE,KAAK,MAAM,CAAC,QAAQ,CAAC,UAAU,CAAC;gBAEvJ,uCAAuC;gBACvC,WAAW;oBACT,OAAO,QAAQ,CAAC,MAAM;gBACxB,GAAG;YACL,OAAO;gBACL,UAAU;gBACV,WAAW,KAAK,KAAK,IAAI;YAC3B;QACF,EAAE,OAAO,OAAO;YACd,UAAU;YACV,WAAW;YACX,QAAQ,KAAK,CAAC,yBAAyB;QACzC,SAAU;YACR,kBAAkB;QACpB;IACF;IAEA,MAAM,gBAAgB;QACpB,OAAQ;YACN,KAAK;gBACH,qBAAO,6LAAC,oNAAA,CAAA,UAAO;oBAAC,WAAU;oBAAe,MAAM;;;;;;YACjD,KAAK;gBACH,qBAAO,6LAAC,8NAAA,CAAA,cAAW;oBAAC,WAAU;oBAAiB,MAAM;;;;;;YACvD,KAAK;gBACH,qBAAO,6LAAC,uNAAA,CAAA,cAAW;oBAAC,WAAU;oBAAe,MAAM;;;;;;YACrD;gBACE,qBAAO,6LAAC,6MAAA,CAAA,WAAQ;oBAAC,MAAM;;;;;;QAC3B;IACF;IAEA,MAAM,gBAAgB;QACpB,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,MAAM,mBAAmB;QACvB,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC,qIAAA,CAAA,UAAM;gBACL,SAAS;gBACT,UAAU,kBAAkB,WAAW;gBACvC,SAAS;gBACT,MAAK;gBACL,WAAU;;oBAET;kCACD,6LAAC;kCAAM;;;;;;;;;;;;YAGR,yBACC,6LAAC;gBAAI,WAAW,CAAC,oBAAoB,EACnC,WAAW,YACP,8DACA,WAAW,UACX,wDACA,0DACJ;0BACA,cAAA,6LAAC;oBAAE,WAAU;8BAAW;;;;;;;;;;;YAI3B,WAAW,2BACV,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAE,WAAU;8BAAwB;;;;;;;;;;;;;;;;;AAO/C;GAhHM;KAAA;uCAkHS", "debugId": null}}]}