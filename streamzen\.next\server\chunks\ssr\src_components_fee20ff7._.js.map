{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/New%20folder%20%2825%29/streamzen/src/components/EnhancedHeroSection.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState, useEffect, useCallback } from 'react';\nimport Image from 'next/image';\nimport Link from 'next/link';\nimport { ChevronLeft, ChevronRight, Play, Info, Star, Volume2, VolumeX, Sparkles, TrendingUp } from 'lucide-react';\nimport { motion, AnimatePresence } from 'framer-motion';\nimport { cn, getImageUrl, formatRating, truncateText } from '@/lib/utils';\n\ninterface HeroItem {\n  id: string;\n  imdbId: string;\n  title: string;\n  year?: number;\n  posterUrl?: string;\n  imdbRating?: number;\n  description?: string;\n  type: 'movie' | 'series';\n}\n\ninterface EnhancedHeroSectionProps {\n  items: HeroItem[];\n}\n\nconst EnhancedHeroSection: React.FC<EnhancedHeroSectionProps> = ({ items }) => {\n  const [currentIndex, setCurrentIndex] = useState(0);\n  const [isAutoPlaying, setIsAutoPlaying] = useState(true);\n  const [isMuted, setIsMuted] = useState(true);\n  const [isLoaded, setIsLoaded] = useState(false);\n  const [mousePosition, setMousePosition] = useState({ x: 0, y: 0 });\n\n  // Auto-play functionality\n  useEffect(() => {\n    if (!isAutoPlaying || items.length <= 1) return;\n\n    const interval = setInterval(() => {\n      setCurrentIndex((prev) => (prev + 1) % items.length);\n    }, 10000); // Longer duration for immersive experience\n\n    return () => clearInterval(interval);\n  }, [isAutoPlaying, items.length]);\n\n  // Mouse tracking for parallax effects\n  const handleMouseMove = useCallback((e: React.MouseEvent) => {\n    const rect = e.currentTarget.getBoundingClientRect();\n    setMousePosition({\n      x: (e.clientX - rect.left) / rect.width,\n      y: (e.clientY - rect.top) / rect.height,\n    });\n  }, []);\n\n  // Preload next image\n  useEffect(() => {\n    if (items.length > 1) {\n      const nextIndex = (currentIndex + 1) % items.length;\n      const img = new window.Image();\n      img.src = getImageUrl(items[nextIndex].posterUrl);\n    }\n  }, [currentIndex, items]);\n\n  const currentItem = items[currentIndex];\n\n  if (!currentItem || items.length === 0) {\n    return (\n      <div className=\"relative h-screen bg-gradient-to-br from-gray-900 via-black to-gray-900 flex items-center justify-center\">\n        <div className=\"text-center\">\n          <Sparkles className=\"w-16 h-16 text-white/50 mx-auto mb-4 animate-pulse\" />\n          <h2 className=\"text-2xl font-bold text-white/70\">Loading amazing content...</h2>\n        </div>\n      </div>\n    );\n  }\n\n  const navigateToItem = (index: number) => {\n    setCurrentIndex(index);\n    setIsAutoPlaying(false);\n    setTimeout(() => setIsAutoPlaying(true), 15000); // Resume auto-play after 15s\n  };\n\n  const nextItem = () => navigateToItem((currentIndex + 1) % items.length);\n  const prevItem = () => navigateToItem((currentIndex - 1 + items.length) % items.length);\n\n  return (\n    <div \n      className=\"relative h-screen overflow-hidden bg-black\"\n      onMouseMove={handleMouseMove}\n      onMouseEnter={() => setIsAutoPlaying(false)}\n      onMouseLeave={() => setIsAutoPlaying(true)}\n    >\n      {/* Dynamic Background with Parallax */}\n      <div className=\"absolute inset-0\">\n        <AnimatePresence mode=\"wait\">\n          <motion.div\n            key={currentIndex}\n            initial={{ opacity: 0, scale: 1.1 }}\n            animate={{ \n              opacity: 1, \n              scale: 1,\n              x: (mousePosition.x - 0.5) * 20,\n              y: (mousePosition.y - 0.5) * 20\n            }}\n            exit={{ opacity: 0, scale: 0.95 }}\n            transition={{ duration: 1.2, ease: \"easeInOut\" }}\n            className=\"absolute inset-0\"\n          >\n            <Image\n              src={getImageUrl(currentItem.posterUrl)}\n              alt={currentItem.title}\n              fill\n              className=\"object-cover object-center\"\n              priority\n              onLoad={() => setIsLoaded(true)}\n            />\n            {/* Multi-layer Gradient Overlays */}\n            <div className=\"absolute inset-0 bg-gradient-to-r from-black via-black/70 to-transparent\" />\n            <div className=\"absolute inset-0 bg-gradient-to-t from-black via-transparent to-black/30\" />\n            <div className=\"absolute inset-0 bg-gradient-to-b from-transparent via-transparent to-black\" />\n          </motion.div>\n        </AnimatePresence>\n      </div>\n\n      {/* Floating Particles Effect */}\n      <div className=\"absolute inset-0 overflow-hidden pointer-events-none\">\n        {[...Array(20)].map((_, i) => (\n          <motion.div\n            key={i}\n            className=\"absolute w-1 h-1 bg-white/20 rounded-full\"\n            animate={{\n              x: [0, Math.random() * 100, 0],\n              y: [0, Math.random() * 100, 0],\n              opacity: [0, 1, 0],\n            }}\n            transition={{\n              duration: Math.random() * 10 + 10,\n              repeat: Infinity,\n              ease: \"linear\",\n            }}\n            style={{\n              left: `${Math.random() * 100}%`,\n              top: `${Math.random() * 100}%`,\n            }}\n          />\n        ))}\n      </div>\n\n      {/* Main Content */}\n      <div className=\"relative h-full flex items-center\">\n        <div className=\"max-w-7xl mx-auto px-6 lg:px-12 w-full\">\n          <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-12 items-center\">\n            {/* Content Side */}\n            <motion.div\n              key={`content-${currentIndex}`}\n              initial={{ opacity: 0, x: -50 }}\n              animate={{ opacity: 1, x: 0 }}\n              transition={{ duration: 0.8, delay: 0.3 }}\n              className=\"space-y-8\"\n            >\n              {/* Category Badge */}\n              <motion.div\n                initial={{ opacity: 0, y: 20 }}\n                animate={{ opacity: 1, y: 0 }}\n                transition={{ duration: 0.6, delay: 0.5 }}\n                className=\"flex items-center space-x-3\"\n              >\n                <div className=\"px-4 py-2 bg-gradient-to-r from-red-600/20 to-red-700/20 border border-red-500/30 rounded-full backdrop-blur-sm\">\n                  <span className=\"text-red-400 text-sm font-semibold uppercase tracking-wider\">\n                    {currentItem.type === 'movie' ? '🎬 Featured Movie' : '📺 Featured Series'}\n                  </span>\n                </div>\n                {currentItem.imdbRating && currentItem.imdbRating >= 8.0 && (\n                  <div className=\"flex items-center space-x-1 px-3 py-1 bg-yellow-500/20 border border-yellow-400/30 rounded-full backdrop-blur-sm\">\n                    <Star className=\"w-4 h-4 text-yellow-400 fill-current\" />\n                    <span className=\"text-yellow-400 text-sm font-bold\">Top Rated</span>\n                  </div>\n                )}\n              </motion.div>\n\n              {/* Title */}\n              <motion.h1\n                initial={{ opacity: 0, y: 30 }}\n                animate={{ opacity: 1, y: 0 }}\n                transition={{ duration: 0.8, delay: 0.6 }}\n                className=\"text-5xl lg:text-7xl font-black text-white leading-tight\"\n                style={{\n                  textShadow: '2px 2px 4px rgba(0,0,0,0.8), 0 0 20px rgba(0,0,0,0.5)'\n                }}\n              >\n                {currentItem.title}\n              </motion.h1>\n\n              {/* Metadata */}\n              <motion.div\n                initial={{ opacity: 0, y: 20 }}\n                animate={{ opacity: 1, y: 0 }}\n                transition={{ duration: 0.6, delay: 0.8 }}\n                className=\"flex items-center space-x-6 text-gray-300\"\n              >\n                {currentItem.year && (\n                  <span className=\"text-lg font-semibold\">{currentItem.year}</span>\n                )}\n                {currentItem.imdbRating && (\n                  <div className=\"flex items-center space-x-2\">\n                    <Star className=\"w-5 h-5 text-yellow-400 fill-current\" />\n                    <span className=\"text-lg font-bold text-white\">\n                      {formatRating(currentItem.imdbRating)}\n                    </span>\n                  </div>\n                )}\n                <div className=\"flex items-center space-x-1\">\n                  <TrendingUp className=\"w-5 h-5 text-green-400\" />\n                  <span className=\"text-green-400 font-semibold\">Trending</span>\n                </div>\n              </motion.div>\n\n              {/* Description */}\n              {currentItem.description && (\n                <motion.p\n                  initial={{ opacity: 0, y: 20 }}\n                  animate={{ opacity: 1, y: 0 }}\n                  transition={{ duration: 0.6, delay: 1.0 }}\n                  className=\"text-lg text-gray-300 leading-relaxed max-w-2xl\"\n                  style={{\n                    textShadow: '1px 1px 2px rgba(0,0,0,0.8)'\n                  }}\n                >\n                  {truncateText(currentItem.description, 200)}\n                </motion.p>\n              )}\n\n              {/* Action Buttons */}\n              <motion.div\n                initial={{ opacity: 0, y: 30 }}\n                animate={{ opacity: 1, y: 0 }}\n                transition={{ duration: 0.8, delay: 1.2 }}\n                className=\"flex items-center space-x-4\"\n              >\n                <Link\n                  href={`/watch/${currentItem.type}/${currentItem.imdbId}`}\n                  className=\"group relative px-8 py-4 bg-gradient-to-r from-red-600 to-red-700 hover:from-red-500 hover:to-red-600 text-white font-bold rounded-xl transition-all duration-300 transform hover:scale-105 hover:shadow-2xl hover:shadow-red-500/25\"\n                >\n                  <div className=\"flex items-center space-x-3\">\n                    <Play className=\"w-6 h-6 fill-current\" />\n                    <span className=\"text-lg\">Watch Now</span>\n                  </div>\n                  <div className=\"absolute inset-0 bg-white/20 rounded-xl opacity-0 group-hover:opacity-100 transition-opacity duration-300\" />\n                </Link>\n\n                <Link\n                  href={`/watch/${currentItem.type}/${currentItem.imdbId}`}\n                  className=\"group px-8 py-4 bg-white/10 hover:bg-white/20 text-white font-semibold rounded-xl border border-white/20 hover:border-white/40 transition-all duration-300 backdrop-blur-sm\"\n                >\n                  <div className=\"flex items-center space-x-3\">\n                    <Info className=\"w-5 h-5\" />\n                    <span>More Info</span>\n                  </div>\n                </Link>\n              </motion.div>\n            </motion.div>\n\n            {/* Enhanced Poster Side */}\n            <motion.div\n              key={`poster-${currentIndex}`}\n              initial={{ opacity: 0, x: 50, rotateY: 15 }}\n              animate={{ opacity: 1, x: 0, rotateY: 0 }}\n              transition={{ duration: 1, delay: 0.4 }}\n              className=\"hidden lg:flex justify-center items-center\"\n            >\n              <div className=\"relative group\">\n                <div className=\"absolute -inset-4 bg-gradient-to-r from-red-600/20 via-purple-600/20 to-blue-600/20 rounded-3xl blur-xl group-hover:blur-2xl transition-all duration-500\" />\n                <div className=\"relative\">\n                  <Image\n                    src={getImageUrl(currentItem.posterUrl)}\n                    alt={currentItem.title}\n                    width={400}\n                    height={600}\n                    className=\"rounded-2xl shadow-2xl transform group-hover:scale-105 transition-transform duration-500\"\n                  />\n                  <div className=\"absolute inset-0 bg-gradient-to-t from-black/50 via-transparent to-transparent rounded-2xl\" />\n                </div>\n              </div>\n            </motion.div>\n          </div>\n        </div>\n      </div>\n\n      {/* Navigation Controls */}\n      {items.length > 1 && (\n        <>\n          {/* Previous/Next Buttons */}\n          <button\n            onClick={prevItem}\n            className=\"absolute left-6 top-1/2 -translate-y-1/2 p-3 bg-black/50 hover:bg-black/70 text-white rounded-full backdrop-blur-sm transition-all duration-300 hover:scale-110 z-10\"\n          >\n            <ChevronLeft className=\"w-6 h-6\" />\n          </button>\n          <button\n            onClick={nextItem}\n            className=\"absolute right-6 top-1/2 -translate-y-1/2 p-3 bg-black/50 hover:bg-black/70 text-white rounded-full backdrop-blur-sm transition-all duration-300 hover:scale-110 z-10\"\n          >\n            <ChevronRight className=\"w-6 h-6\" />\n          </button>\n\n          {/* Dots Indicator */}\n          <div className=\"absolute bottom-8 left-1/2 -translate-x-1/2 flex space-x-3 z-10\">\n            {items.map((_, index) => (\n              <button\n                key={index}\n                onClick={() => navigateToItem(index)}\n                className={cn(\n                  \"w-3 h-3 rounded-full transition-all duration-300\",\n                  index === currentIndex\n                    ? \"bg-white scale-125 shadow-lg\"\n                    : \"bg-white/40 hover:bg-white/60\"\n                )}\n              />\n            ))}\n          </div>\n        </>\n      )}\n\n      {/* Auto-play Indicator */}\n      <div className=\"absolute top-6 right-6 flex items-center space-x-3 z-10\">\n        <button\n          onClick={() => setIsAutoPlaying(!isAutoPlaying)}\n          className=\"p-2 bg-black/50 hover:bg-black/70 text-white rounded-full backdrop-blur-sm transition-all duration-300\"\n        >\n          {isAutoPlaying ? (\n            <div className=\"w-5 h-5 flex items-center justify-center\">\n              <div className=\"w-2 h-2 bg-green-400 rounded-full animate-pulse\" />\n            </div>\n          ) : (\n            <div className=\"w-5 h-5 flex items-center justify-center\">\n              <div className=\"w-2 h-2 bg-gray-400 rounded-full\" />\n            </div>\n          )}\n        </button>\n      </div>\n    </div>\n  );\n};\n\nexport default EnhancedHeroSection;\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AAAA;AACA;AAPA;;;;;;;;AAwBA,MAAM,sBAA0D,CAAC,EAAE,KAAK,EAAE;IACxE,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACnD,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;QAAE,GAAG;QAAG,GAAG;IAAE;IAEhE,0BAA0B;IAC1B,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,CAAC,iBAAiB,MAAM,MAAM,IAAI,GAAG;QAEzC,MAAM,WAAW,YAAY;YAC3B,gBAAgB,CAAC,OAAS,CAAC,OAAO,CAAC,IAAI,MAAM,MAAM;QACrD,GAAG,QAAQ,2CAA2C;QAEtD,OAAO,IAAM,cAAc;IAC7B,GAAG;QAAC;QAAe,MAAM,MAAM;KAAC;IAEhC,sCAAsC;IACtC,MAAM,kBAAkB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC;QACnC,MAAM,OAAO,EAAE,aAAa,CAAC,qBAAqB;QAClD,iBAAiB;YACf,GAAG,CAAC,EAAE,OAAO,GAAG,KAAK,IAAI,IAAI,KAAK,KAAK;YACvC,GAAG,CAAC,EAAE,OAAO,GAAG,KAAK,GAAG,IAAI,KAAK,MAAM;QACzC;IACF,GAAG,EAAE;IAEL,qBAAqB;IACrB,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,MAAM,MAAM,GAAG,GAAG;YACpB,MAAM,YAAY,CAAC,eAAe,CAAC,IAAI,MAAM,MAAM;YACnD,MAAM,MAAM,IAAI,OAAO,KAAK;YAC5B,IAAI,GAAG,GAAG,CAAA,GAAA,mHAAA,CAAA,cAAW,AAAD,EAAE,KAAK,CAAC,UAAU,CAAC,SAAS;QAClD;IACF,GAAG;QAAC;QAAc;KAAM;IAExB,MAAM,cAAc,KAAK,CAAC,aAAa;IAEvC,IAAI,CAAC,eAAe,MAAM,MAAM,KAAK,GAAG;QACtC,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;kCACb,8OAAC,0MAAA,CAAA,WAAQ;wBAAC,WAAU;;;;;;kCACpB,8OAAC;wBAAG,WAAU;kCAAmC;;;;;;;;;;;;;;;;;IAIzD;IAEA,MAAM,iBAAiB,CAAC;QACtB,gBAAgB;QAChB,iBAAiB;QACjB,WAAW,IAAM,iBAAiB,OAAO,QAAQ,6BAA6B;IAChF;IAEA,MAAM,WAAW,IAAM,eAAe,CAAC,eAAe,CAAC,IAAI,MAAM,MAAM;IACvE,MAAM,WAAW,IAAM,eAAe,CAAC,eAAe,IAAI,MAAM,MAAM,IAAI,MAAM,MAAM;IAEtF,qBACE,8OAAC;QACC,WAAU;QACV,aAAa;QACb,cAAc,IAAM,iBAAiB;QACrC,cAAc,IAAM,iBAAiB;;0BAGrC,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC,yLAAA,CAAA,kBAAe;oBAAC,MAAK;8BACpB,cAAA,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wBAET,SAAS;4BAAE,SAAS;4BAAG,OAAO;wBAAI;wBAClC,SAAS;4BACP,SAAS;4BACT,OAAO;4BACP,GAAG,CAAC,cAAc,CAAC,GAAG,GAAG,IAAI;4BAC7B,GAAG,CAAC,cAAc,CAAC,GAAG,GAAG,IAAI;wBAC/B;wBACA,MAAM;4BAAE,SAAS;4BAAG,OAAO;wBAAK;wBAChC,YAAY;4BAAE,UAAU;4BAAK,MAAM;wBAAY;wBAC/C,WAAU;;0CAEV,8OAAC,6HAAA,CAAA,UAAK;gCACJ,KAAK,CAAA,GAAA,mHAAA,CAAA,cAAW,AAAD,EAAE,YAAY,SAAS;gCACtC,KAAK,YAAY,KAAK;gCACtB,IAAI;gCACJ,WAAU;gCACV,QAAQ;gCACR,QAAQ,IAAM,YAAY;;;;;;0CAG5B,8OAAC;gCAAI,WAAU;;;;;;0CACf,8OAAC;gCAAI,WAAU;;;;;;0CACf,8OAAC;gCAAI,WAAU;;;;;;;uBAvBV;;;;;;;;;;;;;;;0BA6BX,8OAAC;gBAAI,WAAU;0BACZ;uBAAI,MAAM;iBAAI,CAAC,GAAG,CAAC,CAAC,GAAG,kBACtB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wBAET,WAAU;wBACV,SAAS;4BACP,GAAG;gCAAC;gCAAG,KAAK,MAAM,KAAK;gCAAK;6BAAE;4BAC9B,GAAG;gCAAC;gCAAG,KAAK,MAAM,KAAK;gCAAK;6BAAE;4BAC9B,SAAS;gCAAC;gCAAG;gCAAG;6BAAE;wBACpB;wBACA,YAAY;4BACV,UAAU,KAAK,MAAM,KAAK,KAAK;4BAC/B,QAAQ;4BACR,MAAM;wBACR;wBACA,OAAO;4BACL,MAAM,GAAG,KAAK,MAAM,KAAK,IAAI,CAAC,CAAC;4BAC/B,KAAK,GAAG,KAAK,MAAM,KAAK,IAAI,CAAC,CAAC;wBAChC;uBAfK;;;;;;;;;;0BAqBX,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CAEb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gCAET,SAAS;oCAAE,SAAS;oCAAG,GAAG,CAAC;gCAAG;gCAC9B,SAAS;oCAAE,SAAS;oCAAG,GAAG;gCAAE;gCAC5B,YAAY;oCAAE,UAAU;oCAAK,OAAO;gCAAI;gCACxC,WAAU;;kDAGV,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wCACT,SAAS;4CAAE,SAAS;4CAAG,GAAG;wCAAG;wCAC7B,SAAS;4CAAE,SAAS;4CAAG,GAAG;wCAAE;wCAC5B,YAAY;4CAAE,UAAU;4CAAK,OAAO;wCAAI;wCACxC,WAAU;;0DAEV,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC;oDAAK,WAAU;8DACb,YAAY,IAAI,KAAK,UAAU,sBAAsB;;;;;;;;;;;4CAGzD,YAAY,UAAU,IAAI,YAAY,UAAU,IAAI,qBACnD,8OAAC;gDAAI,WAAU;;kEACb,8OAAC,kMAAA,CAAA,OAAI;wDAAC,WAAU;;;;;;kEAChB,8OAAC;wDAAK,WAAU;kEAAoC;;;;;;;;;;;;;;;;;;kDAM1D,8OAAC,0LAAA,CAAA,SAAM,CAAC,EAAE;wCACR,SAAS;4CAAE,SAAS;4CAAG,GAAG;wCAAG;wCAC7B,SAAS;4CAAE,SAAS;4CAAG,GAAG;wCAAE;wCAC5B,YAAY;4CAAE,UAAU;4CAAK,OAAO;wCAAI;wCACxC,WAAU;wCACV,OAAO;4CACL,YAAY;wCACd;kDAEC,YAAY,KAAK;;;;;;kDAIpB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wCACT,SAAS;4CAAE,SAAS;4CAAG,GAAG;wCAAG;wCAC7B,SAAS;4CAAE,SAAS;4CAAG,GAAG;wCAAE;wCAC5B,YAAY;4CAAE,UAAU;4CAAK,OAAO;wCAAI;wCACxC,WAAU;;4CAET,YAAY,IAAI,kBACf,8OAAC;gDAAK,WAAU;0DAAyB,YAAY,IAAI;;;;;;4CAE1D,YAAY,UAAU,kBACrB,8OAAC;gDAAI,WAAU;;kEACb,8OAAC,kMAAA,CAAA,OAAI;wDAAC,WAAU;;;;;;kEAChB,8OAAC;wDAAK,WAAU;kEACb,CAAA,GAAA,mHAAA,CAAA,eAAY,AAAD,EAAE,YAAY,UAAU;;;;;;;;;;;;0DAI1C,8OAAC;gDAAI,WAAU;;kEACb,8OAAC,kNAAA,CAAA,aAAU;wDAAC,WAAU;;;;;;kEACtB,8OAAC;wDAAK,WAAU;kEAA+B;;;;;;;;;;;;;;;;;;oCAKlD,YAAY,WAAW,kBACtB,8OAAC,0LAAA,CAAA,SAAM,CAAC,CAAC;wCACP,SAAS;4CAAE,SAAS;4CAAG,GAAG;wCAAG;wCAC7B,SAAS;4CAAE,SAAS;4CAAG,GAAG;wCAAE;wCAC5B,YAAY;4CAAE,UAAU;4CAAK,OAAO;wCAAI;wCACxC,WAAU;wCACV,OAAO;4CACL,YAAY;wCACd;kDAEC,CAAA,GAAA,mHAAA,CAAA,eAAY,AAAD,EAAE,YAAY,WAAW,EAAE;;;;;;kDAK3C,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wCACT,SAAS;4CAAE,SAAS;4CAAG,GAAG;wCAAG;wCAC7B,SAAS;4CAAE,SAAS;4CAAG,GAAG;wCAAE;wCAC5B,YAAY;4CAAE,UAAU;4CAAK,OAAO;wCAAI;wCACxC,WAAU;;0DAEV,8OAAC,4JAAA,CAAA,UAAI;gDACH,MAAM,CAAC,OAAO,EAAE,YAAY,IAAI,CAAC,CAAC,EAAE,YAAY,MAAM,EAAE;gDACxD,WAAU;;kEAEV,8OAAC;wDAAI,WAAU;;0EACb,8OAAC,kMAAA,CAAA,OAAI;gEAAC,WAAU;;;;;;0EAChB,8OAAC;gEAAK,WAAU;0EAAU;;;;;;;;;;;;kEAE5B,8OAAC;wDAAI,WAAU;;;;;;;;;;;;0DAGjB,8OAAC,4JAAA,CAAA,UAAI;gDACH,MAAM,CAAC,OAAO,EAAE,YAAY,IAAI,CAAC,CAAC,EAAE,YAAY,MAAM,EAAE;gDACxD,WAAU;0DAEV,cAAA,8OAAC;oDAAI,WAAU;;sEACb,8OAAC,kMAAA,CAAA,OAAI;4DAAC,WAAU;;;;;;sEAChB,8OAAC;sEAAK;;;;;;;;;;;;;;;;;;;;;;;;+BAtGP,CAAC,QAAQ,EAAE,cAAc;;;;;0CA6GhC,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gCAET,SAAS;oCAAE,SAAS;oCAAG,GAAG;oCAAI,SAAS;gCAAG;gCAC1C,SAAS;oCAAE,SAAS;oCAAG,GAAG;oCAAG,SAAS;gCAAE;gCACxC,YAAY;oCAAE,UAAU;oCAAG,OAAO;gCAAI;gCACtC,WAAU;0CAEV,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;;;;;sDACf,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,6HAAA,CAAA,UAAK;oDACJ,KAAK,CAAA,GAAA,mHAAA,CAAA,cAAW,AAAD,EAAE,YAAY,SAAS;oDACtC,KAAK,YAAY,KAAK;oDACtB,OAAO;oDACP,QAAQ;oDACR,WAAU;;;;;;8DAEZ,8OAAC;oDAAI,WAAU;;;;;;;;;;;;;;;;;;+BAhBd,CAAC,OAAO,EAAE,cAAc;;;;;;;;;;;;;;;;;;;;;YAyBpC,MAAM,MAAM,GAAG,mBACd;;kCAEE,8OAAC;wBACC,SAAS;wBACT,WAAU;kCAEV,cAAA,8OAAC,oNAAA,CAAA,cAAW;4BAAC,WAAU;;;;;;;;;;;kCAEzB,8OAAC;wBACC,SAAS;wBACT,WAAU;kCAEV,cAAA,8OAAC,sNAAA,CAAA,eAAY;4BAAC,WAAU;;;;;;;;;;;kCAI1B,8OAAC;wBAAI,WAAU;kCACZ,MAAM,GAAG,CAAC,CAAC,GAAG,sBACb,8OAAC;gCAEC,SAAS,IAAM,eAAe;gCAC9B,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,oDACA,UAAU,eACN,iCACA;+BAND;;;;;;;;;;;;0BAef,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBACC,SAAS,IAAM,iBAAiB,CAAC;oBACjC,WAAU;8BAET,8BACC,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;;;;;;;;;6CAGjB,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;AAO7B;uCAEe", "debugId": null}}, {"offset": {"line": 723, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/New%20folder%20%2825%29/streamzen/src/components/TrendingSection.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useRef, useState, useEffect } from 'react';\nimport Link from 'next/link';\nimport Image from 'next/image';\nimport { ChevronLeft, ChevronRight, TrendingUp, Star, Play, Flame } from 'lucide-react';\nimport { motion, useInView } from 'framer-motion';\nimport { cn, getImageUrl, formatRating } from '@/lib/utils';\n\ninterface ContentItem {\n  _id: string;\n  imdbId: string;\n  title: string;\n  year?: number;\n  startYear?: number;\n  posterUrl?: string;\n  imdbRating?: number;\n  description?: string;\n  popularity?: number;\n}\n\ninterface TrendingSectionProps {\n  movies: ContentItem[];\n  series: ContentItem[];\n  className?: string;\n  style?: React.CSSProperties;\n}\n\nconst TrendingSection: React.FC<TrendingSectionProps> = ({\n  movies,\n  series,\n  className,\n  style\n}) => {\n  const scrollContainerRef = useRef<HTMLDivElement>(null);\n  const sectionRef = useRef<HTMLDivElement>(null);\n  const isInView = useInView(sectionRef, { once: true, margin: \"-100px\" });\n  const [canScrollLeft, setCanScrollLeft] = useState(false);\n  const [canScrollRight, setCanScrollRight] = useState(true);\n  const [activeTab, setActiveTab] = useState<'all' | 'movies' | 'series'>('all');\n\n  // Combine and sort content by popularity\n  const allContent = [\n    ...movies.map(item => ({ ...item, type: 'movie' as const })),\n    ...series.map(item => ({ ...item, type: 'series' as const }))\n  ].sort((a, b) => (b.popularity || 0) - (a.popularity || 0));\n\n  const getFilteredContent = () => {\n    switch (activeTab) {\n      case 'movies':\n        return movies.map(item => ({ ...item, type: 'movie' as const }));\n      case 'series':\n        return series.map(item => ({ ...item, type: 'series' as const }));\n      default:\n        return allContent;\n    }\n  };\n\n  const filteredContent = getFilteredContent();\n\n  const checkScrollButtons = () => {\n    if (!scrollContainerRef.current) return;\n\n    const container = scrollContainerRef.current;\n    setCanScrollLeft(container.scrollLeft > 0);\n    setCanScrollRight(\n      container.scrollLeft < container.scrollWidth - container.clientWidth - 1\n    );\n  };\n\n  useEffect(() => {\n    checkScrollButtons();\n    const container = scrollContainerRef.current;\n    if (container) {\n      container.addEventListener('scroll', checkScrollButtons);\n      return () => container.removeEventListener('scroll', checkScrollButtons);\n    }\n  }, [filteredContent]);\n\n  const scroll = (direction: 'left' | 'right') => {\n    if (!scrollContainerRef.current) return;\n\n    const container = scrollContainerRef.current;\n    const scrollAmount = container.clientWidth * 0.8;\n    const targetScroll = direction === 'left' \n      ? container.scrollLeft - scrollAmount\n      : container.scrollLeft + scrollAmount;\n\n    container.scrollTo({\n      left: targetScroll,\n      behavior: 'smooth'\n    });\n  };\n\n  if (filteredContent.length === 0) {\n    return null;\n  }\n\n  return (\n    <motion.section\n      ref={sectionRef}\n      initial={{ opacity: 0, y: 50 }}\n      animate={isInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 50 }}\n      transition={{ duration: 0.8 }}\n      className={cn(\"relative\", className)}\n      style={style}\n    >\n      <div className=\"max-w-[2560px] mx-auto px-6 lg:px-12\">\n        {/* Section Header */}\n        <motion.div\n          initial={{ opacity: 0, y: 30 }}\n          animate={isInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 30 }}\n          transition={{ duration: 0.6, delay: 0.2 }}\n          className=\"flex items-center justify-between mb-8\"\n        >\n          <div className=\"flex items-center space-x-4\">\n            <div className=\"flex items-center space-x-3\">\n              <div className=\"p-3 bg-gradient-to-br from-orange-500 to-red-600 rounded-xl\">\n                <Flame className=\"w-6 h-6 text-white\" />\n              </div>\n              <div>\n                <h2 className=\"text-3xl lg:text-4xl font-black text-white\">\n                  Trending Now\n                </h2>\n                <p className=\"text-gray-400 text-sm\">What everyone's watching</p>\n              </div>\n            </div>\n            <div className=\"hidden sm:flex items-center space-x-1 px-3 py-1 bg-gradient-to-r from-orange-500/20 to-red-600/20 border border-orange-500/30 rounded-full\">\n              <TrendingUp className=\"w-4 h-4 text-orange-400\" />\n              <span className=\"text-orange-400 text-sm font-semibold\">Hot</span>\n            </div>\n          </div>\n\n          {/* Tab Filters */}\n          <div className=\"flex items-center space-x-2 bg-gray-900/50 rounded-xl p-1 backdrop-blur-sm border border-gray-800\">\n            {[\n              { key: 'all', label: 'All', count: allContent.length },\n              { key: 'movies', label: 'Movies', count: movies.length },\n              { key: 'series', label: 'Series', count: series.length }\n            ].map(({ key, label, count }) => (\n              <button\n                key={key}\n                onClick={() => setActiveTab(key as any)}\n                className={cn(\n                  \"px-4 py-2 rounded-lg text-sm font-semibold transition-all duration-300\",\n                  activeTab === key\n                    ? \"bg-gradient-to-r from-orange-500 to-red-600 text-white shadow-lg\"\n                    : \"text-gray-400 hover:text-white hover:bg-gray-800/50\"\n                )}\n              >\n                {label} ({count})\n              </button>\n            ))}\n          </div>\n        </motion.div>\n\n        {/* Content Carousel */}\n        <div className=\"relative group\">\n          {/* Navigation Buttons */}\n          {canScrollLeft && (\n            <motion.button\n              initial={{ opacity: 0 }}\n              animate={{ opacity: 1 }}\n              onClick={() => scroll('left')}\n              className=\"absolute left-0 top-1/2 -translate-y-1/2 z-10 p-3 bg-black/80 hover:bg-black text-white rounded-full backdrop-blur-sm transition-all duration-300 hover:scale-110 shadow-xl opacity-0 group-hover:opacity-100\"\n            >\n              <ChevronLeft className=\"w-6 h-6\" />\n            </motion.button>\n          )}\n\n          {canScrollRight && (\n            <motion.button\n              initial={{ opacity: 0 }}\n              animate={{ opacity: 1 }}\n              onClick={() => scroll('right')}\n              className=\"absolute right-0 top-1/2 -translate-y-1/2 z-10 p-3 bg-black/80 hover:bg-black text-white rounded-full backdrop-blur-sm transition-all duration-300 hover:scale-110 shadow-xl opacity-0 group-hover:opacity-100\"\n            >\n              <ChevronRight className=\"w-6 h-6\" />\n            </motion.button>\n          )}\n\n          {/* Scrollable Content */}\n          <div\n            ref={scrollContainerRef}\n            className=\"flex space-x-6 overflow-x-auto scrollbar-hide pb-4\"\n            style={{ scrollbarWidth: 'none', msOverflowStyle: 'none' }}\n          >\n            {filteredContent.map((item, index) => (\n              <motion.div\n                key={`${item.type}-${item._id}`}\n                initial={{ opacity: 0, y: 30 }}\n                animate={isInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 30 }}\n                transition={{ duration: 0.6, delay: 0.1 * index }}\n                className=\"flex-shrink-0 group\"\n              >\n                <Link href={`/watch/${item.type}/${item.imdbId}`}>\n                  <div className=\"relative w-64 h-96 rounded-2xl overflow-hidden bg-gray-900 group-hover:scale-105 transition-all duration-500 shadow-xl hover:shadow-2xl\">\n                    {/* Trending Badge */}\n                    <div className=\"absolute top-3 left-3 z-10 flex items-center space-x-1 px-2 py-1 bg-gradient-to-r from-orange-500 to-red-600 rounded-full text-xs font-bold text-white\">\n                      <Flame className=\"w-3 h-3\" />\n                      <span>#{index + 1}</span>\n                    </div>\n\n                    {/* Type Badge */}\n                    <div className=\"absolute top-3 right-3 z-10 px-2 py-1 bg-black/70 rounded-full text-xs font-semibold text-white backdrop-blur-sm\">\n                      {item.type === 'movie' ? '🎬' : '📺'}\n                    </div>\n\n                    {/* Poster Image */}\n                    <Image\n                      src={getImageUrl(item.posterUrl)}\n                      alt={item.title}\n                      fill\n                      className=\"object-cover transition-transform duration-500 group-hover:scale-110\"\n                    />\n\n                    {/* Gradient Overlay */}\n                    <div className=\"absolute inset-0 bg-gradient-to-t from-black via-transparent to-transparent opacity-60 group-hover:opacity-80 transition-opacity duration-300\" />\n\n                    {/* Play Button Overlay */}\n                    <div className=\"absolute inset-0 flex items-center justify-center opacity-0 group-hover:opacity-100 transition-all duration-300\">\n                      <div className=\"p-4 bg-white/20 rounded-full backdrop-blur-sm border border-white/30 group-hover:scale-110 transition-transform duration-300\">\n                        <Play className=\"w-8 h-8 text-white fill-current\" />\n                      </div>\n                    </div>\n\n                    {/* Content Info */}\n                    <div className=\"absolute bottom-0 left-0 right-0 p-4 space-y-2\">\n                      <h3 className=\"text-white font-bold text-lg leading-tight line-clamp-2\">\n                        {item.title}\n                      </h3>\n                      \n                      <div className=\"flex items-center justify-between\">\n                        <span className=\"text-gray-300 text-sm\">\n                          {item.year || item.startYear}\n                        </span>\n                        \n                        {item.imdbRating && (\n                          <div className=\"flex items-center space-x-1 px-2 py-1 bg-yellow-500/20 rounded-full\">\n                            <Star className=\"w-3 h-3 text-yellow-400 fill-current\" />\n                            <span className=\"text-yellow-400 text-xs font-bold\">\n                              {formatRating(item.imdbRating)}\n                            </span>\n                          </div>\n                        )}\n                      </div>\n\n                      {/* Popularity Indicator */}\n                      {item.popularity && (\n                        <div className=\"flex items-center space-x-1\">\n                          <TrendingUp className=\"w-3 h-3 text-green-400\" />\n                          <span className=\"text-green-400 text-xs font-semibold\">\n                            {Math.round(item.popularity)} views\n                          </span>\n                        </div>\n                      )}\n                    </div>\n                  </div>\n                </Link>\n              </motion.div>\n            ))}\n          </div>\n        </div>\n\n        {/* View All Button */}\n        <motion.div\n          initial={{ opacity: 0, y: 20 }}\n          animate={isInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 20 }}\n          transition={{ duration: 0.6, delay: 0.4 }}\n          className=\"flex justify-center mt-8\"\n        >\n          <Link\n            href=\"/movies\"\n            className=\"group px-8 py-3 bg-gradient-to-r from-orange-500/20 to-red-600/20 hover:from-orange-500/30 hover:to-red-600/30 border border-orange-500/30 hover:border-orange-500/50 text-orange-400 hover:text-orange-300 font-semibold rounded-xl transition-all duration-300 backdrop-blur-sm\"\n          >\n            <div className=\"flex items-center space-x-2\">\n              <span>Explore All Trending</span>\n              <ChevronRight className=\"w-4 h-4 group-hover:translate-x-1 transition-transform duration-300\" />\n            </div>\n          </Link>\n        </motion.div>\n      </div>\n    </motion.section>\n  );\n};\n\nexport default TrendingSection;\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AAAA;AACA;AAPA;;;;;;;;AA4BA,MAAM,kBAAkD,CAAC,EACvD,MAAM,EACN,MAAM,EACN,SAAS,EACT,KAAK,EACN;IACC,MAAM,qBAAqB,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAkB;IAClD,MAAM,aAAa,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAkB;IAC1C,MAAM,WAAW,CAAA,GAAA,6KAAA,CAAA,YAAS,AAAD,EAAE,YAAY;QAAE,MAAM;QAAM,QAAQ;IAAS;IACtE,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACnD,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACrD,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAA+B;IAExE,yCAAyC;IACzC,MAAM,aAAa;WACd,OAAO,GAAG,CAAC,CAAA,OAAQ,CAAC;gBAAE,GAAG,IAAI;gBAAE,MAAM;YAAiB,CAAC;WACvD,OAAO,GAAG,CAAC,CAAA,OAAQ,CAAC;gBAAE,GAAG,IAAI;gBAAE,MAAM;YAAkB,CAAC;KAC5D,CAAC,IAAI,CAAC,CAAC,GAAG,IAAM,CAAC,EAAE,UAAU,IAAI,CAAC,IAAI,CAAC,EAAE,UAAU,IAAI,CAAC;IAEzD,MAAM,qBAAqB;QACzB,OAAQ;YACN,KAAK;gBACH,OAAO,OAAO,GAAG,CAAC,CAAA,OAAQ,CAAC;wBAAE,GAAG,IAAI;wBAAE,MAAM;oBAAiB,CAAC;YAChE,KAAK;gBACH,OAAO,OAAO,GAAG,CAAC,CAAA,OAAQ,CAAC;wBAAE,GAAG,IAAI;wBAAE,MAAM;oBAAkB,CAAC;YACjE;gBACE,OAAO;QACX;IACF;IAEA,MAAM,kBAAkB;IAExB,MAAM,qBAAqB;QACzB,IAAI,CAAC,mBAAmB,OAAO,EAAE;QAEjC,MAAM,YAAY,mBAAmB,OAAO;QAC5C,iBAAiB,UAAU,UAAU,GAAG;QACxC,kBACE,UAAU,UAAU,GAAG,UAAU,WAAW,GAAG,UAAU,WAAW,GAAG;IAE3E;IAEA,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR;QACA,MAAM,YAAY,mBAAmB,OAAO;QAC5C,IAAI,WAAW;YACb,UAAU,gBAAgB,CAAC,UAAU;YACrC,OAAO,IAAM,UAAU,mBAAmB,CAAC,UAAU;QACvD;IACF,GAAG;QAAC;KAAgB;IAEpB,MAAM,SAAS,CAAC;QACd,IAAI,CAAC,mBAAmB,OAAO,EAAE;QAEjC,MAAM,YAAY,mBAAmB,OAAO;QAC5C,MAAM,eAAe,UAAU,WAAW,GAAG;QAC7C,MAAM,eAAe,cAAc,SAC/B,UAAU,UAAU,GAAG,eACvB,UAAU,UAAU,GAAG;QAE3B,UAAU,QAAQ,CAAC;YACjB,MAAM;YACN,UAAU;QACZ;IACF;IAEA,IAAI,gBAAgB,MAAM,KAAK,GAAG;QAChC,OAAO;IACT;IAEA,qBACE,8OAAC,0LAAA,CAAA,SAAM,CAAC,OAAO;QACb,KAAK;QACL,SAAS;YAAE,SAAS;YAAG,GAAG;QAAG;QAC7B,SAAS,WAAW;YAAE,SAAS;YAAG,GAAG;QAAE,IAAI;YAAE,SAAS;YAAG,GAAG;QAAG;QAC/D,YAAY;YAAE,UAAU;QAAI;QAC5B,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,YAAY;QAC1B,OAAO;kBAEP,cAAA,8OAAC;YAAI,WAAU;;8BAEb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC7B,SAAS,WAAW;wBAAE,SAAS;wBAAG,GAAG;oBAAE,IAAI;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC/D,YAAY;wBAAE,UAAU;wBAAK,OAAO;oBAAI;oBACxC,WAAU;;sCAEV,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC,oMAAA,CAAA,QAAK;gDAAC,WAAU;;;;;;;;;;;sDAEnB,8OAAC;;8DACC,8OAAC;oDAAG,WAAU;8DAA6C;;;;;;8DAG3D,8OAAC;oDAAE,WAAU;8DAAwB;;;;;;;;;;;;;;;;;;8CAGzC,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,kNAAA,CAAA,aAAU;4CAAC,WAAU;;;;;;sDACtB,8OAAC;4CAAK,WAAU;sDAAwC;;;;;;;;;;;;;;;;;;sCAK5D,8OAAC;4BAAI,WAAU;sCACZ;gCACC;oCAAE,KAAK;oCAAO,OAAO;oCAAO,OAAO,WAAW,MAAM;gCAAC;gCACrD;oCAAE,KAAK;oCAAU,OAAO;oCAAU,OAAO,OAAO,MAAM;gCAAC;gCACvD;oCAAE,KAAK;oCAAU,OAAO;oCAAU,OAAO,OAAO,MAAM;gCAAC;6BACxD,CAAC,GAAG,CAAC,CAAC,EAAE,GAAG,EAAE,KAAK,EAAE,KAAK,EAAE,iBAC1B,8OAAC;oCAEC,SAAS,IAAM,aAAa;oCAC5B,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,0EACA,cAAc,MACV,qEACA;;wCAGL;wCAAM;wCAAG;wCAAM;;mCATX;;;;;;;;;;;;;;;;8BAgBb,8OAAC;oBAAI,WAAU;;wBAEZ,+BACC,8OAAC,0LAAA,CAAA,SAAM,CAAC,MAAM;4BACZ,SAAS;gCAAE,SAAS;4BAAE;4BACtB,SAAS;gCAAE,SAAS;4BAAE;4BACtB,SAAS,IAAM,OAAO;4BACtB,WAAU;sCAEV,cAAA,8OAAC,oNAAA,CAAA,cAAW;gCAAC,WAAU;;;;;;;;;;;wBAI1B,gCACC,8OAAC,0LAAA,CAAA,SAAM,CAAC,MAAM;4BACZ,SAAS;gCAAE,SAAS;4BAAE;4BACtB,SAAS;gCAAE,SAAS;4BAAE;4BACtB,SAAS,IAAM,OAAO;4BACtB,WAAU;sCAEV,cAAA,8OAAC,sNAAA,CAAA,eAAY;gCAAC,WAAU;;;;;;;;;;;sCAK5B,8OAAC;4BACC,KAAK;4BACL,WAAU;4BACV,OAAO;gCAAE,gBAAgB;gCAAQ,iBAAiB;4BAAO;sCAExD,gBAAgB,GAAG,CAAC,CAAC,MAAM,sBAC1B,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oCAET,SAAS;wCAAE,SAAS;wCAAG,GAAG;oCAAG;oCAC7B,SAAS,WAAW;wCAAE,SAAS;wCAAG,GAAG;oCAAE,IAAI;wCAAE,SAAS;wCAAG,GAAG;oCAAG;oCAC/D,YAAY;wCAAE,UAAU;wCAAK,OAAO,MAAM;oCAAM;oCAChD,WAAU;8CAEV,cAAA,8OAAC,4JAAA,CAAA,UAAI;wCAAC,MAAM,CAAC,OAAO,EAAE,KAAK,IAAI,CAAC,CAAC,EAAE,KAAK,MAAM,EAAE;kDAC9C,cAAA,8OAAC;4CAAI,WAAU;;8DAEb,8OAAC;oDAAI,WAAU;;sEACb,8OAAC,oMAAA,CAAA,QAAK;4DAAC,WAAU;;;;;;sEACjB,8OAAC;;gEAAK;gEAAE,QAAQ;;;;;;;;;;;;;8DAIlB,8OAAC;oDAAI,WAAU;8DACZ,KAAK,IAAI,KAAK,UAAU,OAAO;;;;;;8DAIlC,8OAAC,6HAAA,CAAA,UAAK;oDACJ,KAAK,CAAA,GAAA,mHAAA,CAAA,cAAW,AAAD,EAAE,KAAK,SAAS;oDAC/B,KAAK,KAAK,KAAK;oDACf,IAAI;oDACJ,WAAU;;;;;;8DAIZ,8OAAC;oDAAI,WAAU;;;;;;8DAGf,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC;wDAAI,WAAU;kEACb,cAAA,8OAAC,kMAAA,CAAA,OAAI;4DAAC,WAAU;;;;;;;;;;;;;;;;8DAKpB,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAG,WAAU;sEACX,KAAK,KAAK;;;;;;sEAGb,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEAAK,WAAU;8EACb,KAAK,IAAI,IAAI,KAAK,SAAS;;;;;;gEAG7B,KAAK,UAAU,kBACd,8OAAC;oEAAI,WAAU;;sFACb,8OAAC,kMAAA,CAAA,OAAI;4EAAC,WAAU;;;;;;sFAChB,8OAAC;4EAAK,WAAU;sFACb,CAAA,GAAA,mHAAA,CAAA,eAAY,AAAD,EAAE,KAAK,UAAU;;;;;;;;;;;;;;;;;;wDAOpC,KAAK,UAAU,kBACd,8OAAC;4DAAI,WAAU;;8EACb,8OAAC,kNAAA,CAAA,aAAU;oEAAC,WAAU;;;;;;8EACtB,8OAAC;oEAAK,WAAU;;wEACb,KAAK,KAAK,CAAC,KAAK,UAAU;wEAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;mCA/DpC,GAAG,KAAK,IAAI,CAAC,CAAC,EAAE,KAAK,GAAG,EAAE;;;;;;;;;;;;;;;;8BA4EvC,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC7B,SAAS,WAAW;wBAAE,SAAS;wBAAG,GAAG;oBAAE,IAAI;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC/D,YAAY;wBAAE,UAAU;wBAAK,OAAO;oBAAI;oBACxC,WAAU;8BAEV,cAAA,8OAAC,4JAAA,CAAA,UAAI;wBACH,MAAK;wBACL,WAAU;kCAEV,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;8CAAK;;;;;;8CACN,8OAAC,sNAAA,CAAA,eAAY;oCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOtC;uCAEe", "debugId": null}}, {"offset": {"line": 1306, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/New%20folder%20%2825%29/streamzen/src/components/GenreSpotlight.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useRef, useState, useEffect } from 'react';\nimport Link from 'next/link';\nimport Image from 'next/image';\nimport { ChevronLeft, ChevronRight, Star, Play, Zap, Heart, Smile } from 'lucide-react';\nimport { motion, useInView } from 'framer-motion';\nimport { cn, getImageUrl, formatRating } from '@/lib/utils';\n\ninterface ContentItem {\n  _id: string;\n  imdbId: string;\n  title: string;\n  year?: number;\n  startYear?: number;\n  posterUrl?: string;\n  imdbRating?: number;\n  description?: string;\n}\n\ninterface GenreSpotlightProps {\n  actionMovies: ContentItem[];\n  dramaSeries: ContentItem[];\n  comedyMovies: ContentItem[];\n  className?: string;\n  style?: React.CSSProperties;\n}\n\nconst GenreSpotlight: React.FC<GenreSpotlightProps> = ({\n  actionMovies,\n  dramaSeries,\n  comedyMovies,\n  className,\n  style\n}) => {\n  const sectionRef = useRef<HTMLDivElement>(null);\n  const isInView = useInView(sectionRef, { once: true, margin: \"-100px\" });\n\n  const genres = [\n    {\n      title: 'Action & Adventure',\n      subtitle: 'Heart-pounding thrills',\n      icon: Zap,\n      color: 'from-red-500 to-orange-600',\n      bgColor: 'from-red-500/20 to-orange-600/20',\n      borderColor: 'border-red-500/30',\n      textColor: 'text-red-400',\n      content: actionMovies.map(item => ({ ...item, type: 'movie' as const })),\n      href: '/movies?genre=Action'\n    },\n    {\n      title: 'Drama Series',\n      subtitle: 'Compelling stories',\n      icon: Heart,\n      color: 'from-purple-500 to-pink-600',\n      bgColor: 'from-purple-500/20 to-pink-600/20',\n      borderColor: 'border-purple-500/30',\n      textColor: 'text-purple-400',\n      content: dramaSeries.map(item => ({ ...item, type: 'series' as const })),\n      href: '/series?genre=Drama'\n    },\n    {\n      title: 'Comedy Gold',\n      subtitle: 'Laugh out loud',\n      icon: Smile,\n      color: 'from-yellow-500 to-green-600',\n      bgColor: 'from-yellow-500/20 to-green-600/20',\n      borderColor: 'border-yellow-500/30',\n      textColor: 'text-yellow-400',\n      content: comedyMovies.map(item => ({ ...item, type: 'movie' as const })),\n      href: '/movies?genre=Comedy'\n    }\n  ];\n\n  const GenreCarousel = ({ genre, index }: { genre: typeof genres[0], index: number }) => {\n    const scrollRef = useRef<HTMLDivElement>(null);\n    const [canScrollLeft, setCanScrollLeft] = useState(false);\n    const [canScrollRight, setCanScrollRight] = useState(true);\n\n    const checkScrollButtons = () => {\n      if (!scrollRef.current) return;\n      const container = scrollRef.current;\n      setCanScrollLeft(container.scrollLeft > 0);\n      setCanScrollRight(\n        container.scrollLeft < container.scrollWidth - container.clientWidth - 1\n      );\n    };\n\n    useEffect(() => {\n      checkScrollButtons();\n      const container = scrollRef.current;\n      if (container) {\n        container.addEventListener('scroll', checkScrollButtons);\n        return () => container.removeEventListener('scroll', checkScrollButtons);\n      }\n    }, [genre.content]);\n\n    const scroll = (direction: 'left' | 'right') => {\n      if (!scrollRef.current) return;\n      const container = scrollRef.current;\n      const scrollAmount = container.clientWidth * 0.7;\n      const targetScroll = direction === 'left' \n        ? container.scrollLeft - scrollAmount\n        : container.scrollLeft + scrollAmount;\n\n      container.scrollTo({\n        left: targetScroll,\n        behavior: 'smooth'\n      });\n    };\n\n    if (genre.content.length === 0) return null;\n\n    return (\n      <motion.div\n        initial={{ opacity: 0, y: 50 }}\n        animate={isInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 50 }}\n        transition={{ duration: 0.8, delay: 0.2 * index }}\n        className=\"space-y-6\"\n      >\n        {/* Genre Header */}\n        <div className=\"flex items-center justify-between\">\n          <div className=\"flex items-center space-x-4\">\n            <div className={cn(\"p-3 bg-gradient-to-br rounded-xl\", genre.color)}>\n              <genre.icon className=\"w-6 h-6 text-white\" />\n            </div>\n            <div>\n              <h3 className=\"text-2xl lg:text-3xl font-black text-white\">\n                {genre.title}\n              </h3>\n              <p className=\"text-gray-400 text-sm\">{genre.subtitle}</p>\n            </div>\n          </div>\n\n          <Link\n            href={genre.href}\n            className={cn(\n              \"group px-4 py-2 bg-gradient-to-r border rounded-xl transition-all duration-300 backdrop-blur-sm hover:scale-105\",\n              genre.bgColor,\n              genre.borderColor,\n              genre.textColor\n            )}\n          >\n            <div className=\"flex items-center space-x-2\">\n              <span className=\"font-semibold\">View All</span>\n              <ChevronRight className=\"w-4 h-4 group-hover:translate-x-1 transition-transform duration-300\" />\n            </div>\n          </Link>\n        </div>\n\n        {/* Content Carousel */}\n        <div className=\"relative group\">\n          {/* Navigation Buttons */}\n          {canScrollLeft && (\n            <button\n              onClick={() => scroll('left')}\n              className=\"absolute left-0 top-1/2 -translate-y-1/2 z-10 p-2 bg-black/80 hover:bg-black text-white rounded-full backdrop-blur-sm transition-all duration-300 hover:scale-110 shadow-xl opacity-0 group-hover:opacity-100\"\n            >\n              <ChevronLeft className=\"w-5 h-5\" />\n            </button>\n          )}\n\n          {canScrollRight && (\n            <button\n              onClick={() => scroll('right')}\n              className=\"absolute right-0 top-1/2 -translate-y-1/2 z-10 p-2 bg-black/80 hover:bg-black text-white rounded-full backdrop-blur-sm transition-all duration-300 hover:scale-110 shadow-xl opacity-0 group-hover:opacity-100\"\n            >\n              <ChevronRight className=\"w-5 h-5\" />\n            </button>\n          )}\n\n          {/* Scrollable Content */}\n          <div\n            ref={scrollRef}\n            className=\"flex space-x-4 overflow-x-auto scrollbar-hide pb-2\"\n            style={{ scrollbarWidth: 'none', msOverflowStyle: 'none' }}\n          >\n            {genre.content.slice(0, 12).map((item, itemIndex) => (\n              <motion.div\n                key={`${genre.title}-${item._id}`}\n                initial={{ opacity: 0, scale: 0.9 }}\n                animate={isInView ? { opacity: 1, scale: 1 } : { opacity: 0, scale: 0.9 }}\n                transition={{ duration: 0.5, delay: 0.1 * itemIndex }}\n                className=\"flex-shrink-0 group\"\n              >\n                <Link href={`/watch/${item.type}/${item.imdbId}`}>\n                  <div className=\"relative w-48 h-72 rounded-xl overflow-hidden bg-gray-900 group-hover:scale-105 transition-all duration-500 shadow-lg hover:shadow-2xl\">\n                    {/* Type Badge */}\n                    <div className=\"absolute top-2 right-2 z-10 px-2 py-1 bg-black/70 rounded-full text-xs font-semibold text-white backdrop-blur-sm\">\n                      {item.type === 'movie' ? '🎬' : '📺'}\n                    </div>\n\n                    {/* Poster Image */}\n                    <Image\n                      src={getImageUrl(item.posterUrl)}\n                      alt={item.title}\n                      fill\n                      className=\"object-cover transition-transform duration-500 group-hover:scale-110\"\n                    />\n\n                    {/* Gradient Overlay */}\n                    <div className=\"absolute inset-0 bg-gradient-to-t from-black via-transparent to-transparent opacity-60 group-hover:opacity-80 transition-opacity duration-300\" />\n\n                    {/* Play Button Overlay */}\n                    <div className=\"absolute inset-0 flex items-center justify-center opacity-0 group-hover:opacity-100 transition-all duration-300\">\n                      <div className=\"p-3 bg-white/20 rounded-full backdrop-blur-sm border border-white/30 group-hover:scale-110 transition-transform duration-300\">\n                        <Play className=\"w-6 h-6 text-white fill-current\" />\n                      </div>\n                    </div>\n\n                    {/* Content Info */}\n                    <div className=\"absolute bottom-0 left-0 right-0 p-3 space-y-1\">\n                      <h4 className=\"text-white font-bold text-sm leading-tight line-clamp-2\">\n                        {item.title}\n                      </h4>\n                      \n                      <div className=\"flex items-center justify-between\">\n                        <span className=\"text-gray-300 text-xs\">\n                          {item.year || item.startYear}\n                        </span>\n                        \n                        {item.imdbRating && (\n                          <div className=\"flex items-center space-x-1 px-1.5 py-0.5 bg-yellow-500/20 rounded-full\">\n                            <Star className=\"w-2.5 h-2.5 text-yellow-400 fill-current\" />\n                            <span className=\"text-yellow-400 text-xs font-bold\">\n                              {formatRating(item.imdbRating)}\n                            </span>\n                          </div>\n                        )}\n                      </div>\n                    </div>\n\n                    {/* Genre-specific accent */}\n                    <div className={cn(\n                      \"absolute top-0 left-0 w-full h-1 bg-gradient-to-r\",\n                      genre.color\n                    )} />\n                  </div>\n                </Link>\n              </motion.div>\n            ))}\n          </div>\n        </div>\n      </motion.div>\n    );\n  };\n\n  return (\n    <motion.section\n      ref={sectionRef}\n      initial={{ opacity: 0, y: 50 }}\n      animate={isInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 50 }}\n      transition={{ duration: 0.8 }}\n      className={cn(\"relative\", className)}\n      style={style}\n    >\n      <div className=\"max-w-[2560px] mx-auto px-6 lg:px-12\">\n        {/* Section Header */}\n        <motion.div\n          initial={{ opacity: 0, y: 30 }}\n          animate={isInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 30 }}\n          transition={{ duration: 0.6, delay: 0.2 }}\n          className=\"text-center mb-12\"\n        >\n          <h2 className=\"text-4xl lg:text-5xl font-black text-white mb-4\">\n            Genre Spotlights\n          </h2>\n          <p className=\"text-gray-400 text-lg max-w-2xl mx-auto\">\n            Discover your next favorite from our curated genre collections\n          </p>\n        </motion.div>\n\n        {/* Genre Carousels */}\n        <div className=\"space-y-16\">\n          {genres.map((genre, index) => (\n            <GenreCarousel key={genre.title} genre={genre} index={index} />\n          ))}\n        </div>\n\n        {/* Explore All Genres */}\n        <motion.div\n          initial={{ opacity: 0, y: 20 }}\n          animate={isInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 20 }}\n          transition={{ duration: 0.6, delay: 0.8 }}\n          className=\"flex justify-center mt-16\"\n        >\n          <Link\n            href=\"/movies\"\n            className=\"group px-8 py-4 bg-gradient-to-r from-gray-800/50 to-gray-700/50 hover:from-gray-700/50 hover:to-gray-600/50 border border-gray-700 hover:border-gray-600 text-white font-semibold rounded-xl transition-all duration-300 backdrop-blur-sm hover:scale-105\"\n          >\n            <div className=\"flex items-center space-x-3\">\n              <span className=\"text-lg\">Explore All Genres</span>\n              <ChevronRight className=\"w-5 h-5 group-hover:translate-x-1 transition-transform duration-300\" />\n            </div>\n          </Link>\n        </motion.div>\n      </div>\n    </motion.section>\n  );\n};\n\nexport default GenreSpotlight;\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AAAA;AACA;AAPA;;;;;;;;AA4BA,MAAM,iBAAgD,CAAC,EACrD,YAAY,EACZ,WAAW,EACX,YAAY,EACZ,SAAS,EACT,KAAK,EACN;IACC,MAAM,aAAa,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAkB;IAC1C,MAAM,WAAW,CAAA,GAAA,6KAAA,CAAA,YAAS,AAAD,EAAE,YAAY;QAAE,MAAM;QAAM,QAAQ;IAAS;IAEtE,MAAM,SAAS;QACb;YACE,OAAO;YACP,UAAU;YACV,MAAM,gMAAA,CAAA,MAAG;YACT,OAAO;YACP,SAAS;YACT,aAAa;YACb,WAAW;YACX,SAAS,aAAa,GAAG,CAAC,CAAA,OAAQ,CAAC;oBAAE,GAAG,IAAI;oBAAE,MAAM;gBAAiB,CAAC;YACtE,MAAM;QACR;QACA;YACE,OAAO;YACP,UAAU;YACV,MAAM,oMAAA,CAAA,QAAK;YACX,OAAO;YACP,SAAS;YACT,aAAa;YACb,WAAW;YACX,SAAS,YAAY,GAAG,CAAC,CAAA,OAAQ,CAAC;oBAAE,GAAG,IAAI;oBAAE,MAAM;gBAAkB,CAAC;YACtE,MAAM;QACR;QACA;YACE,OAAO;YACP,UAAU;YACV,MAAM,oMAAA,CAAA,QAAK;YACX,OAAO;YACP,SAAS;YACT,aAAa;YACb,WAAW;YACX,SAAS,aAAa,GAAG,CAAC,CAAA,OAAQ,CAAC;oBAAE,GAAG,IAAI;oBAAE,MAAM;gBAAiB,CAAC;YACtE,MAAM;QACR;KACD;IAED,MAAM,gBAAgB,CAAC,EAAE,KAAK,EAAE,KAAK,EAA8C;QACjF,MAAM,YAAY,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAkB;QACzC,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;QACnD,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;QAErD,MAAM,qBAAqB;YACzB,IAAI,CAAC,UAAU,OAAO,EAAE;YACxB,MAAM,YAAY,UAAU,OAAO;YACnC,iBAAiB,UAAU,UAAU,GAAG;YACxC,kBACE,UAAU,UAAU,GAAG,UAAU,WAAW,GAAG,UAAU,WAAW,GAAG;QAE3E;QAEA,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;YACR;YACA,MAAM,YAAY,UAAU,OAAO;YACnC,IAAI,WAAW;gBACb,UAAU,gBAAgB,CAAC,UAAU;gBACrC,OAAO,IAAM,UAAU,mBAAmB,CAAC,UAAU;YACvD;QACF,GAAG;YAAC,MAAM,OAAO;SAAC;QAElB,MAAM,SAAS,CAAC;YACd,IAAI,CAAC,UAAU,OAAO,EAAE;YACxB,MAAM,YAAY,UAAU,OAAO;YACnC,MAAM,eAAe,UAAU,WAAW,GAAG;YAC7C,MAAM,eAAe,cAAc,SAC/B,UAAU,UAAU,GAAG,eACvB,UAAU,UAAU,GAAG;YAE3B,UAAU,QAAQ,CAAC;gBACjB,MAAM;gBACN,UAAU;YACZ;QACF;QAEA,IAAI,MAAM,OAAO,CAAC,MAAM,KAAK,GAAG,OAAO;QAEvC,qBACE,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;YACT,SAAS;gBAAE,SAAS;gBAAG,GAAG;YAAG;YAC7B,SAAS,WAAW;gBAAE,SAAS;gBAAG,GAAG;YAAE,IAAI;gBAAE,SAAS;gBAAG,GAAG;YAAG;YAC/D,YAAY;gBAAE,UAAU;gBAAK,OAAO,MAAM;YAAM;YAChD,WAAU;;8BAGV,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,oCAAoC,MAAM,KAAK;8CAChE,cAAA,8OAAC,MAAM,IAAI;wCAAC,WAAU;;;;;;;;;;;8CAExB,8OAAC;;sDACC,8OAAC;4CAAG,WAAU;sDACX,MAAM,KAAK;;;;;;sDAEd,8OAAC;4CAAE,WAAU;sDAAyB,MAAM,QAAQ;;;;;;;;;;;;;;;;;;sCAIxD,8OAAC,4JAAA,CAAA,UAAI;4BACH,MAAM,MAAM,IAAI;4BAChB,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,mHACA,MAAM,OAAO,EACb,MAAM,WAAW,EACjB,MAAM,SAAS;sCAGjB,cAAA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAK,WAAU;kDAAgB;;;;;;kDAChC,8OAAC,sNAAA,CAAA,eAAY;wCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;8BAM9B,8OAAC;oBAAI,WAAU;;wBAEZ,+BACC,8OAAC;4BACC,SAAS,IAAM,OAAO;4BACtB,WAAU;sCAEV,cAAA,8OAAC,oNAAA,CAAA,cAAW;gCAAC,WAAU;;;;;;;;;;;wBAI1B,gCACC,8OAAC;4BACC,SAAS,IAAM,OAAO;4BACtB,WAAU;sCAEV,cAAA,8OAAC,sNAAA,CAAA,eAAY;gCAAC,WAAU;;;;;;;;;;;sCAK5B,8OAAC;4BACC,KAAK;4BACL,WAAU;4BACV,OAAO;gCAAE,gBAAgB;gCAAQ,iBAAiB;4BAAO;sCAExD,MAAM,OAAO,CAAC,KAAK,CAAC,GAAG,IAAI,GAAG,CAAC,CAAC,MAAM,0BACrC,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oCAET,SAAS;wCAAE,SAAS;wCAAG,OAAO;oCAAI;oCAClC,SAAS,WAAW;wCAAE,SAAS;wCAAG,OAAO;oCAAE,IAAI;wCAAE,SAAS;wCAAG,OAAO;oCAAI;oCACxE,YAAY;wCAAE,UAAU;wCAAK,OAAO,MAAM;oCAAU;oCACpD,WAAU;8CAEV,cAAA,8OAAC,4JAAA,CAAA,UAAI;wCAAC,MAAM,CAAC,OAAO,EAAE,KAAK,IAAI,CAAC,CAAC,EAAE,KAAK,MAAM,EAAE;kDAC9C,cAAA,8OAAC;4CAAI,WAAU;;8DAEb,8OAAC;oDAAI,WAAU;8DACZ,KAAK,IAAI,KAAK,UAAU,OAAO;;;;;;8DAIlC,8OAAC,6HAAA,CAAA,UAAK;oDACJ,KAAK,CAAA,GAAA,mHAAA,CAAA,cAAW,AAAD,EAAE,KAAK,SAAS;oDAC/B,KAAK,KAAK,KAAK;oDACf,IAAI;oDACJ,WAAU;;;;;;8DAIZ,8OAAC;oDAAI,WAAU;;;;;;8DAGf,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC;wDAAI,WAAU;kEACb,cAAA,8OAAC,kMAAA,CAAA,OAAI;4DAAC,WAAU;;;;;;;;;;;;;;;;8DAKpB,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAG,WAAU;sEACX,KAAK,KAAK;;;;;;sEAGb,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEAAK,WAAU;8EACb,KAAK,IAAI,IAAI,KAAK,SAAS;;;;;;gEAG7B,KAAK,UAAU,kBACd,8OAAC;oEAAI,WAAU;;sFACb,8OAAC,kMAAA,CAAA,OAAI;4EAAC,WAAU;;;;;;sFAChB,8OAAC;4EAAK,WAAU;sFACb,CAAA,GAAA,mHAAA,CAAA,eAAY,AAAD,EAAE,KAAK,UAAU;;;;;;;;;;;;;;;;;;;;;;;;8DAQvC,8OAAC;oDAAI,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACf,qDACA,MAAM,KAAK;;;;;;;;;;;;;;;;;mCAxDZ,GAAG,MAAM,KAAK,CAAC,CAAC,EAAE,KAAK,GAAG,EAAE;;;;;;;;;;;;;;;;;;;;;;IAkE/C;IAEA,qBACE,8OAAC,0LAAA,CAAA,SAAM,CAAC,OAAO;QACb,KAAK;QACL,SAAS;YAAE,SAAS;YAAG,GAAG;QAAG;QAC7B,SAAS,WAAW;YAAE,SAAS;YAAG,GAAG;QAAE,IAAI;YAAE,SAAS;YAAG,GAAG;QAAG;QAC/D,YAAY;YAAE,UAAU;QAAI;QAC5B,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,YAAY;QAC1B,OAAO;kBAEP,cAAA,8OAAC;YAAI,WAAU;;8BAEb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC7B,SAAS,WAAW;wBAAE,SAAS;wBAAG,GAAG;oBAAE,IAAI;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC/D,YAAY;wBAAE,UAAU;wBAAK,OAAO;oBAAI;oBACxC,WAAU;;sCAEV,8OAAC;4BAAG,WAAU;sCAAkD;;;;;;sCAGhE,8OAAC;4BAAE,WAAU;sCAA0C;;;;;;;;;;;;8BAMzD,8OAAC;oBAAI,WAAU;8BACZ,OAAO,GAAG,CAAC,CAAC,OAAO,sBAClB,8OAAC;4BAAgC,OAAO;4BAAO,OAAO;2BAAlC,MAAM,KAAK;;;;;;;;;;8BAKnC,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC7B,SAAS,WAAW;wBAAE,SAAS;wBAAG,GAAG;oBAAE,IAAI;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC/D,YAAY;wBAAE,UAAU;wBAAK,OAAO;oBAAI;oBACxC,WAAU;8BAEV,cAAA,8OAAC,4JAAA,CAAA,UAAI;wBACH,MAAK;wBACL,WAAU;kCAEV,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAK,WAAU;8CAAU;;;;;;8CAC1B,8OAAC,sNAAA,CAAA,eAAY;oCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOtC;uCAEe", "debugId": null}}, {"offset": {"line": 1874, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/New%20folder%20%2825%29/streamzen/src/components/DecadeCollection.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useRef, useState, useEffect } from 'react';\nimport Link from 'next/link';\nimport Image from 'next/image';\nimport { ChevronLeft, ChevronRight, Star, Play, Calendar, Clock } from 'lucide-react';\nimport { motion, useInView } from 'framer-motion';\nimport { cn, getImageUrl, formatRating } from '@/lib/utils';\n\ninterface ContentItem {\n  _id: string;\n  imdbId: string;\n  title: string;\n  year?: number;\n  startYear?: number;\n  posterUrl?: string;\n  imdbRating?: number;\n  description?: string;\n}\n\ninterface DecadeCollectionProps {\n  modernContent: ContentItem[];\n  classicContent: ContentItem[];\n  className?: string;\n  style?: React.CSSProperties;\n}\n\nconst DecadeCollection: React.FC<DecadeCollectionProps> = ({\n  modernContent,\n  classicContent,\n  className,\n  style\n}) => {\n  const sectionRef = useRef<HTMLDivElement>(null);\n  const isInView = useInView(sectionRef, { once: true, margin: \"-100px\" });\n\n  const decades = [\n    {\n      title: 'Modern Masterpieces',\n      subtitle: '2020s - Latest & Greatest',\n      period: '2020s',\n      icon: Clock,\n      color: 'from-blue-500 to-cyan-600',\n      bgColor: 'from-blue-500/20 to-cyan-600/20',\n      borderColor: 'border-blue-500/30',\n      textColor: 'text-blue-400',\n      content: modernContent.map(item => ({ ...item, type: 'movie' as const })),\n      href: '/movies?year=2020'\n    },\n    {\n      title: 'Timeless Classics',\n      subtitle: '2000s-2010s - Golden Era',\n      period: '2000s-2010s',\n      icon: Calendar,\n      color: 'from-amber-500 to-orange-600',\n      bgColor: 'from-amber-500/20 to-orange-600/20',\n      borderColor: 'border-amber-500/30',\n      textColor: 'text-amber-400',\n      content: classicContent.map(item => ({ ...item, type: 'movie' as const })),\n      href: '/movies?year=2010'\n    }\n  ];\n\n  const DecadeCarousel = ({ decade, index }: { decade: typeof decades[0], index: number }) => {\n    const scrollRef = useRef<HTMLDivElement>(null);\n    const [canScrollLeft, setCanScrollLeft] = useState(false);\n    const [canScrollRight, setCanScrollRight] = useState(true);\n\n    const checkScrollButtons = () => {\n      if (!scrollRef.current) return;\n      const container = scrollRef.current;\n      setCanScrollLeft(container.scrollLeft > 0);\n      setCanScrollRight(\n        container.scrollLeft < container.scrollWidth - container.clientWidth - 1\n      );\n    };\n\n    useEffect(() => {\n      checkScrollButtons();\n      const container = scrollRef.current;\n      if (container) {\n        container.addEventListener('scroll', checkScrollButtons);\n        return () => container.removeEventListener('scroll', checkScrollButtons);\n      }\n    }, [decade.content]);\n\n    const scroll = (direction: 'left' | 'right') => {\n      if (!scrollRef.current) return;\n      const container = scrollRef.current;\n      const scrollAmount = container.clientWidth * 0.7;\n      const targetScroll = direction === 'left' \n        ? container.scrollLeft - scrollAmount\n        : container.scrollLeft + scrollAmount;\n\n      container.scrollTo({\n        left: targetScroll,\n        behavior: 'smooth'\n      });\n    };\n\n    if (decade.content.length === 0) return null;\n\n    return (\n      <motion.div\n        initial={{ opacity: 0, y: 50 }}\n        animate={isInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 50 }}\n        transition={{ duration: 0.8, delay: 0.2 * index }}\n        className=\"space-y-6\"\n      >\n        {/* Decade Header */}\n        <div className=\"flex items-center justify-between\">\n          <div className=\"flex items-center space-x-4\">\n            <div className={cn(\"p-3 bg-gradient-to-br rounded-xl\", decade.color)}>\n              <decade.icon className=\"w-6 h-6 text-white\" />\n            </div>\n            <div>\n              <h3 className=\"text-2xl lg:text-3xl font-black text-white\">\n                {decade.title}\n              </h3>\n              <p className=\"text-gray-400 text-sm\">{decade.subtitle}</p>\n            </div>\n          </div>\n\n          <div className=\"flex items-center space-x-3\">\n            <div className={cn(\n              \"px-3 py-1 bg-gradient-to-r border rounded-full text-sm font-semibold\",\n              decade.bgColor,\n              decade.borderColor,\n              decade.textColor\n            )}>\n              {decade.period}\n            </div>\n            <Link\n              href={decade.href}\n              className={cn(\n                \"group px-4 py-2 bg-gradient-to-r border rounded-xl transition-all duration-300 backdrop-blur-sm hover:scale-105\",\n                decade.bgColor,\n                decade.borderColor,\n                decade.textColor\n              )}\n            >\n              <div className=\"flex items-center space-x-2\">\n                <span className=\"font-semibold\">View All</span>\n                <ChevronRight className=\"w-4 h-4 group-hover:translate-x-1 transition-transform duration-300\" />\n              </div>\n            </Link>\n          </div>\n        </div>\n\n        {/* Content Carousel */}\n        <div className=\"relative group\">\n          {/* Navigation Buttons */}\n          {canScrollLeft && (\n            <button\n              onClick={() => scroll('left')}\n              className=\"absolute left-0 top-1/2 -translate-y-1/2 z-10 p-2 bg-black/80 hover:bg-black text-white rounded-full backdrop-blur-sm transition-all duration-300 hover:scale-110 shadow-xl opacity-0 group-hover:opacity-100\"\n            >\n              <ChevronLeft className=\"w-5 h-5\" />\n            </button>\n          )}\n\n          {canScrollRight && (\n            <button\n              onClick={() => scroll('right')}\n              className=\"absolute right-0 top-1/2 -translate-y-1/2 z-10 p-2 bg-black/80 hover:bg-black text-white rounded-full backdrop-blur-sm transition-all duration-300 hover:scale-110 shadow-xl opacity-0 group-hover:opacity-100\"\n            >\n              <ChevronRight className=\"w-5 h-5\" />\n            </button>\n          )}\n\n          {/* Scrollable Content */}\n          <div\n            ref={scrollRef}\n            className=\"flex space-x-4 overflow-x-auto scrollbar-hide pb-2\"\n            style={{ scrollbarWidth: 'none', msOverflowStyle: 'none' }}\n          >\n            {decade.content.slice(0, 15).map((item, itemIndex) => (\n              <motion.div\n                key={`${decade.title}-${item._id}`}\n                initial={{ opacity: 0, scale: 0.9 }}\n                animate={isInView ? { opacity: 1, scale: 1 } : { opacity: 0, scale: 0.9 }}\n                transition={{ duration: 0.5, delay: 0.1 * itemIndex }}\n                className=\"flex-shrink-0 group\"\n              >\n                <Link href={`/watch/${item.type}/${item.imdbId}`}>\n                  <div className=\"relative w-52 h-78 rounded-xl overflow-hidden bg-gray-900 group-hover:scale-105 transition-all duration-500 shadow-lg hover:shadow-2xl\">\n                    {/* Year Badge */}\n                    <div className=\"absolute top-2 left-2 z-10 px-2 py-1 bg-black/70 rounded-full text-xs font-semibold text-white backdrop-blur-sm\">\n                      {item.year || item.startYear}\n                    </div>\n\n                    {/* Type Badge */}\n                    <div className=\"absolute top-2 right-2 z-10 px-2 py-1 bg-black/70 rounded-full text-xs font-semibold text-white backdrop-blur-sm\">\n                      🎬\n                    </div>\n\n                    {/* Poster Image */}\n                    <Image\n                      src={getImageUrl(item.posterUrl)}\n                      alt={item.title}\n                      fill\n                      className=\"object-cover transition-transform duration-500 group-hover:scale-110\"\n                    />\n\n                    {/* Gradient Overlay */}\n                    <div className=\"absolute inset-0 bg-gradient-to-t from-black via-transparent to-transparent opacity-60 group-hover:opacity-80 transition-opacity duration-300\" />\n\n                    {/* Play Button Overlay */}\n                    <div className=\"absolute inset-0 flex items-center justify-center opacity-0 group-hover:opacity-100 transition-all duration-300\">\n                      <div className=\"p-3 bg-white/20 rounded-full backdrop-blur-sm border border-white/30 group-hover:scale-110 transition-transform duration-300\">\n                        <Play className=\"w-6 h-6 text-white fill-current\" />\n                      </div>\n                    </div>\n\n                    {/* Content Info */}\n                    <div className=\"absolute bottom-0 left-0 right-0 p-3 space-y-2\">\n                      <h4 className=\"text-white font-bold text-sm leading-tight line-clamp-2\">\n                        {item.title}\n                      </h4>\n                      \n                      <div className=\"flex items-center justify-between\">\n                        <span className={cn(\"text-xs font-semibold\", decade.textColor)}>\n                          {decade.period}\n                        </span>\n                        \n                        {item.imdbRating && (\n                          <div className=\"flex items-center space-x-1 px-1.5 py-0.5 bg-yellow-500/20 rounded-full\">\n                            <Star className=\"w-2.5 h-2.5 text-yellow-400 fill-current\" />\n                            <span className=\"text-yellow-400 text-xs font-bold\">\n                              {formatRating(item.imdbRating)}\n                            </span>\n                          </div>\n                        )}\n                      </div>\n                    </div>\n\n                    {/* Decade-specific accent */}\n                    <div className={cn(\n                      \"absolute bottom-0 left-0 w-full h-1 bg-gradient-to-r\",\n                      decade.color\n                    )} />\n                  </div>\n                </Link>\n              </motion.div>\n            ))}\n          </div>\n        </div>\n      </motion.div>\n    );\n  };\n\n  return (\n    <motion.section\n      ref={sectionRef}\n      initial={{ opacity: 0, y: 50 }}\n      animate={isInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 50 }}\n      transition={{ duration: 0.8 }}\n      className={cn(\"relative\", className)}\n      style={style}\n    >\n      <div className=\"max-w-[2560px] mx-auto px-6 lg:px-12\">\n        {/* Section Header */}\n        <motion.div\n          initial={{ opacity: 0, y: 30 }}\n          animate={isInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 30 }}\n          transition={{ duration: 0.6, delay: 0.2 }}\n          className=\"text-center mb-12\"\n        >\n          <h2 className=\"text-4xl lg:text-5xl font-black text-white mb-4\">\n            Through the Decades\n          </h2>\n          <p className=\"text-gray-400 text-lg max-w-2xl mx-auto\">\n            Journey through cinema history with our curated decade collections\n          </p>\n        </motion.div>\n\n        {/* Decade Carousels */}\n        <div className=\"space-y-16\">\n          {decades.map((decade, index) => (\n            <DecadeCarousel key={decade.title} decade={decade} index={index} />\n          ))}\n        </div>\n\n        {/* Timeline Visual */}\n        <motion.div\n          initial={{ opacity: 0, y: 20 }}\n          animate={isInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 20 }}\n          transition={{ duration: 0.6, delay: 0.8 }}\n          className=\"flex justify-center mt-16\"\n        >\n          <div className=\"flex items-center space-x-4 px-8 py-4 bg-gray-900/50 rounded-xl border border-gray-800 backdrop-blur-sm\">\n            <Calendar className=\"w-5 h-5 text-gray-400\" />\n            <span className=\"text-gray-300 font-semibold\">Explore Cinema Through Time</span>\n          </div>\n        </motion.div>\n      </div>\n    </motion.section>\n  );\n};\n\nexport default DecadeCollection;\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AAAA;AACA;AAPA;;;;;;;;AA2BA,MAAM,mBAAoD,CAAC,EACzD,aAAa,EACb,cAAc,EACd,SAAS,EACT,KAAK,EACN;IACC,MAAM,aAAa,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAkB;IAC1C,MAAM,WAAW,CAAA,GAAA,6KAAA,CAAA,YAAS,AAAD,EAAE,YAAY;QAAE,MAAM;QAAM,QAAQ;IAAS;IAEtE,MAAM,UAAU;QACd;YACE,OAAO;YACP,UAAU;YACV,QAAQ;YACR,MAAM,oMAAA,CAAA,QAAK;YACX,OAAO;YACP,SAAS;YACT,aAAa;YACb,WAAW;YACX,SAAS,cAAc,GAAG,CAAC,CAAA,OAAQ,CAAC;oBAAE,GAAG,IAAI;oBAAE,MAAM;gBAAiB,CAAC;YACvE,MAAM;QACR;QACA;YACE,OAAO;YACP,UAAU;YACV,QAAQ;YACR,MAAM,0MAAA,CAAA,WAAQ;YACd,OAAO;YACP,SAAS;YACT,aAAa;YACb,WAAW;YACX,SAAS,eAAe,GAAG,CAAC,CAAA,OAAQ,CAAC;oBAAE,GAAG,IAAI;oBAAE,MAAM;gBAAiB,CAAC;YACxE,MAAM;QACR;KACD;IAED,MAAM,iBAAiB,CAAC,EAAE,MAAM,EAAE,KAAK,EAAgD;QACrF,MAAM,YAAY,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAkB;QACzC,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;QACnD,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;QAErD,MAAM,qBAAqB;YACzB,IAAI,CAAC,UAAU,OAAO,EAAE;YACxB,MAAM,YAAY,UAAU,OAAO;YACnC,iBAAiB,UAAU,UAAU,GAAG;YACxC,kBACE,UAAU,UAAU,GAAG,UAAU,WAAW,GAAG,UAAU,WAAW,GAAG;QAE3E;QAEA,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;YACR;YACA,MAAM,YAAY,UAAU,OAAO;YACnC,IAAI,WAAW;gBACb,UAAU,gBAAgB,CAAC,UAAU;gBACrC,OAAO,IAAM,UAAU,mBAAmB,CAAC,UAAU;YACvD;QACF,GAAG;YAAC,OAAO,OAAO;SAAC;QAEnB,MAAM,SAAS,CAAC;YACd,IAAI,CAAC,UAAU,OAAO,EAAE;YACxB,MAAM,YAAY,UAAU,OAAO;YACnC,MAAM,eAAe,UAAU,WAAW,GAAG;YAC7C,MAAM,eAAe,cAAc,SAC/B,UAAU,UAAU,GAAG,eACvB,UAAU,UAAU,GAAG;YAE3B,UAAU,QAAQ,CAAC;gBACjB,MAAM;gBACN,UAAU;YACZ;QACF;QAEA,IAAI,OAAO,OAAO,CAAC,MAAM,KAAK,GAAG,OAAO;QAExC,qBACE,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;YACT,SAAS;gBAAE,SAAS;gBAAG,GAAG;YAAG;YAC7B,SAAS,WAAW;gBAAE,SAAS;gBAAG,GAAG;YAAE,IAAI;gBAAE,SAAS;gBAAG,GAAG;YAAG;YAC/D,YAAY;gBAAE,UAAU;gBAAK,OAAO,MAAM;YAAM;YAChD,WAAU;;8BAGV,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,oCAAoC,OAAO,KAAK;8CACjE,cAAA,8OAAC,OAAO,IAAI;wCAAC,WAAU;;;;;;;;;;;8CAEzB,8OAAC;;sDACC,8OAAC;4CAAG,WAAU;sDACX,OAAO,KAAK;;;;;;sDAEf,8OAAC;4CAAE,WAAU;sDAAyB,OAAO,QAAQ;;;;;;;;;;;;;;;;;;sCAIzD,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACf,wEACA,OAAO,OAAO,EACd,OAAO,WAAW,EAClB,OAAO,SAAS;8CAEf,OAAO,MAAM;;;;;;8CAEhB,8OAAC,4JAAA,CAAA,UAAI;oCACH,MAAM,OAAO,IAAI;oCACjB,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,mHACA,OAAO,OAAO,EACd,OAAO,WAAW,EAClB,OAAO,SAAS;8CAGlB,cAAA,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAK,WAAU;0DAAgB;;;;;;0DAChC,8OAAC,sNAAA,CAAA,eAAY;gDAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8BAOhC,8OAAC;oBAAI,WAAU;;wBAEZ,+BACC,8OAAC;4BACC,SAAS,IAAM,OAAO;4BACtB,WAAU;sCAEV,cAAA,8OAAC,oNAAA,CAAA,cAAW;gCAAC,WAAU;;;;;;;;;;;wBAI1B,gCACC,8OAAC;4BACC,SAAS,IAAM,OAAO;4BACtB,WAAU;sCAEV,cAAA,8OAAC,sNAAA,CAAA,eAAY;gCAAC,WAAU;;;;;;;;;;;sCAK5B,8OAAC;4BACC,KAAK;4BACL,WAAU;4BACV,OAAO;gCAAE,gBAAgB;gCAAQ,iBAAiB;4BAAO;sCAExD,OAAO,OAAO,CAAC,KAAK,CAAC,GAAG,IAAI,GAAG,CAAC,CAAC,MAAM,0BACtC,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oCAET,SAAS;wCAAE,SAAS;wCAAG,OAAO;oCAAI;oCAClC,SAAS,WAAW;wCAAE,SAAS;wCAAG,OAAO;oCAAE,IAAI;wCAAE,SAAS;wCAAG,OAAO;oCAAI;oCACxE,YAAY;wCAAE,UAAU;wCAAK,OAAO,MAAM;oCAAU;oCACpD,WAAU;8CAEV,cAAA,8OAAC,4JAAA,CAAA,UAAI;wCAAC,MAAM,CAAC,OAAO,EAAE,KAAK,IAAI,CAAC,CAAC,EAAE,KAAK,MAAM,EAAE;kDAC9C,cAAA,8OAAC;4CAAI,WAAU;;8DAEb,8OAAC;oDAAI,WAAU;8DACZ,KAAK,IAAI,IAAI,KAAK,SAAS;;;;;;8DAI9B,8OAAC;oDAAI,WAAU;8DAAmH;;;;;;8DAKlI,8OAAC,6HAAA,CAAA,UAAK;oDACJ,KAAK,CAAA,GAAA,mHAAA,CAAA,cAAW,AAAD,EAAE,KAAK,SAAS;oDAC/B,KAAK,KAAK,KAAK;oDACf,IAAI;oDACJ,WAAU;;;;;;8DAIZ,8OAAC;oDAAI,WAAU;;;;;;8DAGf,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC;wDAAI,WAAU;kEACb,cAAA,8OAAC,kMAAA,CAAA,OAAI;4DAAC,WAAU;;;;;;;;;;;;;;;;8DAKpB,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAG,WAAU;sEACX,KAAK,KAAK;;;;;;sEAGb,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEAAK,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,yBAAyB,OAAO,SAAS;8EAC1D,OAAO,MAAM;;;;;;gEAGf,KAAK,UAAU,kBACd,8OAAC;oEAAI,WAAU;;sFACb,8OAAC,kMAAA,CAAA,OAAI;4EAAC,WAAU;;;;;;sFAChB,8OAAC;4EAAK,WAAU;sFACb,CAAA,GAAA,mHAAA,CAAA,eAAY,AAAD,EAAE,KAAK,UAAU;;;;;;;;;;;;;;;;;;;;;;;;8DAQvC,8OAAC;oDAAI,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACf,wDACA,OAAO,KAAK;;;;;;;;;;;;;;;;;mCA7Db,GAAG,OAAO,KAAK,CAAC,CAAC,EAAE,KAAK,GAAG,EAAE;;;;;;;;;;;;;;;;;;;;;;IAuEhD;IAEA,qBACE,8OAAC,0LAAA,CAAA,SAAM,CAAC,OAAO;QACb,KAAK;QACL,SAAS;YAAE,SAAS;YAAG,GAAG;QAAG;QAC7B,SAAS,WAAW;YAAE,SAAS;YAAG,GAAG;QAAE,IAAI;YAAE,SAAS;YAAG,GAAG;QAAG;QAC/D,YAAY;YAAE,UAAU;QAAI;QAC5B,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,YAAY;QAC1B,OAAO;kBAEP,cAAA,8OAAC;YAAI,WAAU;;8BAEb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC7B,SAAS,WAAW;wBAAE,SAAS;wBAAG,GAAG;oBAAE,IAAI;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC/D,YAAY;wBAAE,UAAU;wBAAK,OAAO;oBAAI;oBACxC,WAAU;;sCAEV,8OAAC;4BAAG,WAAU;sCAAkD;;;;;;sCAGhE,8OAAC;4BAAE,WAAU;sCAA0C;;;;;;;;;;;;8BAMzD,8OAAC;oBAAI,WAAU;8BACZ,QAAQ,GAAG,CAAC,CAAC,QAAQ,sBACpB,8OAAC;4BAAkC,QAAQ;4BAAQ,OAAO;2BAArC,OAAO,KAAK;;;;;;;;;;8BAKrC,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC7B,SAAS,WAAW;wBAAE,SAAS;wBAAG,GAAG;oBAAE,IAAI;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC/D,YAAY;wBAAE,UAAU;wBAAK,OAAO;oBAAI;oBACxC,WAAU;8BAEV,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,0MAAA,CAAA,WAAQ;gCAAC,WAAU;;;;;;0CACpB,8OAAC;gCAAK,WAAU;0CAA8B;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAM1D;uCAEe", "debugId": null}}, {"offset": {"line": 2446, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/New%20folder%20%2825%29/streamzen/src/components/GlobalCinema.tsx"], "sourcesContent": ["'use client';\n\nimport React from 'react';\nimport Link from 'next/link';\nimport Image from 'next/image';\nimport { Globe, Star, Play } from 'lucide-react';\nimport { motion, useInView } from 'framer-motion';\nimport { cn, getImageUrl, formatRating } from '@/lib/utils';\n\ninterface ContentItem {\n  _id: string;\n  imdbId: string;\n  title: string;\n  year?: number;\n  startYear?: number;\n  posterUrl?: string;\n  imdbRating?: number;\n  language?: string;\n  country?: string;\n}\n\ninterface GlobalCinemaProps {\n  movies: ContentItem[];\n  series: ContentItem[];\n  className?: string;\n  style?: React.CSSProperties;\n}\n\nconst GlobalCinema: React.FC<GlobalCinemaProps> = ({\n  movies,\n  series,\n  className,\n  style\n}) => {\n  const sectionRef = React.useRef<HTMLDivElement>(null);\n  const isInView = useInView(sectionRef, { once: true, margin: \"-100px\" });\n\n  const allContent = [\n    ...movies.map(item => ({ ...item, type: 'movie' as const })),\n    ...series.map(item => ({ ...item, type: 'series' as const }))\n  ].slice(0, 8);\n\n  if (allContent.length === 0) return null;\n\n  return (\n    <motion.section\n      ref={sectionRef}\n      initial={{ opacity: 0, y: 50 }}\n      animate={isInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 50 }}\n      transition={{ duration: 0.8 }}\n      className={cn(\"relative\", className)}\n      style={style}\n    >\n      <div className=\"max-w-[2560px] mx-auto px-6 lg:px-12\">\n        <motion.div\n          initial={{ opacity: 0, y: 30 }}\n          animate={isInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 30 }}\n          transition={{ duration: 0.6, delay: 0.2 }}\n          className=\"text-center mb-12\"\n        >\n          <div className=\"flex items-center justify-center space-x-3 mb-4\">\n            <div className=\"p-3 bg-gradient-to-br from-green-500 to-teal-600 rounded-xl\">\n              <Globe className=\"w-6 h-6 text-white\" />\n            </div>\n            <h2 className=\"text-4xl lg:text-5xl font-black text-white\">\n              Global Cinema\n            </h2>\n          </div>\n          <p className=\"text-gray-400 text-lg max-w-2xl mx-auto\">\n            Discover stories from around the world\n          </p>\n        </motion.div>\n\n        <div className=\"grid grid-cols-2 md:grid-cols-4 gap-4\">\n          {allContent.map((item, index) => (\n            <motion.div\n              key={item._id}\n              initial={{ opacity: 0, scale: 0.9 }}\n              animate={isInView ? { opacity: 1, scale: 1 } : { opacity: 0, scale: 0.9 }}\n              transition={{ duration: 0.5, delay: 0.1 * index }}\n              className=\"group\"\n            >\n              <Link href={`/watch/${item.type}/${item.imdbId}`}>\n                <div className=\"relative w-full h-72 rounded-xl overflow-hidden bg-gray-900 group-hover:scale-105 transition-all duration-500 shadow-lg hover:shadow-2xl\">\n                  <Image\n                    src={getImageUrl(item.posterUrl)}\n                    alt={item.title}\n                    fill\n                    className=\"object-cover transition-transform duration-500 group-hover:scale-110\"\n                  />\n                  <div className=\"absolute inset-0 bg-gradient-to-t from-black via-transparent to-transparent opacity-60 group-hover:opacity-80 transition-opacity duration-300\" />\n                  \n                  <div className=\"absolute inset-0 flex items-center justify-center opacity-0 group-hover:opacity-100 transition-all duration-300\">\n                    <div className=\"p-3 bg-white/20 rounded-full backdrop-blur-sm border border-white/30\">\n                      <Play className=\"w-6 h-6 text-white fill-current\" />\n                    </div>\n                  </div>\n\n                  <div className=\"absolute bottom-0 left-0 right-0 p-3 space-y-1\">\n                    <h4 className=\"text-white font-bold text-sm leading-tight line-clamp-2\">\n                      {item.title}\n                    </h4>\n                    <div className=\"flex items-center justify-between\">\n                      <span className=\"text-gray-300 text-xs\">\n                        {item.country || item.language}\n                      </span>\n                      {item.imdbRating && (\n                        <div className=\"flex items-center space-x-1 px-1.5 py-0.5 bg-yellow-500/20 rounded-full\">\n                          <Star className=\"w-2.5 h-2.5 text-yellow-400 fill-current\" />\n                          <span className=\"text-yellow-400 text-xs font-bold\">\n                            {formatRating(item.imdbRating)}\n                          </span>\n                        </div>\n                      )}\n                    </div>\n                  </div>\n                </div>\n              </Link>\n            </motion.div>\n          ))}\n        </div>\n      </div>\n    </motion.section>\n  );\n};\n\nexport default GlobalCinema;\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAAA;AAAA;AACA;AAAA;AACA;AAPA;;;;;;;;AA4BA,MAAM,eAA4C,CAAC,EACjD,MAAM,EACN,MAAM,EACN,SAAS,EACT,KAAK,EACN;IACC,MAAM,aAAa,qMAAA,CAAA,UAAK,CAAC,MAAM,CAAiB;IAChD,MAAM,WAAW,CAAA,GAAA,6KAAA,CAAA,YAAS,AAAD,EAAE,YAAY;QAAE,MAAM;QAAM,QAAQ;IAAS;IAEtE,MAAM,aAAa;WACd,OAAO,GAAG,CAAC,CAAA,OAAQ,CAAC;gBAAE,GAAG,IAAI;gBAAE,MAAM;YAAiB,CAAC;WACvD,OAAO,GAAG,CAAC,CAAA,OAAQ,CAAC;gBAAE,GAAG,IAAI;gBAAE,MAAM;YAAkB,CAAC;KAC5D,CAAC,KAAK,CAAC,GAAG;IAEX,IAAI,WAAW,MAAM,KAAK,GAAG,OAAO;IAEpC,qBACE,8OAAC,0LAAA,CAAA,SAAM,CAAC,OAAO;QACb,KAAK;QACL,SAAS;YAAE,SAAS;YAAG,GAAG;QAAG;QAC7B,SAAS,WAAW;YAAE,SAAS;YAAG,GAAG;QAAE,IAAI;YAAE,SAAS;YAAG,GAAG;QAAG;QAC/D,YAAY;YAAE,UAAU;QAAI;QAC5B,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,YAAY;QAC1B,OAAO;kBAEP,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC7B,SAAS,WAAW;wBAAE,SAAS;wBAAG,GAAG;oBAAE,IAAI;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC/D,YAAY;wBAAE,UAAU;wBAAK,OAAO;oBAAI;oBACxC,WAAU;;sCAEV,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC,oMAAA,CAAA,QAAK;wCAAC,WAAU;;;;;;;;;;;8CAEnB,8OAAC;oCAAG,WAAU;8CAA6C;;;;;;;;;;;;sCAI7D,8OAAC;4BAAE,WAAU;sCAA0C;;;;;;;;;;;;8BAKzD,8OAAC;oBAAI,WAAU;8BACZ,WAAW,GAAG,CAAC,CAAC,MAAM,sBACrB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4BAET,SAAS;gCAAE,SAAS;gCAAG,OAAO;4BAAI;4BAClC,SAAS,WAAW;gCAAE,SAAS;gCAAG,OAAO;4BAAE,IAAI;gCAAE,SAAS;gCAAG,OAAO;4BAAI;4BACxE,YAAY;gCAAE,UAAU;gCAAK,OAAO,MAAM;4BAAM;4BAChD,WAAU;sCAEV,cAAA,8OAAC,4JAAA,CAAA,UAAI;gCAAC,MAAM,CAAC,OAAO,EAAE,KAAK,IAAI,CAAC,CAAC,EAAE,KAAK,MAAM,EAAE;0CAC9C,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,6HAAA,CAAA,UAAK;4CACJ,KAAK,CAAA,GAAA,mHAAA,CAAA,cAAW,AAAD,EAAE,KAAK,SAAS;4CAC/B,KAAK,KAAK,KAAK;4CACf,IAAI;4CACJ,WAAU;;;;;;sDAEZ,8OAAC;4CAAI,WAAU;;;;;;sDAEf,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC,kMAAA,CAAA,OAAI;oDAAC,WAAU;;;;;;;;;;;;;;;;sDAIpB,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAG,WAAU;8DACX,KAAK,KAAK;;;;;;8DAEb,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAK,WAAU;sEACb,KAAK,OAAO,IAAI,KAAK,QAAQ;;;;;;wDAE/B,KAAK,UAAU,kBACd,8OAAC;4DAAI,WAAU;;8EACb,8OAAC,kMAAA,CAAA,OAAI;oEAAC,WAAU;;;;;;8EAChB,8OAAC;oEAAK,WAAU;8EACb,CAAA,GAAA,mHAAA,CAAA,eAAY,AAAD,EAAE,KAAK,UAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;2BAlCtC,KAAK,GAAG;;;;;;;;;;;;;;;;;;;;;AAgD3B;uCAEe", "debugId": null}}, {"offset": {"line": 2728, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/New%20folder%20%2825%29/streamzen/src/components/ContentCard.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState, useRef, useEffect } from 'react';\nimport Link from 'next/link';\nimport Image from 'next/image';\nimport { Play, Star, Calendar, Info, Plus, Sparkles, TrendingUp, Clock, Eye } from 'lucide-react';\nimport { motion, useMotionValue, useSpring, useTransform } from 'framer-motion';\nimport { cn, getImageUrl, formatRating, truncateText } from '@/lib/utils';\n\ninterface ContentCardProps {\n  id: string;\n  imdbId: string;\n  title: string;\n  year?: number;\n  posterUrl?: string;\n  imdbRating?: number;\n  description?: string;\n  type: 'movie' | 'series' | 'episode';\n  season?: number;\n  episode?: number;\n  seriesTitle?: string;\n  seriesPosterUrl?: string; // For episodes to use series poster\n  className?: string;\n}\n\nconst ContentCard: React.FC<ContentCardProps> = ({\n  id,\n  imdbId,\n  title,\n  year,\n  posterUrl,\n  imdbRating,\n  description,\n  type,\n  season,\n  episode,\n  seriesTitle,\n  seriesPosterUrl,\n  className\n}) => {\n  const [isHovered, setIsHovered] = useState(false);\n  const [isLoaded, setIsLoaded] = useState(false);\n  const cardRef = useRef<HTMLDivElement>(null);\n\n  // Advanced mouse tracking for 3D effects\n  const mouseX = useMotionValue(0);\n  const mouseY = useMotionValue(0);\n  const rotateX = useSpring(useTransform(mouseY, [-0.5, 0.5], [10, -10]), { stiffness: 300, damping: 30 });\n  const rotateY = useSpring(useTransform(mouseX, [-0.5, 0.5], [-10, 10]), { stiffness: 300, damping: 30 });\n\n  const href = type === 'movie'\n    ? `/watch/movie/${imdbId}`\n    : type === 'series'\n    ? `/watch/series/${imdbId}`\n    : `/watch/series/${imdbId}?season=${season}&episode=${episode}`;\n\n  const displayTitle = type === 'episode' ? seriesTitle : title;\n  const subtitle = type === 'episode' ? `S${season}E${episode}: ${title}` : year ? `${year}` : '';\n\n  // Use series poster for episodes, fallback to episode poster, then to placeholder\n  const displayPosterUrl = type === 'episode' ? (seriesPosterUrl || posterUrl) : posterUrl;\n\n  // Enhanced mouse tracking for 3D tilt effect\n  const handleMouseMove = (e: React.MouseEvent<HTMLDivElement>) => {\n    if (!cardRef.current) return;\n\n    const rect = cardRef.current.getBoundingClientRect();\n    const centerX = rect.left + rect.width / 2;\n    const centerY = rect.top + rect.height / 2;\n\n    const mouseXPos = (e.clientX - centerX) / (rect.width / 2);\n    const mouseYPos = (e.clientY - centerY) / (rect.height / 2);\n\n    mouseX.set(mouseXPos);\n    mouseY.set(mouseYPos);\n  };\n\n  const handleMouseLeave = () => {\n    setIsHovered(false);\n    mouseX.set(0);\n    mouseY.set(0);\n  };\n\n  // Quality indicator based on rating\n  const getQualityBadge = () => {\n    if (!imdbRating) return null;\n\n    if (imdbRating >= 8.5) return { label: 'Masterpiece', color: 'from-yellow-400 to-orange-500', icon: Sparkles };\n    if (imdbRating >= 8.0) return { label: 'Excellent', color: 'from-green-400 to-emerald-500', icon: Star };\n    if (imdbRating >= 7.0) return { label: 'Great', color: 'from-blue-400 to-cyan-500', icon: TrendingUp };\n    if (imdbRating >= 6.0) return { label: 'Good', color: 'from-purple-400 to-pink-500', icon: Eye };\n    return null;\n  };\n\n  const qualityBadge = getQualityBadge();\n\n  return (\n    <Link href={href} className=\"block\">\n      <motion.div\n        ref={cardRef}\n        className={cn(\n          'group relative cursor-pointer perspective-1000',\n          className\n        )}\n        style={{\n          rotateX,\n          rotateY,\n          transformStyle: 'preserve-3d'\n        }}\n        onMouseMove={handleMouseMove}\n        onMouseEnter={() => setIsHovered(true)}\n        onMouseLeave={handleMouseLeave}\n        whileHover={{\n          scale: 1.05,\n          y: -8,\n          transition: { duration: 0.3, ease: \"easeOut\" }\n        }}\n        whileTap={{ scale: 0.98 }}\n      >\n        {/* Main Card Container with Glassmorphism */}\n        <div className=\"relative aspect-[2/3] bg-gradient-to-br from-gray-900/90 to-black/90 overflow-hidden rounded-3xl backdrop-blur-xl border border-white/10 hover:border-white/20 transition-all duration-500 shadow-2xl hover:shadow-4xl\">\n\n          {/* Animated Background Gradient */}\n          <div className=\"absolute inset-0 bg-gradient-to-br from-blue-600/10 via-purple-600/10 to-pink-600/10 opacity-0 group-hover:opacity-100 transition-opacity duration-500\" />\n\n          {/* Poster Image with Enhanced Effects */}\n          <div className=\"relative w-full h-full overflow-hidden\">\n            <Image\n              src={getImageUrl(displayPosterUrl)}\n              alt={displayTitle || 'Content poster'}\n              fill\n              className={cn(\n                'object-cover transition-all duration-700 ease-out',\n                isHovered ? 'scale-110 brightness-75 blur-[1px]' : 'scale-100 brightness-90'\n              )}\n              sizes=\"(max-width: 768px) 50vw, (max-width: 1200px) 33vw, 25vw\"\n              priority={false}\n              onLoad={() => setIsLoaded(true)}\n            />\n\n            {/* Loading shimmer effect */}\n            {!isLoaded && (\n              <div className=\"absolute inset-0 bg-gradient-to-r from-gray-800 via-gray-700 to-gray-800 animate-pulse\" />\n            )}\n          </div>\n\n          {/* Multi-layer Gradient Overlays */}\n          <div className=\"absolute inset-0 bg-gradient-to-t from-black/95 via-black/30 to-transparent\" />\n          <div className={cn(\n            'absolute inset-0 bg-gradient-to-br from-transparent via-transparent to-black/50 transition-opacity duration-500',\n            isHovered ? 'opacity-100' : 'opacity-60'\n          )} />\n\n          {/* Glassmorphism Inner Glow */}\n          <div className={cn(\n            'absolute inset-0 rounded-3xl transition-all duration-500',\n            isHovered ? 'shadow-[inset_0_0_60px_rgba(255,255,255,0.15)]' : 'shadow-[inset_0_0_20px_rgba(255,255,255,0.05)]'\n          )} />\n\n          {/* Enhanced Quality Badge */}\n          {qualityBadge && (\n            <motion.div\n              className=\"absolute top-3 right-3 z-20\"\n              initial={{ opacity: 0, scale: 0.8 }}\n              animate={{ opacity: 1, scale: 1 }}\n              transition={{ delay: 0.2 }}\n            >\n              <div className={cn(\n                'px-3 py-1.5 rounded-full flex items-center space-x-1.5 backdrop-blur-xl border border-white/20 shadow-lg',\n                `bg-gradient-to-r ${qualityBadge.color}`\n              )}>\n                <qualityBadge.icon size={12} className=\"text-white\" />\n                <span className=\"text-white text-xs font-bold\">{formatRating(imdbRating!)}</span>\n              </div>\n            </motion.div>\n          )}\n\n          {/* Type Badge with Enhanced Design */}\n          <motion.div\n            className=\"absolute top-3 left-3 z-20\"\n            initial={{ opacity: 0, x: -20 }}\n            animate={{ opacity: 1, x: 0 }}\n            transition={{ delay: 0.1 }}\n          >\n            <div className=\"px-3 py-1.5 rounded-full backdrop-blur-xl bg-black/40 border border-white/20 shadow-lg\">\n              <span className=\"text-white text-xs font-bold uppercase tracking-wider\">\n                {type === 'episode' ? '📺 Episode' : type === 'movie' ? '🎬 Movie' : '📺 Series'}\n              </span>\n            </div>\n          </motion.div>\n\n          {/* Trending Indicator for High Ratings */}\n          {imdbRating && imdbRating >= 8.5 && (\n            <motion.div\n              className=\"absolute top-3 left-1/2 -translate-x-1/2 z-20\"\n              initial={{ opacity: 0, y: -20 }}\n              animate={{ opacity: 1, y: 0 }}\n              transition={{ delay: 0.3 }}\n            >\n              <div className=\"px-2 py-1 rounded-full backdrop-blur-xl bg-gradient-to-r from-yellow-400/20 to-orange-500/20 border border-yellow-400/30 shadow-lg\">\n                <Sparkles size={12} className=\"text-yellow-400\" />\n              </div>\n            </motion.div>\n          )}\n\n          {/* Enhanced Hover Play Button with Pulse Animation */}\n          <motion.div\n            className=\"absolute inset-0 flex items-center justify-center pointer-events-none z-30\"\n            initial={{ opacity: 0, scale: 0.5 }}\n            animate={isHovered ? { opacity: 1, scale: 1 } : { opacity: 0, scale: 0.5 }}\n            transition={{ duration: 0.3, ease: \"easeOut\" }}\n          >\n            <motion.div\n              className=\"relative\"\n              whileHover={{ scale: 1.1 }}\n              whileTap={{ scale: 0.95 }}\n            >\n              {/* Pulsing background ring */}\n              <div className=\"absolute inset-0 w-20 h-20 bg-red-500/30 rounded-full animate-ping\" />\n\n              {/* Main play button */}\n              <div className=\"relative w-20 h-20 bg-gradient-to-br from-red-500 to-red-600 rounded-full flex items-center justify-center shadow-2xl border-2 border-white/30 backdrop-blur-sm\">\n                <Play size={28} className=\"text-white fill-current ml-1\" />\n              </div>\n            </motion.div>\n          </motion.div>\n\n          {/* Enhanced Bottom Content Info with Slide Animation */}\n          <motion.div\n            className=\"absolute bottom-0 left-0 right-0 p-4 pointer-events-none\"\n            initial={{ y: 20, opacity: 0.8 }}\n            animate={isHovered ? { y: 0, opacity: 1 } : { y: 20, opacity: 0.8 }}\n            transition={{ duration: 0.3 }}\n          >\n            <div className=\"space-y-2\">\n              <h3 className=\"text-white font-bold text-base line-clamp-2 leading-tight drop-shadow-2xl\">\n                {displayTitle}\n              </h3>\n\n              {subtitle && (\n                <p className=\"text-gray-200 text-sm font-medium drop-shadow-lg\">\n                  {subtitle}\n                </p>\n              )}\n\n              {/* Additional info on hover */}\n              {isHovered && description && (\n                <motion.p\n                  className=\"text-gray-300 text-xs line-clamp-2 leading-relaxed drop-shadow-md\"\n                  initial={{ opacity: 0, y: 10 }}\n                  animate={{ opacity: 1, y: 0 }}\n                  transition={{ delay: 0.2 }}\n                >\n                  {truncateText(description, 80)}\n                </motion.p>\n              )}\n            </div>\n          </motion.div>\n\n          {/* Premium Border Glow with Enhanced Effects */}\n          <div className={cn(\n            'absolute inset-0 rounded-3xl border transition-all duration-500 pointer-events-none',\n            isHovered\n              ? 'border-white/30 shadow-[0_0_40px_rgba(255,255,255,0.2),inset_0_0_40px_rgba(255,255,255,0.1)]'\n              : 'border-white/10 shadow-[0_0_20px_rgba(255,255,255,0.05)]'\n          )} />\n\n          {/* Floating particles effect on hover */}\n          {isHovered && (\n            <div className=\"absolute inset-0 overflow-hidden rounded-3xl pointer-events-none\">\n              {[...Array(6)].map((_, i) => (\n                <motion.div\n                  key={i}\n                  className=\"absolute w-1 h-1 bg-white/40 rounded-full\"\n                  initial={{\n                    x: Math.random() * 100 + '%',\n                    y: '100%',\n                    opacity: 0\n                  }}\n                  animate={{\n                    y: '-10%',\n                    opacity: [0, 1, 0],\n                  }}\n                  transition={{\n                    duration: 2,\n                    repeat: Infinity,\n                    delay: i * 0.3,\n                    ease: \"easeOut\"\n                  }}\n                />\n              ))}\n            </div>\n          )}\n        </div>\n      </motion.div>\n    </Link>\n  );\n};\n\nexport default ContentCard;\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AACA;AAAA;AAAA;AAAA;AACA;AAPA;;;;;;;;AAyBA,MAAM,cAA0C,CAAC,EAC/C,EAAE,EACF,MAAM,EACN,KAAK,EACL,IAAI,EACJ,SAAS,EACT,UAAU,EACV,WAAW,EACX,IAAI,EACJ,MAAM,EACN,OAAO,EACP,WAAW,EACX,eAAe,EACf,SAAS,EACV;IACC,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,UAAU,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAkB;IAEvC,yCAAyC;IACzC,MAAM,SAAS,CAAA,GAAA,kLAAA,CAAA,iBAAc,AAAD,EAAE;IAC9B,MAAM,SAAS,CAAA,GAAA,kLAAA,CAAA,iBAAc,AAAD,EAAE;IAC9B,MAAM,UAAU,CAAA,GAAA,yKAAA,CAAA,YAAS,AAAD,EAAE,CAAA,GAAA,4KAAA,CAAA,eAAY,AAAD,EAAE,QAAQ;QAAC,CAAC;QAAK;KAAI,EAAE;QAAC;QAAI,CAAC;KAAG,GAAG;QAAE,WAAW;QAAK,SAAS;IAAG;IACtG,MAAM,UAAU,CAAA,GAAA,yKAAA,CAAA,YAAS,AAAD,EAAE,CAAA,GAAA,4KAAA,CAAA,eAAY,AAAD,EAAE,QAAQ;QAAC,CAAC;QAAK;KAAI,EAAE;QAAC,CAAC;QAAI;KAAG,GAAG;QAAE,WAAW;QAAK,SAAS;IAAG;IAEtG,MAAM,OAAO,SAAS,UAClB,CAAC,aAAa,EAAE,QAAQ,GACxB,SAAS,WACT,CAAC,cAAc,EAAE,QAAQ,GACzB,CAAC,cAAc,EAAE,OAAO,QAAQ,EAAE,OAAO,SAAS,EAAE,SAAS;IAEjE,MAAM,eAAe,SAAS,YAAY,cAAc;IACxD,MAAM,WAAW,SAAS,YAAY,CAAC,CAAC,EAAE,OAAO,CAAC,EAAE,QAAQ,EAAE,EAAE,OAAO,GAAG,OAAO,GAAG,MAAM,GAAG;IAE7F,kFAAkF;IAClF,MAAM,mBAAmB,SAAS,YAAa,mBAAmB,YAAa;IAE/E,6CAA6C;IAC7C,MAAM,kBAAkB,CAAC;QACvB,IAAI,CAAC,QAAQ,OAAO,EAAE;QAEtB,MAAM,OAAO,QAAQ,OAAO,CAAC,qBAAqB;QAClD,MAAM,UAAU,KAAK,IAAI,GAAG,KAAK,KAAK,GAAG;QACzC,MAAM,UAAU,KAAK,GAAG,GAAG,KAAK,MAAM,GAAG;QAEzC,MAAM,YAAY,CAAC,EAAE,OAAO,GAAG,OAAO,IAAI,CAAC,KAAK,KAAK,GAAG,CAAC;QACzD,MAAM,YAAY,CAAC,EAAE,OAAO,GAAG,OAAO,IAAI,CAAC,KAAK,MAAM,GAAG,CAAC;QAE1D,OAAO,GAAG,CAAC;QACX,OAAO,GAAG,CAAC;IACb;IAEA,MAAM,mBAAmB;QACvB,aAAa;QACb,OAAO,GAAG,CAAC;QACX,OAAO,GAAG,CAAC;IACb;IAEA,oCAAoC;IACpC,MAAM,kBAAkB;QACtB,IAAI,CAAC,YAAY,OAAO;QAExB,IAAI,cAAc,KAAK,OAAO;YAAE,OAAO;YAAe,OAAO;YAAiC,MAAM,0MAAA,CAAA,WAAQ;QAAC;QAC7G,IAAI,cAAc,KAAK,OAAO;YAAE,OAAO;YAAa,OAAO;YAAiC,MAAM,kMAAA,CAAA,OAAI;QAAC;QACvG,IAAI,cAAc,KAAK,OAAO;YAAE,OAAO;YAAS,OAAO;YAA6B,MAAM,kNAAA,CAAA,aAAU;QAAC;QACrG,IAAI,cAAc,KAAK,OAAO;YAAE,OAAO;YAAQ,OAAO;YAA+B,MAAM,gMAAA,CAAA,MAAG;QAAC;QAC/F,OAAO;IACT;IAEA,MAAM,eAAe;IAErB,qBACE,8OAAC,4JAAA,CAAA,UAAI;QAAC,MAAM;QAAM,WAAU;kBAC1B,cAAA,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;YACT,KAAK;YACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,kDACA;YAEF,OAAO;gBACL;gBACA;gBACA,gBAAgB;YAClB;YACA,aAAa;YACb,cAAc,IAAM,aAAa;YACjC,cAAc;YACd,YAAY;gBACV,OAAO;gBACP,GAAG,CAAC;gBACJ,YAAY;oBAAE,UAAU;oBAAK,MAAM;gBAAU;YAC/C;YACA,UAAU;gBAAE,OAAO;YAAK;sBAGxB,cAAA,8OAAC;gBAAI,WAAU;;kCAGb,8OAAC;wBAAI,WAAU;;;;;;kCAGf,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,6HAAA,CAAA,UAAK;gCACJ,KAAK,CAAA,GAAA,mHAAA,CAAA,cAAW,AAAD,EAAE;gCACjB,KAAK,gBAAgB;gCACrB,IAAI;gCACJ,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,qDACA,YAAY,uCAAuC;gCAErD,OAAM;gCACN,UAAU;gCACV,QAAQ,IAAM,YAAY;;;;;;4BAI3B,CAAC,0BACA,8OAAC;gCAAI,WAAU;;;;;;;;;;;;kCAKnB,8OAAC;wBAAI,WAAU;;;;;;kCACf,8OAAC;wBAAI,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACf,mHACA,YAAY,gBAAgB;;;;;;kCAI9B,8OAAC;wBAAI,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACf,4DACA,YAAY,mDAAmD;;;;;;oBAIhE,8BACC,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,WAAU;wBACV,SAAS;4BAAE,SAAS;4BAAG,OAAO;wBAAI;wBAClC,SAAS;4BAAE,SAAS;4BAAG,OAAO;wBAAE;wBAChC,YAAY;4BAAE,OAAO;wBAAI;kCAEzB,cAAA,8OAAC;4BAAI,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACf,4GACA,CAAC,iBAAiB,EAAE,aAAa,KAAK,EAAE;;8CAExC,8OAAC,aAAa,IAAI;oCAAC,MAAM;oCAAI,WAAU;;;;;;8CACvC,8OAAC;oCAAK,WAAU;8CAAgC,CAAA,GAAA,mHAAA,CAAA,eAAY,AAAD,EAAE;;;;;;;;;;;;;;;;;kCAMnE,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,WAAU;wBACV,SAAS;4BAAE,SAAS;4BAAG,GAAG,CAAC;wBAAG;wBAC9B,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAE;wBAC5B,YAAY;4BAAE,OAAO;wBAAI;kCAEzB,cAAA,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAK,WAAU;0CACb,SAAS,YAAY,eAAe,SAAS,UAAU,aAAa;;;;;;;;;;;;;;;;oBAM1E,cAAc,cAAc,qBAC3B,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,WAAU;wBACV,SAAS;4BAAE,SAAS;4BAAG,GAAG,CAAC;wBAAG;wBAC9B,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAE;wBAC5B,YAAY;4BAAE,OAAO;wBAAI;kCAEzB,cAAA,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC,0MAAA,CAAA,WAAQ;gCAAC,MAAM;gCAAI,WAAU;;;;;;;;;;;;;;;;kCAMpC,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,WAAU;wBACV,SAAS;4BAAE,SAAS;4BAAG,OAAO;wBAAI;wBAClC,SAAS,YAAY;4BAAE,SAAS;4BAAG,OAAO;wBAAE,IAAI;4BAAE,SAAS;4BAAG,OAAO;wBAAI;wBACzE,YAAY;4BAAE,UAAU;4BAAK,MAAM;wBAAU;kCAE7C,cAAA,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,WAAU;4BACV,YAAY;gCAAE,OAAO;4BAAI;4BACzB,UAAU;gCAAE,OAAO;4BAAK;;8CAGxB,8OAAC;oCAAI,WAAU;;;;;;8CAGf,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC,kMAAA,CAAA,OAAI;wCAAC,MAAM;wCAAI,WAAU;;;;;;;;;;;;;;;;;;;;;;kCAMhC,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,WAAU;wBACV,SAAS;4BAAE,GAAG;4BAAI,SAAS;wBAAI;wBAC/B,SAAS,YAAY;4BAAE,GAAG;4BAAG,SAAS;wBAAE,IAAI;4BAAE,GAAG;4BAAI,SAAS;wBAAI;wBAClE,YAAY;4BAAE,UAAU;wBAAI;kCAE5B,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CACX;;;;;;gCAGF,0BACC,8OAAC;oCAAE,WAAU;8CACV;;;;;;gCAKJ,aAAa,6BACZ,8OAAC,0LAAA,CAAA,SAAM,CAAC,CAAC;oCACP,WAAU;oCACV,SAAS;wCAAE,SAAS;wCAAG,GAAG;oCAAG;oCAC7B,SAAS;wCAAE,SAAS;wCAAG,GAAG;oCAAE;oCAC5B,YAAY;wCAAE,OAAO;oCAAI;8CAExB,CAAA,GAAA,mHAAA,CAAA,eAAY,AAAD,EAAE,aAAa;;;;;;;;;;;;;;;;;kCAOnC,8OAAC;wBAAI,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACf,uFACA,YACI,iGACA;;;;;;oBAIL,2BACC,8OAAC;wBAAI,WAAU;kCACZ;+BAAI,MAAM;yBAAG,CAAC,GAAG,CAAC,CAAC,GAAG,kBACrB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gCAET,WAAU;gCACV,SAAS;oCACP,GAAG,KAAK,MAAM,KAAK,MAAM;oCACzB,GAAG;oCACH,SAAS;gCACX;gCACA,SAAS;oCACP,GAAG;oCACH,SAAS;wCAAC;wCAAG;wCAAG;qCAAE;gCACpB;gCACA,YAAY;oCACV,UAAU;oCACV,QAAQ;oCACR,OAAO,IAAI;oCACX,MAAM;gCACR;+BAhBK;;;;;;;;;;;;;;;;;;;;;;;;;;AAyBvB;uCAEe", "debugId": null}}, {"offset": {"line": 3211, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/New%20folder%20%2825%29/streamzen/src/components/ContentSection.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useRef, useState, useEffect } from 'react';\nimport Link from 'next/link';\nimport { ChevronLeft, ChevronRight, ArrowRight } from 'lucide-react';\nimport { cn } from '@/lib/utils';\nimport ContentCard from './ContentCard';\n\ninterface ContentItem {\n  id: string;\n  imdbId: string;\n  title: string;\n  year?: number;\n  posterUrl?: string;\n  seriesPosterUrl?: string; // For episodes to use their series poster\n  imdbRating?: number;\n  description?: string;\n  type: 'movie' | 'series' | 'episode';\n  season?: number;\n  episode?: number;\n  seriesTitle?: string;\n}\n\ninterface ContentSectionProps {\n  title: string;\n  items: ContentItem[];\n  viewAllHref?: string;\n  className?: string;\n}\n\nconst ContentSection: React.FC<ContentSectionProps> = ({\n  title,\n  items,\n  viewAllHref,\n  className\n}) => {\n  const scrollContainerRef = useRef<HTMLDivElement>(null);\n  const [canScrollLeft, setCanScrollLeft] = useState(false);\n  const [canScrollRight, setCanScrollRight] = useState(true);\n  const [isHovered, setIsHovered] = useState(false);\n\n  const checkScrollButtons = () => {\n    if (!scrollContainerRef.current) return;\n\n    const container = scrollContainerRef.current;\n    setCanScrollLeft(container.scrollLeft > 0);\n    setCanScrollRight(\n      container.scrollLeft < container.scrollWidth - container.clientWidth - 1\n    );\n  };\n\n  useEffect(() => {\n    checkScrollButtons();\n    const container = scrollContainerRef.current;\n    if (container) {\n      container.addEventListener('scroll', checkScrollButtons);\n      return () => container.removeEventListener('scroll', checkScrollButtons);\n    }\n  }, [items]);\n\n  const scroll = (direction: 'left' | 'right') => {\n    if (!scrollContainerRef.current) return;\n\n    const container = scrollContainerRef.current;\n    const cardWidth = 280; // Approximate card width including gap\n    const scrollAmount = cardWidth * 4; // Scroll 4 cards at a time\n\n    container.scrollBy({\n      left: direction === 'left' ? -scrollAmount : scrollAmount,\n      behavior: 'smooth'\n    });\n  };\n\n  if (!items.length) {\n    return null;\n  }\n\n  return (\n    <section\n      className={cn('py-12 animate-fade-in', className)}\n      onMouseEnter={() => setIsHovered(true)}\n      onMouseLeave={() => setIsHovered(false)}\n    >\n      <div className=\"max-w-[1920px] mx-auto px-6 lg:px-12\">\n        {/* Section Header */}\n        <div className=\"flex items-center justify-between mb-8\">\n          <h2 className=\"text-3xl lg:text-4xl font-bold text-white tracking-tight\">\n            {title}\n          </h2>\n          {viewAllHref && (\n            <Link\n              href={viewAllHref}\n              className=\"flex items-center space-x-2 text-gray-400 hover:text-red-500 transition-all duration-300 group focus-ring rounded-lg px-3 py-2\"\n            >\n              <span className=\"font-medium\">View All</span>\n              <ArrowRight size={18} className=\"group-hover:translate-x-1 transition-transform duration-300\" />\n            </Link>\n          )}\n        </div>\n\n        {/* Content Container */}\n        <div className=\"relative group/section\">\n          {/* Left Navigation Button */}\n          <button\n            onClick={() => scroll('left')}\n            disabled={!canScrollLeft}\n            className={cn(\n              'absolute left-0 top-1/2 -translate-y-1/2 -translate-x-16 z-20',\n              'w-14 h-14 glass-elevated rounded-full flex items-center justify-center',\n              'transition-all duration-300 focus-ring shadow-2xl',\n              'hover:scale-110 hover:bg-red-600/20',\n              isHovered && canScrollLeft ? 'opacity-100 -translate-x-8' : 'opacity-0 -translate-x-16',\n              !canScrollLeft && 'cursor-not-allowed opacity-30'\n            )}\n          >\n            <ChevronLeft size={26} className=\"text-white\" />\n          </button>\n\n          {/* Right Navigation Button */}\n          <button\n            onClick={() => scroll('right')}\n            disabled={!canScrollRight}\n            className={cn(\n              'absolute right-0 top-1/2 -translate-y-1/2 translate-x-16 z-20',\n              'w-14 h-14 glass-elevated rounded-full flex items-center justify-center',\n              'transition-all duration-300 focus-ring shadow-2xl',\n              'hover:scale-110 hover:bg-red-600/20',\n              isHovered && canScrollRight ? 'opacity-100 translate-x-8' : 'opacity-0 translate-x-16',\n              !canScrollRight && 'cursor-not-allowed opacity-30'\n            )}\n          >\n            <ChevronRight size={26} className=\"text-white\" />\n          </button>\n\n          {/* Scrollable Content */}\n          <div\n            ref={scrollContainerRef}\n            className=\"flex space-x-6 overflow-x-auto scrollbar-hide pb-6 scroll-smooth\"\n            style={{ scrollSnapType: 'x mandatory' }}\n          >\n            {items.map((item, index) => (\n              <div\n                key={`${item.type}-${item.id}`}\n                className=\"flex-none w-64 lg:w-72 animate-slide-up\"\n                style={{\n                  scrollSnapAlign: 'start',\n                  animationDelay: `${index * 0.1}s`\n                }}\n              >\n                <ContentCard\n                  id={item.id}\n                  imdbId={item.imdbId}\n                  title={item.title}\n                  year={item.year}\n                  posterUrl={item.posterUrl}\n                  seriesPosterUrl={item.seriesPosterUrl}\n                  imdbRating={item.imdbRating}\n                  description={item.description}\n                  type={item.type}\n                  season={item.season}\n                  episode={item.episode}\n                  seriesTitle={item.seriesTitle}\n                />\n              </div>\n            ))}\n          </div>\n\n          {/* Premium Fade Gradients */}\n          <div className=\"absolute left-0 top-0 bottom-6 w-16 bg-gradient-to-r from-black via-black/80 to-transparent pointer-events-none z-10\" />\n          <div className=\"absolute right-0 top-0 bottom-6 w-16 bg-gradient-to-l from-black via-black/80 to-transparent pointer-events-none z-10\" />\n        </div>\n\n        {/* Progress Indicator */}\n        <div className=\"flex justify-center mt-6\">\n          <div className=\"flex space-x-2\">\n            {Array.from({ length: Math.ceil(items.length / 4) }).map((_, index) => (\n              <div\n                key={index}\n                className=\"w-2 h-2 rounded-full bg-white/20 transition-all duration-300\"\n              />\n            ))}\n          </div>\n        </div>\n      </div>\n    </section>\n  );\n};\n\nexport default ContentSection;\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AAAA;AACA;AACA;AANA;;;;;;;AA8BA,MAAM,iBAAgD,CAAC,EACrD,KAAK,EACL,KAAK,EACL,WAAW,EACX,SAAS,EACV;IACC,MAAM,qBAAqB,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAkB;IAClD,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACnD,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACrD,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,MAAM,qBAAqB;QACzB,IAAI,CAAC,mBAAmB,OAAO,EAAE;QAEjC,MAAM,YAAY,mBAAmB,OAAO;QAC5C,iBAAiB,UAAU,UAAU,GAAG;QACxC,kBACE,UAAU,UAAU,GAAG,UAAU,WAAW,GAAG,UAAU,WAAW,GAAG;IAE3E;IAEA,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR;QACA,MAAM,YAAY,mBAAmB,OAAO;QAC5C,IAAI,WAAW;YACb,UAAU,gBAAgB,CAAC,UAAU;YACrC,OAAO,IAAM,UAAU,mBAAmB,CAAC,UAAU;QACvD;IACF,GAAG;QAAC;KAAM;IAEV,MAAM,SAAS,CAAC;QACd,IAAI,CAAC,mBAAmB,OAAO,EAAE;QAEjC,MAAM,YAAY,mBAAmB,OAAO;QAC5C,MAAM,YAAY,KAAK,uCAAuC;QAC9D,MAAM,eAAe,YAAY,GAAG,2BAA2B;QAE/D,UAAU,QAAQ,CAAC;YACjB,MAAM,cAAc,SAAS,CAAC,eAAe;YAC7C,UAAU;QACZ;IACF;IAEA,IAAI,CAAC,MAAM,MAAM,EAAE;QACjB,OAAO;IACT;IAEA,qBACE,8OAAC;QACC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,yBAAyB;QACvC,cAAc,IAAM,aAAa;QACjC,cAAc,IAAM,aAAa;kBAEjC,cAAA,8OAAC;YAAI,WAAU;;8BAEb,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;sCACX;;;;;;wBAEF,6BACC,8OAAC,4JAAA,CAAA,UAAI;4BACH,MAAM;4BACN,WAAU;;8CAEV,8OAAC;oCAAK,WAAU;8CAAc;;;;;;8CAC9B,8OAAC,kNAAA,CAAA,aAAU;oCAAC,MAAM;oCAAI,WAAU;;;;;;;;;;;;;;;;;;8BAMtC,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC;4BACC,SAAS,IAAM,OAAO;4BACtB,UAAU,CAAC;4BACX,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,iEACA,0EACA,qDACA,uCACA,aAAa,gBAAgB,+BAA+B,6BAC5D,CAAC,iBAAiB;sCAGpB,cAAA,8OAAC,oNAAA,CAAA,cAAW;gCAAC,MAAM;gCAAI,WAAU;;;;;;;;;;;sCAInC,8OAAC;4BACC,SAAS,IAAM,OAAO;4BACtB,UAAU,CAAC;4BACX,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,iEACA,0EACA,qDACA,uCACA,aAAa,iBAAiB,8BAA8B,4BAC5D,CAAC,kBAAkB;sCAGrB,cAAA,8OAAC,sNAAA,CAAA,eAAY;gCAAC,MAAM;gCAAI,WAAU;;;;;;;;;;;sCAIpC,8OAAC;4BACC,KAAK;4BACL,WAAU;4BACV,OAAO;gCAAE,gBAAgB;4BAAc;sCAEtC,MAAM,GAAG,CAAC,CAAC,MAAM,sBAChB,8OAAC;oCAEC,WAAU;oCACV,OAAO;wCACL,iBAAiB;wCACjB,gBAAgB,GAAG,QAAQ,IAAI,CAAC,CAAC;oCACnC;8CAEA,cAAA,8OAAC,iIAAA,CAAA,UAAW;wCACV,IAAI,KAAK,EAAE;wCACX,QAAQ,KAAK,MAAM;wCACnB,OAAO,KAAK,KAAK;wCACjB,MAAM,KAAK,IAAI;wCACf,WAAW,KAAK,SAAS;wCACzB,iBAAiB,KAAK,eAAe;wCACrC,YAAY,KAAK,UAAU;wCAC3B,aAAa,KAAK,WAAW;wCAC7B,MAAM,KAAK,IAAI;wCACf,QAAQ,KAAK,MAAM;wCACnB,SAAS,KAAK,OAAO;wCACrB,aAAa,KAAK,WAAW;;;;;;mCAnB1B,GAAG,KAAK,IAAI,CAAC,CAAC,EAAE,KAAK,EAAE,EAAE;;;;;;;;;;sCA0BpC,8OAAC;4BAAI,WAAU;;;;;;sCACf,8OAAC;4BAAI,WAAU;;;;;;;;;;;;8BAIjB,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;kCACZ,MAAM,IAAI,CAAC;4BAAE,QAAQ,KAAK,IAAI,CAAC,MAAM,MAAM,GAAG;wBAAG,GAAG,GAAG,CAAC,CAAC,GAAG,sBAC3D,8OAAC;gCAEC,WAAU;+BADL;;;;;;;;;;;;;;;;;;;;;;;;;;AASrB;uCAEe", "debugId": null}}, {"offset": {"line": 3452, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/New%20folder%20%2825%29/streamzen/src/components/BingeWorthy.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useRef, useState, useEffect } from 'react';\nimport Link from 'next/link';\nimport Image from 'next/image';\nimport { ChevronLeft, ChevronRight, Star, Play, Tv, Clock } from 'lucide-react';\nimport { motion, useInView } from 'framer-motion';\nimport { cn, getImageUrl, formatRating } from '@/lib/utils';\n\ninterface SeriesItem {\n  _id: string;\n  imdbId: string;\n  title: string;\n  startYear?: number;\n  posterUrl?: string;\n  imdbRating?: number;\n  description?: string;\n  totalSeasons?: number;\n  status?: string;\n}\n\ninterface BingeWorthyProps {\n  series: SeriesItem[];\n  className?: string;\n  style?: React.CSSProperties;\n}\n\nconst BingeWorthy: React.FC<BingeWorthyProps> = ({\n  series,\n  className,\n  style\n}) => {\n  const scrollRef = useRef<HTMLDivElement>(null);\n  const sectionRef = useRef<HTMLDivElement>(null);\n  const isInView = useInView(sectionRef, { once: true, margin: \"-100px\" });\n  const [canScrollLeft, setCanScrollLeft] = useState(false);\n  const [canScrollRight, setCanScrollRight] = useState(true);\n\n  const checkScrollButtons = () => {\n    if (!scrollRef.current) return;\n    const container = scrollRef.current;\n    setCanScrollLeft(container.scrollLeft > 0);\n    setCanScrollRight(\n      container.scrollLeft < container.scrollWidth - container.clientWidth - 1\n    );\n  };\n\n  useEffect(() => {\n    checkScrollButtons();\n    const container = scrollRef.current;\n    if (container) {\n      container.addEventListener('scroll', checkScrollButtons);\n      return () => container.removeEventListener('scroll', checkScrollButtons);\n    }\n  }, [series]);\n\n  const scroll = (direction: 'left' | 'right') => {\n    if (!scrollRef.current) return;\n    const container = scrollRef.current;\n    const scrollAmount = container.clientWidth * 0.7;\n    const targetScroll = direction === 'left' \n      ? container.scrollLeft - scrollAmount\n      : container.scrollLeft + scrollAmount;\n\n    container.scrollTo({\n      left: targetScroll,\n      behavior: 'smooth'\n    });\n  };\n\n  if (series.length === 0) return null;\n\n  return (\n    <motion.section\n      ref={sectionRef}\n      initial={{ opacity: 0, y: 50 }}\n      animate={isInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 50 }}\n      transition={{ duration: 0.8 }}\n      className={cn(\"relative\", className)}\n      style={style}\n    >\n      <div className=\"max-w-[2560px] mx-auto px-6 lg:px-12\">\n        {/* Section Header */}\n        <motion.div\n          initial={{ opacity: 0, y: 30 }}\n          animate={isInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 30 }}\n          transition={{ duration: 0.6, delay: 0.2 }}\n          className=\"flex items-center justify-between mb-8\"\n        >\n          <div className=\"flex items-center space-x-4\">\n            <div className=\"p-3 bg-gradient-to-br from-indigo-500 to-purple-600 rounded-xl\">\n              <Tv className=\"w-6 h-6 text-white\" />\n            </div>\n            <div>\n              <h2 className=\"text-3xl lg:text-4xl font-black text-white\">\n                Binge-Worthy Series\n              </h2>\n              <p className=\"text-gray-400 text-sm\">Perfect for your next marathon</p>\n            </div>\n          </div>\n\n          <Link\n            href=\"/series\"\n            className=\"group px-4 py-2 bg-gradient-to-r from-indigo-500/20 to-purple-600/20 border border-indigo-500/30 text-indigo-400 rounded-xl transition-all duration-300 backdrop-blur-sm hover:scale-105\"\n          >\n            <div className=\"flex items-center space-x-2\">\n              <span className=\"font-semibold\">View All Series</span>\n              <ChevronRight className=\"w-4 h-4 group-hover:translate-x-1 transition-transform duration-300\" />\n            </div>\n          </Link>\n        </motion.div>\n\n        {/* Content Carousel */}\n        <div className=\"relative group\">\n          {/* Navigation Buttons */}\n          {canScrollLeft && (\n            <button\n              onClick={() => scroll('left')}\n              className=\"absolute left-0 top-1/2 -translate-y-1/2 z-10 p-2 bg-black/80 hover:bg-black text-white rounded-full backdrop-blur-sm transition-all duration-300 hover:scale-110 shadow-xl opacity-0 group-hover:opacity-100\"\n            >\n              <ChevronLeft className=\"w-5 h-5\" />\n            </button>\n          )}\n\n          {canScrollRight && (\n            <button\n              onClick={() => scroll('right')}\n              className=\"absolute right-0 top-1/2 -translate-y-1/2 z-10 p-2 bg-black/80 hover:bg-black text-white rounded-full backdrop-blur-sm transition-all duration-300 hover:scale-110 shadow-xl opacity-0 group-hover:opacity-100\"\n            >\n              <ChevronRight className=\"w-5 h-5\" />\n            </button>\n          )}\n\n          {/* Scrollable Content */}\n          <div\n            ref={scrollRef}\n            className=\"flex space-x-4 overflow-x-auto scrollbar-hide pb-2\"\n            style={{ scrollbarWidth: 'none', msOverflowStyle: 'none' }}\n          >\n            {series.slice(0, 12).map((item, index) => (\n              <motion.div\n                key={item._id}\n                initial={{ opacity: 0, scale: 0.9 }}\n                animate={isInView ? { opacity: 1, scale: 1 } : { opacity: 0, scale: 0.9 }}\n                transition={{ duration: 0.5, delay: 0.1 * index }}\n                className=\"flex-shrink-0 group\"\n              >\n                <Link href={`/watch/series/${item.imdbId}`}>\n                  <div className=\"relative w-52 h-78 rounded-xl overflow-hidden bg-gray-900 group-hover:scale-105 transition-all duration-500 shadow-lg hover:shadow-2xl\">\n                    {/* Status Badge */}\n                    {item.status && (\n                      <div className=\"absolute top-2 left-2 z-10 px-2 py-1 bg-green-500/80 rounded-full text-xs font-semibold text-white backdrop-blur-sm\">\n                        {item.status === 'ongoing' ? '🔴 Ongoing' : '✅ Complete'}\n                      </div>\n                    )}\n\n                    {/* Seasons Badge */}\n                    {item.totalSeasons && (\n                      <div className=\"absolute top-2 right-2 z-10 px-2 py-1 bg-black/70 rounded-full text-xs font-semibold text-white backdrop-blur-sm\">\n                        {item.totalSeasons} Season{item.totalSeasons > 1 ? 's' : ''}\n                      </div>\n                    )}\n\n                    {/* Poster Image */}\n                    <Image\n                      src={getImageUrl(item.posterUrl)}\n                      alt={item.title}\n                      fill\n                      className=\"object-cover transition-transform duration-500 group-hover:scale-110\"\n                    />\n\n                    {/* Gradient Overlay */}\n                    <div className=\"absolute inset-0 bg-gradient-to-t from-black via-transparent to-transparent opacity-60 group-hover:opacity-80 transition-opacity duration-300\" />\n\n                    {/* Play Button Overlay */}\n                    <div className=\"absolute inset-0 flex items-center justify-center opacity-0 group-hover:opacity-100 transition-all duration-300\">\n                      <div className=\"p-3 bg-white/20 rounded-full backdrop-blur-sm border border-white/30 group-hover:scale-110 transition-transform duration-300\">\n                        <Play className=\"w-6 h-6 text-white fill-current\" />\n                      </div>\n                    </div>\n\n                    {/* Content Info */}\n                    <div className=\"absolute bottom-0 left-0 right-0 p-3 space-y-2\">\n                      <h4 className=\"text-white font-bold text-sm leading-tight line-clamp-2\">\n                        {item.title}\n                      </h4>\n                      \n                      <div className=\"flex items-center justify-between\">\n                        <span className=\"text-gray-300 text-xs\">\n                          {item.startYear}\n                        </span>\n                        \n                        {item.imdbRating && (\n                          <div className=\"flex items-center space-x-1 px-1.5 py-0.5 bg-yellow-500/20 rounded-full\">\n                            <Star className=\"w-2.5 h-2.5 text-yellow-400 fill-current\" />\n                            <span className=\"text-yellow-400 text-xs font-bold\">\n                              {formatRating(item.imdbRating)}\n                            </span>\n                          </div>\n                        )}\n                      </div>\n\n                      {/* Binge indicator */}\n                      <div className=\"flex items-center space-x-1\">\n                        <Clock className=\"w-3 h-3 text-indigo-400\" />\n                        <span className=\"text-indigo-400 text-xs font-semibold\">\n                          Perfect for binging\n                        </span>\n                      </div>\n                    </div>\n\n                    {/* Accent */}\n                    <div className=\"absolute bottom-0 left-0 w-full h-1 bg-gradient-to-r from-indigo-500 to-purple-600\" />\n                  </div>\n                </Link>\n              </motion.div>\n            ))}\n          </div>\n        </div>\n      </div>\n    </motion.section>\n  );\n};\n\nexport default BingeWorthy;\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AAAA;AACA;AAPA;;;;;;;;AA2BA,MAAM,cAA0C,CAAC,EAC/C,MAAM,EACN,SAAS,EACT,KAAK,EACN;IACC,MAAM,YAAY,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAkB;IACzC,MAAM,aAAa,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAkB;IAC1C,MAAM,WAAW,CAAA,GAAA,6KAAA,CAAA,YAAS,AAAD,EAAE,YAAY;QAAE,MAAM;QAAM,QAAQ;IAAS;IACtE,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACnD,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAErD,MAAM,qBAAqB;QACzB,IAAI,CAAC,UAAU,OAAO,EAAE;QACxB,MAAM,YAAY,UAAU,OAAO;QACnC,iBAAiB,UAAU,UAAU,GAAG;QACxC,kBACE,UAAU,UAAU,GAAG,UAAU,WAAW,GAAG,UAAU,WAAW,GAAG;IAE3E;IAEA,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR;QACA,MAAM,YAAY,UAAU,OAAO;QACnC,IAAI,WAAW;YACb,UAAU,gBAAgB,CAAC,UAAU;YACrC,OAAO,IAAM,UAAU,mBAAmB,CAAC,UAAU;QACvD;IACF,GAAG;QAAC;KAAO;IAEX,MAAM,SAAS,CAAC;QACd,IAAI,CAAC,UAAU,OAAO,EAAE;QACxB,MAAM,YAAY,UAAU,OAAO;QACnC,MAAM,eAAe,UAAU,WAAW,GAAG;QAC7C,MAAM,eAAe,cAAc,SAC/B,UAAU,UAAU,GAAG,eACvB,UAAU,UAAU,GAAG;QAE3B,UAAU,QAAQ,CAAC;YACjB,MAAM;YACN,UAAU;QACZ;IACF;IAEA,IAAI,OAAO,MAAM,KAAK,GAAG,OAAO;IAEhC,qBACE,8OAAC,0LAAA,CAAA,SAAM,CAAC,OAAO;QACb,KAAK;QACL,SAAS;YAAE,SAAS;YAAG,GAAG;QAAG;QAC7B,SAAS,WAAW;YAAE,SAAS;YAAG,GAAG;QAAE,IAAI;YAAE,SAAS;YAAG,GAAG;QAAG;QAC/D,YAAY;YAAE,UAAU;QAAI;QAC5B,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,YAAY;QAC1B,OAAO;kBAEP,cAAA,8OAAC;YAAI,WAAU;;8BAEb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC7B,SAAS,WAAW;wBAAE,SAAS;wBAAG,GAAG;oBAAE,IAAI;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC/D,YAAY;wBAAE,UAAU;wBAAK,OAAO;oBAAI;oBACxC,WAAU;;sCAEV,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC,8LAAA,CAAA,KAAE;wCAAC,WAAU;;;;;;;;;;;8CAEhB,8OAAC;;sDACC,8OAAC;4CAAG,WAAU;sDAA6C;;;;;;sDAG3D,8OAAC;4CAAE,WAAU;sDAAwB;;;;;;;;;;;;;;;;;;sCAIzC,8OAAC,4JAAA,CAAA,UAAI;4BACH,MAAK;4BACL,WAAU;sCAEV,cAAA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAK,WAAU;kDAAgB;;;;;;kDAChC,8OAAC,sNAAA,CAAA,eAAY;wCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;8BAM9B,8OAAC;oBAAI,WAAU;;wBAEZ,+BACC,8OAAC;4BACC,SAAS,IAAM,OAAO;4BACtB,WAAU;sCAEV,cAAA,8OAAC,oNAAA,CAAA,cAAW;gCAAC,WAAU;;;;;;;;;;;wBAI1B,gCACC,8OAAC;4BACC,SAAS,IAAM,OAAO;4BACtB,WAAU;sCAEV,cAAA,8OAAC,sNAAA,CAAA,eAAY;gCAAC,WAAU;;;;;;;;;;;sCAK5B,8OAAC;4BACC,KAAK;4BACL,WAAU;4BACV,OAAO;gCAAE,gBAAgB;gCAAQ,iBAAiB;4BAAO;sCAExD,OAAO,KAAK,CAAC,GAAG,IAAI,GAAG,CAAC,CAAC,MAAM,sBAC9B,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oCAET,SAAS;wCAAE,SAAS;wCAAG,OAAO;oCAAI;oCAClC,SAAS,WAAW;wCAAE,SAAS;wCAAG,OAAO;oCAAE,IAAI;wCAAE,SAAS;wCAAG,OAAO;oCAAI;oCACxE,YAAY;wCAAE,UAAU;wCAAK,OAAO,MAAM;oCAAM;oCAChD,WAAU;8CAEV,cAAA,8OAAC,4JAAA,CAAA,UAAI;wCAAC,MAAM,CAAC,cAAc,EAAE,KAAK,MAAM,EAAE;kDACxC,cAAA,8OAAC;4CAAI,WAAU;;gDAEZ,KAAK,MAAM,kBACV,8OAAC;oDAAI,WAAU;8DACZ,KAAK,MAAM,KAAK,YAAY,eAAe;;;;;;gDAK/C,KAAK,YAAY,kBAChB,8OAAC;oDAAI,WAAU;;wDACZ,KAAK,YAAY;wDAAC;wDAAQ,KAAK,YAAY,GAAG,IAAI,MAAM;;;;;;;8DAK7D,8OAAC,6HAAA,CAAA,UAAK;oDACJ,KAAK,CAAA,GAAA,mHAAA,CAAA,cAAW,AAAD,EAAE,KAAK,SAAS;oDAC/B,KAAK,KAAK,KAAK;oDACf,IAAI;oDACJ,WAAU;;;;;;8DAIZ,8OAAC;oDAAI,WAAU;;;;;;8DAGf,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC;wDAAI,WAAU;kEACb,cAAA,8OAAC,kMAAA,CAAA,OAAI;4DAAC,WAAU;;;;;;;;;;;;;;;;8DAKpB,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAG,WAAU;sEACX,KAAK,KAAK;;;;;;sEAGb,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEAAK,WAAU;8EACb,KAAK,SAAS;;;;;;gEAGhB,KAAK,UAAU,kBACd,8OAAC;oEAAI,WAAU;;sFACb,8OAAC,kMAAA,CAAA,OAAI;4EAAC,WAAU;;;;;;sFAChB,8OAAC;4EAAK,WAAU;sFACb,CAAA,GAAA,mHAAA,CAAA,eAAY,AAAD,EAAE,KAAK,UAAU;;;;;;;;;;;;;;;;;;sEAOrC,8OAAC;4DAAI,WAAU;;8EACb,8OAAC,oMAAA,CAAA,QAAK;oEAAC,WAAU;;;;;;8EACjB,8OAAC;oEAAK,WAAU;8EAAwC;;;;;;;;;;;;;;;;;;8DAO5D,8OAAC;oDAAI,WAAU;;;;;;;;;;;;;;;;;mCAvEd,KAAK,GAAG;;;;;;;;;;;;;;;;;;;;;;;;;;;AAiF7B;uCAEe", "debugId": null}}, {"offset": {"line": 3891, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/New%20folder%20%2825%29/streamzen/src/components/HiddenGems.tsx"], "sourcesContent": ["'use client';\n\nimport React from 'react';\nimport Link from 'next/link';\nimport Image from 'next/image';\nimport { Gem, Star, Play } from 'lucide-react';\nimport { motion, useInView } from 'framer-motion';\nimport { cn, getImageUrl, formatRating } from '@/lib/utils';\n\ninterface ContentItem {\n  _id: string;\n  imdbId: string;\n  title: string;\n  year?: number;\n  startYear?: number;\n  posterUrl?: string;\n  imdbRating?: number;\n}\n\ninterface HiddenGemsProps {\n  movies: ContentItem[];\n  series: ContentItem[];\n  className?: string;\n  style?: React.CSSProperties;\n}\n\nconst HiddenGems: React.FC<HiddenGemsProps> = ({\n  movies,\n  series,\n  className,\n  style\n}) => {\n  const sectionRef = React.useRef<HTMLDivElement>(null);\n  const isInView = useInView(sectionRef, { once: true, margin: \"-100px\" });\n\n  const allContent = [\n    ...movies.map(item => ({ ...item, type: 'movie' as const })),\n    ...series.map(item => ({ ...item, type: 'series' as const }))\n  ].slice(0, 6);\n\n  if (allContent.length === 0) return null;\n\n  return (\n    <motion.section\n      ref={sectionRef}\n      initial={{ opacity: 0, y: 50 }}\n      animate={isInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 50 }}\n      transition={{ duration: 0.8 }}\n      className={cn(\"relative\", className)}\n      style={style}\n    >\n      <div className=\"max-w-[2560px] mx-auto px-6 lg:px-12\">\n        <motion.div\n          initial={{ opacity: 0, y: 30 }}\n          animate={isInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 30 }}\n          transition={{ duration: 0.6, delay: 0.2 }}\n          className=\"text-center mb-12\"\n        >\n          <div className=\"flex items-center justify-center space-x-3 mb-4\">\n            <div className=\"p-3 bg-gradient-to-br from-emerald-500 to-teal-600 rounded-xl\">\n              <Gem className=\"w-6 h-6 text-white\" />\n            </div>\n            <h2 className=\"text-4xl lg:text-5xl font-black text-white\">\n              Hidden Gems\n            </h2>\n          </div>\n          <p className=\"text-gray-400 text-lg max-w-2xl mx-auto\">\n            Highly rated treasures waiting to be discovered\n          </p>\n        </motion.div>\n\n        <div className=\"grid grid-cols-2 md:grid-cols-3 gap-6\">\n          {allContent.map((item, index) => (\n            <motion.div\n              key={item._id}\n              initial={{ opacity: 0, scale: 0.9 }}\n              animate={isInView ? { opacity: 1, scale: 1 } : { opacity: 0, scale: 0.9 }}\n              transition={{ duration: 0.5, delay: 0.1 * index }}\n              className=\"group\"\n            >\n              <Link href={`/watch/${item.type}/${item.imdbId}`}>\n                <div className=\"relative w-full h-80 rounded-xl overflow-hidden bg-gray-900 group-hover:scale-105 transition-all duration-500 shadow-lg hover:shadow-2xl\">\n                  <div className=\"absolute top-2 left-2 z-10 px-2 py-1 bg-emerald-500/80 rounded-full text-xs font-semibold text-white backdrop-blur-sm\">\n                    💎 Hidden Gem\n                  </div>\n\n                  <Image\n                    src={getImageUrl(item.posterUrl)}\n                    alt={item.title}\n                    fill\n                    className=\"object-cover transition-transform duration-500 group-hover:scale-110\"\n                  />\n                  <div className=\"absolute inset-0 bg-gradient-to-t from-black via-transparent to-transparent opacity-60 group-hover:opacity-80 transition-opacity duration-300\" />\n                  \n                  <div className=\"absolute inset-0 flex items-center justify-center opacity-0 group-hover:opacity-100 transition-all duration-300\">\n                    <div className=\"p-3 bg-white/20 rounded-full backdrop-blur-sm border border-white/30\">\n                      <Play className=\"w-6 h-6 text-white fill-current\" />\n                    </div>\n                  </div>\n\n                  <div className=\"absolute bottom-0 left-0 right-0 p-3 space-y-2\">\n                    <h4 className=\"text-white font-bold text-sm leading-tight line-clamp-2\">\n                      {item.title}\n                    </h4>\n                    <div className=\"flex items-center justify-between\">\n                      <span className=\"text-gray-300 text-xs\">\n                        {item.year || item.startYear}\n                      </span>\n                      {item.imdbRating && (\n                        <div className=\"flex items-center space-x-1 px-2 py-1 bg-yellow-500/20 rounded-full\">\n                          <Star className=\"w-3 h-3 text-yellow-400 fill-current\" />\n                          <span className=\"text-yellow-400 text-xs font-bold\">\n                            {formatRating(item.imdbRating)}\n                          </span>\n                        </div>\n                      )}\n                    </div>\n                  </div>\n                </div>\n              </Link>\n            </motion.div>\n          ))}\n        </div>\n      </div>\n    </motion.section>\n  );\n};\n\nexport default HiddenGems;\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAAA;AAAA;AACA;AAAA;AACA;AAPA;;;;;;;;AA0BA,MAAM,aAAwC,CAAC,EAC7C,MAAM,EACN,MAAM,EACN,SAAS,EACT,KAAK,EACN;IACC,MAAM,aAAa,qMAAA,CAAA,UAAK,CAAC,MAAM,CAAiB;IAChD,MAAM,WAAW,CAAA,GAAA,6KAAA,CAAA,YAAS,AAAD,EAAE,YAAY;QAAE,MAAM;QAAM,QAAQ;IAAS;IAEtE,MAAM,aAAa;WACd,OAAO,GAAG,CAAC,CAAA,OAAQ,CAAC;gBAAE,GAAG,IAAI;gBAAE,MAAM;YAAiB,CAAC;WACvD,OAAO,GAAG,CAAC,CAAA,OAAQ,CAAC;gBAAE,GAAG,IAAI;gBAAE,MAAM;YAAkB,CAAC;KAC5D,CAAC,KAAK,CAAC,GAAG;IAEX,IAAI,WAAW,MAAM,KAAK,GAAG,OAAO;IAEpC,qBACE,8OAAC,0LAAA,CAAA,SAAM,CAAC,OAAO;QACb,KAAK;QACL,SAAS;YAAE,SAAS;YAAG,GAAG;QAAG;QAC7B,SAAS,WAAW;YAAE,SAAS;YAAG,GAAG;QAAE,IAAI;YAAE,SAAS;YAAG,GAAG;QAAG;QAC/D,YAAY;YAAE,UAAU;QAAI;QAC5B,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,YAAY;QAC1B,OAAO;kBAEP,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC7B,SAAS,WAAW;wBAAE,SAAS;wBAAG,GAAG;oBAAE,IAAI;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC/D,YAAY;wBAAE,UAAU;wBAAK,OAAO;oBAAI;oBACxC,WAAU;;sCAEV,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC,gMAAA,CAAA,MAAG;wCAAC,WAAU;;;;;;;;;;;8CAEjB,8OAAC;oCAAG,WAAU;8CAA6C;;;;;;;;;;;;sCAI7D,8OAAC;4BAAE,WAAU;sCAA0C;;;;;;;;;;;;8BAKzD,8OAAC;oBAAI,WAAU;8BACZ,WAAW,GAAG,CAAC,CAAC,MAAM,sBACrB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4BAET,SAAS;gCAAE,SAAS;gCAAG,OAAO;4BAAI;4BAClC,SAAS,WAAW;gCAAE,SAAS;gCAAG,OAAO;4BAAE,IAAI;gCAAE,SAAS;gCAAG,OAAO;4BAAI;4BACxE,YAAY;gCAAE,UAAU;gCAAK,OAAO,MAAM;4BAAM;4BAChD,WAAU;sCAEV,cAAA,8OAAC,4JAAA,CAAA,UAAI;gCAAC,MAAM,CAAC,OAAO,EAAE,KAAK,IAAI,CAAC,CAAC,EAAE,KAAK,MAAM,EAAE;0CAC9C,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDAAwH;;;;;;sDAIvI,8OAAC,6HAAA,CAAA,UAAK;4CACJ,KAAK,CAAA,GAAA,mHAAA,CAAA,cAAW,AAAD,EAAE,KAAK,SAAS;4CAC/B,KAAK,KAAK,KAAK;4CACf,IAAI;4CACJ,WAAU;;;;;;sDAEZ,8OAAC;4CAAI,WAAU;;;;;;sDAEf,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC,kMAAA,CAAA,OAAI;oDAAC,WAAU;;;;;;;;;;;;;;;;sDAIpB,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAG,WAAU;8DACX,KAAK,KAAK;;;;;;8DAEb,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAK,WAAU;sEACb,KAAK,IAAI,IAAI,KAAK,SAAS;;;;;;wDAE7B,KAAK,UAAU,kBACd,8OAAC;4DAAI,WAAU;;8EACb,8OAAC,kMAAA,CAAA,OAAI;oEAAC,WAAU;;;;;;8EAChB,8OAAC;oEAAK,WAAU;8EACb,CAAA,GAAA,mHAAA,CAAA,eAAY,AAAD,EAAE,KAAK,UAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;2BAtCtC,KAAK,GAAG;;;;;;;;;;;;;;;;;;;;;AAoD3B;uCAEe", "debugId": null}}, {"offset": {"line": 4181, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/New%20folder%20%2825%29/streamzen/src/components/QuickPicks.tsx"], "sourcesContent": ["'use client';\n\nimport React from 'react';\nimport Link from 'next/link';\nimport Image from 'next/image';\nimport { Clock, Star, Play } from 'lucide-react';\nimport { motion, useInView } from 'framer-motion';\nimport { cn, getImageUrl, formatRating } from '@/lib/utils';\n\ninterface MovieItem {\n  _id: string;\n  imdbId: string;\n  title: string;\n  year?: number;\n  posterUrl?: string;\n  imdbRating?: number;\n  runtime?: string;\n}\n\ninterface QuickPicksProps {\n  movies: MovieItem[];\n  className?: string;\n  style?: React.CSSProperties;\n}\n\nconst QuickPicks: React.FC<QuickPicksProps> = ({\n  movies,\n  className,\n  style\n}) => {\n  const sectionRef = React.useRef<HTMLDivElement>(null);\n  const isInView = useInView(sectionRef, { once: true, margin: \"-100px\" });\n\n  if (movies.length === 0) return null;\n\n  return (\n    <motion.section\n      ref={sectionRef}\n      initial={{ opacity: 0, y: 50 }}\n      animate={isInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 50 }}\n      transition={{ duration: 0.8 }}\n      className={cn(\"relative\", className)}\n      style={style}\n    >\n      <div className=\"max-w-[2560px] mx-auto px-6 lg:px-12\">\n        <motion.div\n          initial={{ opacity: 0, y: 30 }}\n          animate={isInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 30 }}\n          transition={{ duration: 0.6, delay: 0.2 }}\n          className=\"text-center mb-12\"\n        >\n          <div className=\"flex items-center justify-center space-x-3 mb-4\">\n            <div className=\"p-3 bg-gradient-to-br from-cyan-500 to-blue-600 rounded-xl\">\n              <Clock className=\"w-6 h-6 text-white\" />\n            </div>\n            <h2 className=\"text-4xl lg:text-5xl font-black text-white\">\n              Quick Picks\n            </h2>\n          </div>\n          <p className=\"text-gray-400 text-lg max-w-2xl mx-auto\">\n            Perfect for a quick movie night - under 2 hours\n          </p>\n        </motion.div>\n\n        <div className=\"grid grid-cols-2 md:grid-cols-4 lg:grid-cols-5 gap-4\">\n          {movies.slice(0, 10).map((item, index) => (\n            <motion.div\n              key={item._id}\n              initial={{ opacity: 0, scale: 0.9 }}\n              animate={isInView ? { opacity: 1, scale: 1 } : { opacity: 0, scale: 0.9 }}\n              transition={{ duration: 0.5, delay: 0.1 * index }}\n              className=\"group\"\n            >\n              <Link href={`/watch/movie/${item.imdbId}`}>\n                <div className=\"relative w-full h-72 rounded-xl overflow-hidden bg-gray-900 group-hover:scale-105 transition-all duration-500 shadow-lg hover:shadow-2xl\">\n                  <div className=\"absolute top-2 left-2 z-10 px-2 py-1 bg-cyan-500/80 rounded-full text-xs font-semibold text-white backdrop-blur-sm\">\n                    ⚡ Quick\n                  </div>\n\n                  <Image\n                    src={getImageUrl(item.posterUrl)}\n                    alt={item.title}\n                    fill\n                    className=\"object-cover transition-transform duration-500 group-hover:scale-110\"\n                  />\n                  <div className=\"absolute inset-0 bg-gradient-to-t from-black via-transparent to-transparent opacity-60 group-hover:opacity-80 transition-opacity duration-300\" />\n                  \n                  <div className=\"absolute inset-0 flex items-center justify-center opacity-0 group-hover:opacity-100 transition-all duration-300\">\n                    <div className=\"p-3 bg-white/20 rounded-full backdrop-blur-sm border border-white/30\">\n                      <Play className=\"w-6 h-6 text-white fill-current\" />\n                    </div>\n                  </div>\n\n                  <div className=\"absolute bottom-0 left-0 right-0 p-3 space-y-1\">\n                    <h4 className=\"text-white font-bold text-sm leading-tight line-clamp-2\">\n                      {item.title}\n                    </h4>\n                    <div className=\"flex items-center justify-between\">\n                      <span className=\"text-gray-300 text-xs\">\n                        {item.runtime || `${item.year}`}\n                      </span>\n                      {item.imdbRating && (\n                        <div className=\"flex items-center space-x-1 px-1.5 py-0.5 bg-yellow-500/20 rounded-full\">\n                          <Star className=\"w-2.5 h-2.5 text-yellow-400 fill-current\" />\n                          <span className=\"text-yellow-400 text-xs font-bold\">\n                            {formatRating(item.imdbRating)}\n                          </span>\n                        </div>\n                      )}\n                    </div>\n                  </div>\n                </div>\n              </Link>\n            </motion.div>\n          ))}\n        </div>\n      </div>\n    </motion.section>\n  );\n};\n\nexport default QuickPicks;\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAAA;AAAA;AACA;AAAA;AACA;AAPA;;;;;;;;AAyBA,MAAM,aAAwC,CAAC,EAC7C,MAAM,EACN,SAAS,EACT,KAAK,EACN;IACC,MAAM,aAAa,qMAAA,CAAA,UAAK,CAAC,MAAM,CAAiB;IAChD,MAAM,WAAW,CAAA,GAAA,6KAAA,CAAA,YAAS,AAAD,EAAE,YAAY;QAAE,MAAM;QAAM,QAAQ;IAAS;IAEtE,IAAI,OAAO,MAAM,KAAK,GAAG,OAAO;IAEhC,qBACE,8OAAC,0LAAA,CAAA,SAAM,CAAC,OAAO;QACb,KAAK;QACL,SAAS;YAAE,SAAS;YAAG,GAAG;QAAG;QAC7B,SAAS,WAAW;YAAE,SAAS;YAAG,GAAG;QAAE,IAAI;YAAE,SAAS;YAAG,GAAG;QAAG;QAC/D,YAAY;YAAE,UAAU;QAAI;QAC5B,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,YAAY;QAC1B,OAAO;kBAEP,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC7B,SAAS,WAAW;wBAAE,SAAS;wBAAG,GAAG;oBAAE,IAAI;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC/D,YAAY;wBAAE,UAAU;wBAAK,OAAO;oBAAI;oBACxC,WAAU;;sCAEV,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC,oMAAA,CAAA,QAAK;wCAAC,WAAU;;;;;;;;;;;8CAEnB,8OAAC;oCAAG,WAAU;8CAA6C;;;;;;;;;;;;sCAI7D,8OAAC;4BAAE,WAAU;sCAA0C;;;;;;;;;;;;8BAKzD,8OAAC;oBAAI,WAAU;8BACZ,OAAO,KAAK,CAAC,GAAG,IAAI,GAAG,CAAC,CAAC,MAAM,sBAC9B,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4BAET,SAAS;gCAAE,SAAS;gCAAG,OAAO;4BAAI;4BAClC,SAAS,WAAW;gCAAE,SAAS;gCAAG,OAAO;4BAAE,IAAI;gCAAE,SAAS;gCAAG,OAAO;4BAAI;4BACxE,YAAY;gCAAE,UAAU;gCAAK,OAAO,MAAM;4BAAM;4BAChD,WAAU;sCAEV,cAAA,8OAAC,4JAAA,CAAA,UAAI;gCAAC,MAAM,CAAC,aAAa,EAAE,KAAK,MAAM,EAAE;0CACvC,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDAAqH;;;;;;sDAIpI,8OAAC,6HAAA,CAAA,UAAK;4CACJ,KAAK,CAAA,GAAA,mHAAA,CAAA,cAAW,AAAD,EAAE,KAAK,SAAS;4CAC/B,KAAK,KAAK,KAAK;4CACf,IAAI;4CACJ,WAAU;;;;;;sDAEZ,8OAAC;4CAAI,WAAU;;;;;;sDAEf,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC,kMAAA,CAAA,OAAI;oDAAC,WAAU;;;;;;;;;;;;;;;;sDAIpB,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAG,WAAU;8DACX,KAAK,KAAK;;;;;;8DAEb,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAK,WAAU;sEACb,KAAK,OAAO,IAAI,GAAG,KAAK,IAAI,EAAE;;;;;;wDAEhC,KAAK,UAAU,kBACd,8OAAC;4DAAI,WAAU;;8EACb,8OAAC,kMAAA,CAAA,OAAI;oEAAC,WAAU;;;;;;8EAChB,8OAAC;oEAAK,WAAU;8EACb,CAAA,GAAA,mHAAA,CAAA,eAAY,AAAD,EAAE,KAAK,UAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;2BAtCtC,KAAK,GAAG;;;;;;;;;;;;;;;;;;;;;AAoD3B;uCAEe", "debugId": null}}, {"offset": {"line": 4461, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/New%20folder%20%2825%29/streamzen/src/components/ModernLoader.tsx"], "sourcesContent": ["'use client';\n\nimport React from 'react';\nimport { motion } from 'framer-motion';\nimport { Play, Sparkles, Film, Tv } from 'lucide-react';\n\ninterface ModernLoaderProps {\n  message?: string;\n  type?: 'default' | 'content' | 'hero';\n  className?: string;\n}\n\nconst ModernLoader: React.FC<ModernLoaderProps> = ({ \n  message = \"Loading amazing content...\", \n  type = 'default',\n  className = \"\"\n}) => {\n  if (type === 'hero') {\n    return (\n      <div className={`h-screen bg-gradient-to-br from-gray-900 via-black to-gray-900 flex items-center justify-center ${className}`}>\n        <div className=\"text-center\">\n          {/* Animated Logo */}\n          <motion.div\n            className=\"relative mb-8\"\n            initial={{ scale: 0.8, opacity: 0 }}\n            animate={{ scale: 1, opacity: 1 }}\n            transition={{ duration: 0.8 }}\n          >\n            <motion.div\n              className=\"w-20 h-20 bg-gradient-to-br from-red-500 to-red-600 rounded-2xl flex items-center justify-center shadow-2xl mx-auto\"\n              animate={{ \n                rotate: [0, 360],\n                scale: [1, 1.1, 1]\n              }}\n              transition={{ \n                rotate: { duration: 3, repeat: Infinity, ease: \"linear\" },\n                scale: { duration: 2, repeat: Infinity, ease: \"easeInOut\" }\n              }}\n            >\n              <Play size={32} className=\"text-white fill-current ml-1\" />\n            </motion.div>\n            \n            {/* Floating sparkles */}\n            {[...Array(6)].map((_, i) => (\n              <motion.div\n                key={i}\n                className=\"absolute w-2 h-2 bg-yellow-400 rounded-full\"\n                style={{\n                  left: `${20 + Math.cos(i * 60 * Math.PI / 180) * 40}px`,\n                  top: `${20 + Math.sin(i * 60 * Math.PI / 180) * 40}px`,\n                }}\n                animate={{\n                  scale: [0, 1, 0],\n                  opacity: [0, 1, 0],\n                }}\n                transition={{\n                  duration: 2,\n                  repeat: Infinity,\n                  delay: i * 0.3,\n                }}\n              >\n                <Sparkles size={8} />\n              </motion.div>\n            ))}\n          </motion.div>\n\n          {/* Loading Text */}\n          <motion.div\n            initial={{ y: 20, opacity: 0 }}\n            animate={{ y: 0, opacity: 1 }}\n            transition={{ duration: 0.6, delay: 0.3 }}\n          >\n            <h2 className=\"text-3xl font-black text-white mb-2\">\n              free<span className=\"text-red-500\">Movies</span>WatchNow\n            </h2>\n            <p className=\"text-gray-400 text-lg\">{message}</p>\n          </motion.div>\n\n          {/* Loading Bar */}\n          <motion.div\n            className=\"w-64 h-1 bg-gray-800 rounded-full overflow-hidden mx-auto mt-6\"\n            initial={{ opacity: 0 }}\n            animate={{ opacity: 1 }}\n            transition={{ delay: 0.6 }}\n          >\n            <motion.div\n              className=\"h-full bg-gradient-to-r from-red-500 to-red-600 rounded-full\"\n              animate={{ x: ['-100%', '100%'] }}\n              transition={{ duration: 1.5, repeat: Infinity, ease: \"easeInOut\" }}\n            />\n          </motion.div>\n        </div>\n      </div>\n    );\n  }\n\n  if (type === 'content') {\n    return (\n      <div className={`h-80 bg-gradient-to-br from-gray-900/20 to-gray-800/20 rounded-3xl flex items-center justify-center border border-gray-800/50 backdrop-blur-sm ${className}`}>\n        <div className=\"text-center\">\n          <motion.div\n            className=\"flex items-center justify-center space-x-2 mb-4\"\n            initial={{ opacity: 0, y: 20 }}\n            animate={{ opacity: 1, y: 0 }}\n            transition={{ duration: 0.6 }}\n          >\n            <motion.div\n              animate={{ rotate: 360 }}\n              transition={{ duration: 2, repeat: Infinity, ease: \"linear\" }}\n            >\n              <Film className=\"w-8 h-8 text-red-500\" />\n            </motion.div>\n            <motion.div\n              animate={{ rotate: -360 }}\n              transition={{ duration: 2.5, repeat: Infinity, ease: \"linear\" }}\n            >\n              <Tv className=\"w-8 h-8 text-blue-500\" />\n            </motion.div>\n          </motion.div>\n          \n          <motion.p\n            className=\"text-gray-400 font-medium\"\n            initial={{ opacity: 0 }}\n            animate={{ opacity: 1 }}\n            transition={{ delay: 0.3 }}\n          >\n            {message}\n          </motion.p>\n        </div>\n      </div>\n    );\n  }\n\n  // Default loader\n  return (\n    <div className={`flex items-center justify-center p-8 ${className}`}>\n      <div className=\"text-center\">\n        <motion.div\n          className=\"w-12 h-12 border-4 border-gray-600 border-t-red-500 rounded-full mx-auto mb-4\"\n          animate={{ rotate: 360 }}\n          transition={{ duration: 1, repeat: Infinity, ease: \"linear\" }}\n        />\n        <p className=\"text-gray-400 text-sm\">{message}</p>\n      </div>\n    </div>\n  );\n};\n\nexport default ModernLoader;\n"], "names": [], "mappings": ";;;;AAGA;AACA;AAAA;AAAA;AAAA;AAJA;;;;AAYA,MAAM,eAA4C,CAAC,EACjD,UAAU,4BAA4B,EACtC,OAAO,SAAS,EAChB,YAAY,EAAE,EACf;IACC,IAAI,SAAS,QAAQ;QACnB,qBACE,8OAAC;YAAI,WAAW,CAAC,gGAAgG,EAAE,WAAW;sBAC5H,cAAA,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,WAAU;wBACV,SAAS;4BAAE,OAAO;4BAAK,SAAS;wBAAE;wBAClC,SAAS;4BAAE,OAAO;4BAAG,SAAS;wBAAE;wBAChC,YAAY;4BAAE,UAAU;wBAAI;;0CAE5B,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gCACT,WAAU;gCACV,SAAS;oCACP,QAAQ;wCAAC;wCAAG;qCAAI;oCAChB,OAAO;wCAAC;wCAAG;wCAAK;qCAAE;gCACpB;gCACA,YAAY;oCACV,QAAQ;wCAAE,UAAU;wCAAG,QAAQ;wCAAU,MAAM;oCAAS;oCACxD,OAAO;wCAAE,UAAU;wCAAG,QAAQ;wCAAU,MAAM;oCAAY;gCAC5D;0CAEA,cAAA,8OAAC,kMAAA,CAAA,OAAI;oCAAC,MAAM;oCAAI,WAAU;;;;;;;;;;;4BAI3B;mCAAI,MAAM;6BAAG,CAAC,GAAG,CAAC,CAAC,GAAG,kBACrB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oCAET,WAAU;oCACV,OAAO;wCACL,MAAM,GAAG,KAAK,KAAK,GAAG,CAAC,IAAI,KAAK,KAAK,EAAE,GAAG,OAAO,GAAG,EAAE,CAAC;wCACvD,KAAK,GAAG,KAAK,KAAK,GAAG,CAAC,IAAI,KAAK,KAAK,EAAE,GAAG,OAAO,GAAG,EAAE,CAAC;oCACxD;oCACA,SAAS;wCACP,OAAO;4CAAC;4CAAG;4CAAG;yCAAE;wCAChB,SAAS;4CAAC;4CAAG;4CAAG;yCAAE;oCACpB;oCACA,YAAY;wCACV,UAAU;wCACV,QAAQ;wCACR,OAAO,IAAI;oCACb;8CAEA,cAAA,8OAAC,0MAAA,CAAA,WAAQ;wCAAC,MAAM;;;;;;mCAhBX;;;;;;;;;;;kCAsBX,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,SAAS;4BAAE,GAAG;4BAAI,SAAS;wBAAE;wBAC7B,SAAS;4BAAE,GAAG;4BAAG,SAAS;wBAAE;wBAC5B,YAAY;4BAAE,UAAU;4BAAK,OAAO;wBAAI;;0CAExC,8OAAC;gCAAG,WAAU;;oCAAsC;kDAC9C,8OAAC;wCAAK,WAAU;kDAAe;;;;;;oCAAa;;;;;;;0CAElD,8OAAC;gCAAE,WAAU;0CAAyB;;;;;;;;;;;;kCAIxC,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,WAAU;wBACV,SAAS;4BAAE,SAAS;wBAAE;wBACtB,SAAS;4BAAE,SAAS;wBAAE;wBACtB,YAAY;4BAAE,OAAO;wBAAI;kCAEzB,cAAA,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,WAAU;4BACV,SAAS;gCAAE,GAAG;oCAAC;oCAAS;iCAAO;4BAAC;4BAChC,YAAY;gCAAE,UAAU;gCAAK,QAAQ;gCAAU,MAAM;4BAAY;;;;;;;;;;;;;;;;;;;;;;IAM7E;IAEA,IAAI,SAAS,WAAW;QACtB,qBACE,8OAAC;YAAI,WAAW,CAAC,+IAA+I,EAAE,WAAW;sBAC3K,cAAA,8OAAC;gBAAI,WAAU;;kCACb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,WAAU;wBACV,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAG;wBAC7B,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAE;wBAC5B,YAAY;4BAAE,UAAU;wBAAI;;0CAE5B,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gCACT,SAAS;oCAAE,QAAQ;gCAAI;gCACvB,YAAY;oCAAE,UAAU;oCAAG,QAAQ;oCAAU,MAAM;gCAAS;0CAE5D,cAAA,8OAAC,kMAAA,CAAA,OAAI;oCAAC,WAAU;;;;;;;;;;;0CAElB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gCACT,SAAS;oCAAE,QAAQ,CAAC;gCAAI;gCACxB,YAAY;oCAAE,UAAU;oCAAK,QAAQ;oCAAU,MAAM;gCAAS;0CAE9D,cAAA,8OAAC,8LAAA,CAAA,KAAE;oCAAC,WAAU;;;;;;;;;;;;;;;;;kCAIlB,8OAAC,0LAAA,CAAA,SAAM,CAAC,CAAC;wBACP,WAAU;wBACV,SAAS;4BAAE,SAAS;wBAAE;wBACtB,SAAS;4BAAE,SAAS;wBAAE;wBACtB,YAAY;4BAAE,OAAO;wBAAI;kCAExB;;;;;;;;;;;;;;;;;IAKX;IAEA,iBAAiB;IACjB,qBACE,8OAAC;QAAI,WAAW,CAAC,qCAAqC,EAAE,WAAW;kBACjE,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,WAAU;oBACV,SAAS;wBAAE,QAAQ;oBAAI;oBACvB,YAAY;wBAAE,UAAU;wBAAG,QAAQ;wBAAU,MAAM;oBAAS;;;;;;8BAE9D,8OAAC;oBAAE,WAAU;8BAAyB;;;;;;;;;;;;;;;;;AAI9C;uCAEe", "debugId": null}}, {"offset": {"line": 4814, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/New%20folder%20%2825%29/streamzen/src/components/ClientInitializeButton.tsx"], "sourcesContent": ["'use client';\n\nimport dynamic from 'next/dynamic';\n\nconst InitializeButton = dynamic(() => import('./InitializeButton'), {\n  ssr: false,\n  loading: () => (\n    <div className=\"animate-pulse bg-gray-700 h-12 w-48 rounded-lg mx-auto\"></div>\n  )\n});\n\nexport default InitializeButton;\n"], "names": [], "mappings": ";;;;AAEA;;AAFA;;;AAIA,MAAM,mBAAmB,CAAA,GAAA,+JAAA,CAAA,UAAO,AAAD;;;;;;IAC7B,KAAK;IACL,SAAS,kBACP,8OAAC;YAAI,WAAU;;;;;;;uCAIJ", "debugId": null}}]}