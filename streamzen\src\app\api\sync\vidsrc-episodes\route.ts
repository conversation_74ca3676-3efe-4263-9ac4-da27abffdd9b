import { NextRequest, NextResponse } from 'next/server';
import connectDB from '@/lib/mongodb';
import Series from '@/models/Series';
import Episode from '@/models/Episode';
import VidSrcAPI from '@/lib/vidsrc';
import IMDbScraper from '@/lib/scraper';

const vidsrc = VidSrcAPI.getInstance();
const imdbScraper = IMDbScraper.getInstance();

export async function POST(request: NextRequest) {
  try {
    await connectDB();
    
    console.log('🚀 Starting VidSrc Episodes Sync - Parallel Processing');
    const startTime = Date.now();
    
    // **PARALLEL FETCH** - Fetch all 15 pages simultaneously
    console.log('📄 Fetching VidSrc episodes pages 1-15 in parallel...');
    
    const pagePromises = [];
    for (let page = 1; page <= 15; page++) {
      pagePromises.push(
        vidsrc.getLatestEpisodes(page).catch(error => {
          console.error(`❌ Error fetching page ${page}:`, error);
          return []; // Return empty array on error
        })
      );
    }
    
    // Wait for all pages to complete
    const pageResults = await Promise.all(pagePromises);
    const allEpisodes = pageResults.flat();
    
    console.log(`✅ Parallel fetch complete: ${allEpisodes.length} episodes from 15 pages`);
    
    if (allEpisodes.length === 0) {
      return NextResponse.json({
        success: false,
        message: 'No episodes found from VidSrc API'
      });
    }
    
    // Get unique IMDb IDs
    const uniqueImdbIds = [...new Set(allEpisodes.map(ep => ep.imdb_id))];
    console.log(`🎬 Found ${uniqueImdbIds.length} unique series`);
    
    // **PARALLEL SERIES PROCESSING** - Process series in batches
    const batchSize = 10; // Process 10 series at a time
    const processedSeries = new Map();
    
    for (let i = 0; i < uniqueImdbIds.length; i += batchSize) {
      const batch = uniqueImdbIds.slice(i, i + batchSize);
      console.log(`🔄 Processing series batch ${Math.floor(i/batchSize) + 1}/${Math.ceil(uniqueImdbIds.length/batchSize)}`);
      
      const batchPromises = batch.map(async (imdbId) => {
        try {
          // Check if series exists
          let series = await Series.findOne({ imdbId });
          
          if (!series) {
            console.log(`🆕 Creating series: ${imdbId}`);
            
            // Find episode data for this series
            const episodeData = allEpisodes.find(ep => ep.imdb_id === imdbId);
            
            try {
              // Scrape series data
              const seriesData = await imdbScraper.scrapeSeries(imdbId);
              const embedUrl = `https://vidsrc.me/embed/tv?imdb=${imdbId}`;
              const currentYear = new Date().getFullYear();
              
              series = new Series({
                imdbId,
                title: seriesData.title || episodeData?.show_title || 'Unknown Series',
                description: seriesData.description,
                posterUrl: seriesData.posterUrl,
                imdbRating: seriesData.imdbRating,
                startYear: seriesData.startYear || currentYear,
                endYear: seriesData.endYear,
                genres: seriesData.genres || [],
                cast: seriesData.cast || [],
                director: seriesData.director,
                language: seriesData.language,
                country: seriesData.country,
                runtime: seriesData.runtime,
                embedUrl,
                type: 'series'
              });
              
              await series.save();
              console.log(`✅ Created: ${series.title}`);
            } catch (error) {
              // Create minimal series if scraping fails
              const embedUrl = `https://vidsrc.me/embed/tv?imdb=${imdbId}`;
              const currentYear = new Date().getFullYear();
              
              series = new Series({
                imdbId,
                title: episodeData?.show_title || 'Unknown Series',
                startYear: currentYear,
                embedUrl,
                type: 'series',
                genres: []
              });
              
              await series.save();
              console.log(`✅ Created minimal: ${series.title}`);
            }
          }
          
          processedSeries.set(imdbId, series);
          return { imdbId, success: true };
        } catch (error) {
          console.error(`❌ Error processing series ${imdbId}:`, error);
          return { imdbId, success: false, error: error.message };
        }
      });
      
      await Promise.all(batchPromises);
    }
    
    console.log(`✅ Series processing complete: ${processedSeries.size} series processed`);
    
    // Update sync timestamp
    const syncTime = new Date();
    
    const endTime = Date.now();
    const duration = (endTime - startTime) / 1000;
    
    console.log(`🎉 VidSrc Episodes Sync Complete in ${duration}s`);
    
    return NextResponse.json({
      success: true,
      message: `Sync completed successfully`,
      stats: {
        totalEpisodes: allEpisodes.length,
        uniqueSeries: uniqueImdbIds.length,
        processedSeries: processedSeries.size,
        duration: `${duration}s`,
        syncTime: syncTime.toISOString()
      }
    });
    
  } catch (error) {
    console.error('❌ VidSrc Episodes Sync Error:', error);
    return NextResponse.json(
      { 
        success: false,
        error: 'Sync failed',
        message: error.message
      },
      { status: 500 }
    );
  }
}

// GET endpoint to check last sync status
export async function GET() {
  try {
    await connectDB();
    
    const totalSeries = await Series.countDocuments({ type: 'series' });
    const totalEpisodes = await Episode.countDocuments({});
    
    return NextResponse.json({
      success: true,
      stats: {
        totalSeries,
        totalEpisodes,
        lastSync: 'Check logs for last sync time'
      }
    });
  } catch (error) {
    return NextResponse.json(
      { error: 'Failed to get sync status' },
      { status: 500 }
    );
  }
}
