import { Suspense } from 'react';
import { Metadata } from 'next';
import ContentGrid from '@/components/ContentGrid';
import ModernFilterSystem from '@/components/ModernFilterSystem';
import LoadingSpinner from '@/components/LoadingSpinner';
import ClientSearchBar from '@/components/ClientSearchBar';
import { SEOGenerator } from '@/lib/seo';
import { SchemaGenerator } from '@/lib/schema';
import connectDB from '@/lib/mongodb';
import Series from '@/models/Series';
import Episode from '@/models/Episode';

interface EpisodesPageProps {
  searchParams: Promise<{
    page?: string;
    genre?: string;
    language?: string;
    country?: string;
    quality?: string;
    sortBy?: string;
    sortOrder?: string;
    search?: string;
  }>;
}

export async function generateMetadata({ searchParams }: EpisodesPageProps): Promise<Metadata> {
  const resolvedSearchParams = await searchParams;
  const { genre, language, country, search, page } = resolvedSearchParams;

  let title = 'Latest Episodes - Watch TV Episodes Online Free';
  let description = 'Watch the latest TV episodes online free in HD quality. Discover new episodes from your favorite series and shows updated daily.';
  let keywords = ['latest episodes', 'watch episodes online', 'free episodes', 'HD episodes', 'TV episodes', 'new episodes'];

  // Dynamic title and description based on filters
  if (search) {
    title = `Search Results for "${search}" - Episodes`;
    description = `Search results for "${search}" episodes. Watch ${search} episodes online free in HD quality on StreamZen.`;
    keywords.push(search, `${search} episodes`, `watch ${search}`);
  } else if (genre) {
    title = `Latest ${genre} Episodes - Watch Online Free`;
    description = `Watch the latest ${genre} episodes online free in HD quality. Discover new ${genre} episodes from your favorite series.`;
    keywords.push(genre, `${genre} episodes`, `latest ${genre} episodes`);
  } else if (language) {
    title = `Latest ${language} Episodes - Watch Online Free`;
    description = `Watch the latest ${language} episodes online free in HD quality. Discover new ${language} episodes from your favorite series.`;
    keywords.push(language, `${language} episodes`, `latest ${language} episodes`);
  } else if (country) {
    title = `Latest ${country} Episodes - Watch Online Free`;
    description = `Watch the latest ${country} episodes online free in HD quality. Discover new ${country} episodes from your favorite series.`;
    keywords.push(country, `${country} episodes`, `latest ${country} episodes`);
  }

  if (page && parseInt(page) > 1) {
    title += ` - Page ${page}`;
    description += ` Browse page ${page} for more episodes.`;
  }

  const path = `/episodes${Object.keys(resolvedSearchParams).length > 0 ? '?' + new URLSearchParams(resolvedSearchParams as any).toString() : ''}`;

  return SEOGenerator.generatePageMetadata(title, description, path, keywords);
}

async function getEpisodes(searchParams: Awaited<EpisodesPageProps['searchParams']>) {
  try {
    await connectDB();

    console.log('🔍 Fetching latest episodes from database (fast!)...');

    // Debug: Check database counts
    const totalSeries = await Series.countDocuments({ type: 'series' });
    const totalEpisodes = await Episode.countDocuments({});
    const uniqueSeriesInEpisodes = await Episode.distinct('imdbId');
    console.log(`📊 Database stats: ${totalSeries} series, ${totalEpisodes} episodes`);
    console.log(`📊 Unique series in episodes: ${uniqueSeriesInEpisodes.length}`);

    // 🔬 DEEP RESEARCH: Investigate data structure for language/country filtering
    console.log('🔬 DEEP RESEARCH - Investigating episode data structure...');

    const sampleEpisodes = await Episode.find({}).limit(5).lean();
    console.log('📝 Sample episode data structure:');
    sampleEpisodes.forEach((ep, index) => {
      console.log(`Episode ${index + 1}:`, {
        seriesTitle: ep.seriesTitle,
        language: ep.language,
        country: ep.country,
        genres: ep.genres,
        imdbId: ep.imdbId,
        // Show all available fields
        allFields: Object.keys(ep).filter(key => !['_id', '__v', 'createdAt', 'updatedAt'].includes(key))
      });
    });

    // Check language variations in database
    const languageVariations = await Episode.aggregate([
      { $group: { _id: '$language', count: { $sum: 1 } } },
      { $sort: { count: -1 } },
      { $limit: 10 }
    ]);
    console.log('🌍 Language variations found:', languageVariations);

    // Check country variations in database
    const countryVariations = await Episode.aggregate([
      { $group: { _id: '$country', count: { $sum: 1 } } },
      { $sort: { count: -1 } },
      { $limit: 10 }
    ]);
    console.log('🏳️ Country variations found:', countryVariations);

    // **NEW APPROACH**: Start with Episodes collection since we have episodes but no series
    console.log('🔄 Using Episodes-first approach since we have episodes but no series');

    // Build query for episodes (BEFORE series lookup - only episode fields)
    const episodeMatchStage: any = {};

    if (searchParams.search) {
      episodeMatchStage.$or = [
        { seriesTitle: { $regex: searchParams.search, $options: 'i' } },
        { episodeTitle: { $regex: searchParams.search, $options: 'i' } },
        { description: { $regex: searchParams.search, $options: 'i' } }
      ];
    }

    if (searchParams.genre) {
      episodeMatchStage.genres = { $in: [searchParams.genre] };
    }

    // NOTE: language and country are NOT in Episode model - they're in Series model
    // So we'll filter by these AFTER the series lookup

    console.log('🔍 Episodes page filters applied:', {
      search: searchParams.search,
      genre: searchParams.genre,
      language: searchParams.language,
      country: searchParams.country,
      episodeMatchStage
    });

    // 🔬 DEEP RESEARCH: Test filter matching before aggregation
    if (searchParams.language) {
      const directLanguageCount = await Episode.countDocuments({ language: searchParams.language });
      console.log(`🧪 Direct language filter test - "${searchParams.language}": ${directLanguageCount} episodes`);

      // Test case variations
      const languageVariations = [
        searchParams.language,
        searchParams.language.toLowerCase(),
        searchParams.language.toUpperCase(),
        searchParams.language.charAt(0).toUpperCase() + searchParams.language.slice(1).toLowerCase()
      ];

      for (const variation of languageVariations) {
        const count = await Episode.countDocuments({ language: variation });
        if (count > 0) {
          console.log(`✅ Language variation "${variation}": ${count} episodes`);
        }
      }
    }

    if (searchParams.country) {
      const directCountryCount = await Episode.countDocuments({ country: searchParams.country });
      console.log(`🧪 Direct country filter test - "${searchParams.country}": ${directCountryCount} episodes`);
    }

    // Aggregation to get latest episode from each series with series poster
    const aggregationPipeline = [
      { $match: episodeMatchStage }, // ✅ Use episode-only filters first
      // Sort episodes by series, then by latest episode
      {
        $sort: {
          imdbId: 1,
          season: -1,
          episode: -1,
          createdAt: -1
        }
      },
      // Group by series (imdbId) and get the latest episode
      {
        $group: {
          _id: '$imdbId',
          latestEpisode: { $first: '$$ROOT' }
        }
      },
      // Replace root with the latest episode
      {
        $replaceRoot: { newRoot: '$latestEpisode' }
      },
      // Join with Series collection to get series poster
      {
        $lookup: {
          from: 'series',
          localField: 'imdbId',
          foreignField: 'imdbId',
          as: 'seriesInfo'
        }
      },
      // ✅ Add series information including language/country for filtering
      {
        $addFields: {
          seriesPosterUrl: { $arrayElemAt: ['$seriesInfo.posterUrl', 0] },
          seriesLanguage: { $arrayElemAt: ['$seriesInfo.language', 0] }, // ✅ ADDED
          seriesCountry: { $arrayElemAt: ['$seriesInfo.country', 0] },   // ✅ ADDED
          seriesTitle: {
            $cond: {
              if: { $ne: [{ $arrayElemAt: ['$seriesInfo.title', 0] }, null] },
              then: { $arrayElemAt: ['$seriesInfo.title', 0] },
              else: '$seriesTitle'
            }
          },
          // Use series poster if episode doesn't have one
          posterUrl: {
            $cond: {
              if: { $and: [{ $ne: ['$posterUrl', ''] }, { $ne: ['$posterUrl', null] }] },
              then: '$posterUrl',
              else: { $arrayElemAt: ['$seriesInfo.posterUrl', 0] }
            }
          }
        }
      },
      // ✅ NOW apply language/country filters AFTER getting series data
      ...(searchParams.language || searchParams.country ? [{
        $match: {
          ...(searchParams.language && { seriesLanguage: searchParams.language }),
          ...(searchParams.country && { seriesCountry: searchParams.country })
        }
      }] : []),
      // Clean up the response
      {
        $project: {
          seriesInfo: 0,
          seriesLanguage: 0, // ✅ Remove temp fields
          seriesCountry: 0   // ✅ Remove temp fields
        }
      },
      // Sort by creation date (newest first)
      { $sort: { createdAt: -1 } },
      // Limit to reasonable number for performance
      { $limit: 1000 }
    ];

    const rawEpisodes = await Episode.aggregate(aggregationPipeline);

    // 🔬 DEEP RESEARCH: Debug aggregation results
    console.log(`🔍 Aggregation pipeline results: ${rawEpisodes.length} episodes found`);

    if (rawEpisodes.length === 0 && (searchParams.language || searchParams.country)) {
      console.log('🚨 ZERO RESULTS - Debugging aggregation pipeline...');

      // Test each stage of the pipeline
      const stageResults = [];

      // Stage 1: Initial match (episode filters only)
      const stage1 = await Episode.aggregate([{ $match: episodeMatchStage }]);
      stageResults.push({ stage: 'Initial Match (Episodes)', count: stage1.length });

      // Stage 2: After sorting
      const stage2 = await Episode.aggregate([
        { $match: episodeMatchStage },
        { $sort: { imdbId: 1, season: -1, episode: -1, createdAt: -1 } }
      ]);
      stageResults.push({ stage: 'After Sort', count: stage2.length });

      // Stage 3: After grouping
      const stage3 = await Episode.aggregate([
        { $match: episodeMatchStage },
        { $sort: { imdbId: 1, season: -1, episode: -1, createdAt: -1 } },
        { $group: { _id: '$imdbId', latestEpisode: { $first: '$$ROOT' } } }
      ]);
      stageResults.push({ stage: 'After Grouping', count: stage3.length });

      // Stage 4: After series lookup and language/country filter
      if (searchParams.language || searchParams.country) {
        const stage4 = await Episode.aggregate([
          { $match: episodeMatchStage },
          { $sort: { imdbId: 1, season: -1, episode: -1, createdAt: -1 } },
          { $group: { _id: '$imdbId', latestEpisode: { $first: '$$ROOT' } } },
          { $replaceRoot: { newRoot: '$latestEpisode' } },
          {
            $lookup: {
              from: 'series',
              localField: 'imdbId',
              foreignField: 'imdbId',
              as: 'seriesInfo'
            }
          },
          {
            $addFields: {
              seriesLanguage: { $arrayElemAt: ['$seriesInfo.language', 0] },
              seriesCountry: { $arrayElemAt: ['$seriesInfo.country', 0] }
            }
          },
          {
            $match: {
              ...(searchParams.language && { seriesLanguage: searchParams.language }),
              ...(searchParams.country && { seriesCountry: searchParams.country })
            }
          }
        ]);
        stageResults.push({ stage: 'After Series Filter', count: stage4.length });
      }

      console.log('📊 Pipeline stage results:', stageResults);

      if (stage1.length > 0) {
        console.log('📝 Sample matched episodes:', stage1.slice(0, 3).map(ep => ({
          seriesTitle: ep.seriesTitle,
          language: ep.language,
          country: ep.country,
          imdbId: ep.imdbId
        })));
      }
    }

    // Convert MongoDB documents to plain objects (fix serialization issue)
    const episodes = rawEpisodes.map(episode => ({
      _id: episode._id.toString(), // Convert ObjectId to string
      imdbId: episode.imdbId,
      seriesTitle: episode.seriesTitle,
      season: episode.season,
      episode: episode.episode,
      episodeTitle: episode.episodeTitle,
      description: episode.description,
      posterUrl: episode.posterUrl || episode.seriesPosterUrl, // Use series poster as fallback
      seriesPosterUrl: episode.seriesPosterUrl,
      runtime: episode.runtime,
      imdbRating: episode.imdbRating,
      airDate: episode.airDate,
      embedUrl: episode.embedUrl,
      genres: episode.genres || [],
      createdAt: episode.createdAt,
      updatedAt: episode.updatedAt
    }));

    console.log(`✅ Episodes-first query complete: ${episodes.length} latest episodes`);

    // Debug: Show sample results with creation dates
    if (episodes.length > 0) {
      console.log('📝 Sample episodes with poster info:', episodes.slice(0, 3).map(ep => ({
        seriesTitle: ep.seriesTitle,
        season: ep.season,
        episode: ep.episode,
        imdbId: ep.imdbId,
        hasPoster: !!ep.posterUrl,
        hasSeriesPoster: !!ep.seriesPosterUrl,
        createdAt: ep.createdAt,
        hasGenres: ep.genres?.length || 0
      })));

      // Check when episodes were created
      const recentEpisodes = await Episode.countDocuments({
        createdAt: { $gte: new Date(Date.now() - 24 * 60 * 60 * 1000) } // Last 24 hours
      });
      console.log(`📅 Episodes created in last 24 hours: ${recentEpisodes}`);

      // Check unique series count
      const uniqueSeriesCount = await Episode.distinct('imdbId');
      console.log(`🎬 Total unique series in episodes: ${uniqueSeriesCount.length}`);

    } else {
      console.log('⚠️ No episodes found - checking raw episode data...');
      const sampleEpisodes = await Episode.find({}).limit(5).select('seriesTitle season episode imdbId genres createdAt');
      console.log('📝 Raw episodes sample:', sampleEpisodes);
    }

    return {
      data: episodes,
      pagination: {
        page: 1,
        limit: episodes.length,
        total: episodes.length,
        pages: 1
      }
    };

  } catch (error) {
    console.error('Error fetching episodes from database:', error);
    return {
      data: [],
      pagination: {
        page: 1,
        limit: 0,
        total: 0,
        pages: 0
      }
    };
  }
}

function transformEpisodeToContentItem(episode: any) {
  return {
    id: episode._id,
    imdbId: episode.imdbId,
    title: episode.episodeTitle || `Episode ${episode.episode}`,
    season: episode.season,
    episode: episode.episode,
    seriesTitle: episode.seriesTitle,
    posterUrl: episode.posterUrl,
    seriesPosterUrl: episode.seriesPosterUrl, // Use series poster for episodes
    imdbRating: episode.imdbRating,
    description: episode.description,
    type: 'episode' as const
  };
}

export default async function EpisodesPage({ searchParams }: EpisodesPageProps) {
  const resolvedSearchParams = await searchParams;
  const { data: episodes, pagination } = await getEpisodes(resolvedSearchParams);

  return (
    <div className="min-h-screen bg-black">
      {/* Enhanced Hero Header */}
      <div className="relative bg-black border-b border-gray-800/50 overflow-hidden">
        {/* Premium Background Effects */}
        <div className="absolute inset-0">
          <div className="absolute top-0 left-1/3 w-96 h-96 bg-orange-900/20 rounded-full blur-3xl animate-pulse" />
          <div className="absolute top-1/2 right-1/3 w-80 h-80 bg-gray-800/15 rounded-full blur-3xl animate-pulse" style={{ animationDelay: '1s' }} />
          <div className="absolute bottom-0 left-1/2 transform -translate-x-1/2 w-[600px] h-32 bg-gradient-to-t from-gray-900/30 to-transparent blur-xl" />
        </div>

        <div className="relative max-w-[2560px] mx-auto px-6 lg:px-12 py-16 lg:py-24">
          <div className="mb-12">
            {/* Premium Title with Gradient Text */}
            <div className="mb-6">
              <h1 className="text-5xl lg:text-7xl xl:text-8xl font-black text-transparent bg-clip-text bg-gradient-to-r from-white via-orange-200 to-gray-400 mb-6 tracking-tight leading-none">
                Latest Episodes
              </h1>
              <div className="w-24 h-1 bg-gradient-to-r from-orange-500 to-gray-600 rounded-full mb-8"></div>
            </div>

            <p className="text-xl lg:text-2xl text-gray-300 max-w-3xl leading-relaxed font-light mb-8">
              Discover the latest episodes synced daily from VidSrc. Lightning-fast loading with comprehensive series metadata and automatic updates.
            </p>


          </div>

          {/* Enhanced Stats Cards */}
          <div className="flex flex-wrap items-center gap-6">
            <div className="glass-elevated px-6 py-4 rounded-2xl border border-gray-700/50 backdrop-blur-sm hover:border-gray-600/50 transition-all duration-300">
              <div className="flex items-center space-x-3">
                <div className="w-3 h-3 bg-orange-500 rounded-full animate-pulse"></div>
                <span className="text-orange-400 font-bold text-lg">{pagination.total.toLocaleString()}</span>
                <span className="text-gray-400 text-lg">Episodes Available</span>
              </div>
            </div>
            <div className="glass-elevated px-6 py-4 rounded-2xl border border-gray-700/50 backdrop-blur-sm hover:border-gray-600/50 transition-all duration-300">
              <div className="flex items-center space-x-3">
                <div className="w-3 h-3 bg-green-500 rounded-full animate-pulse" style={{ animationDelay: '1s' }}></div>
                <span className="text-green-400 font-bold text-lg">Fresh Daily</span>
                <span className="text-gray-400 text-lg">New Episodes</span>
              </div>
            </div>
            <div className="glass-elevated px-6 py-4 rounded-2xl border border-gray-700/50 backdrop-blur-sm hover:border-gray-600/50 transition-all duration-300">
              <div className="flex items-center space-x-3">
                <div className="w-3 h-3 bg-blue-500 rounded-full animate-pulse" style={{ animationDelay: '2s' }}></div>
                <span className="text-blue-400 font-bold text-lg">Auto-Sync</span>
                <span className="text-gray-400 text-lg">Real-time Updates</span>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Enhanced Main Content */}
      <div className="relative">
        {/* Subtle Background Effects */}
        <div className="absolute inset-0 overflow-hidden pointer-events-none">
          <div className="absolute top-1/3 right-1/3 w-72 h-72 bg-orange-800/5 rounded-full blur-3xl" />
          <div className="absolute bottom-1/3 left-1/3 w-64 h-64 bg-gray-700/5 rounded-full blur-3xl" />
        </div>

        <div className="relative max-w-[2560px] mx-auto px-6 lg:px-12 py-12">
          <div className="space-y-8">
            {/* Page Header */}
            <div className="flex items-center justify-between">
              <div>
                <h1 className="text-4xl lg:text-5xl font-black text-white mb-2">
                  {resolvedSearchParams.search ? 'Search Results' : 'Latest Episodes'}
                </h1>
                {resolvedSearchParams.search && (
                  <p className="text-gray-400 text-lg">
                    Results for "{resolvedSearchParams.search}"
                  </p>
                )}
              </div>
              <div className="glass-elevated px-4 py-2 rounded-xl border border-gray-700/50">
                <span className="text-gray-300 text-base font-medium">
                  {pagination.total.toLocaleString()} latest episodes from VidSrc
                </span>
              </div>
            </div>

            {/* Modern Filter System */}
            <div className="glass-elevated p-6 rounded-2xl border border-gray-700/50">
              <ModernFilterSystem
                currentFilters={resolvedSearchParams}
                basePath="/episodes"
                contentType="episodes"
              />
            </div>

            {/* Content Grid */}
            <Suspense fallback={<LoadingSpinner />}>
              <ContentGrid
                items={episodes.map(transformEpisodeToContentItem)}
                pagination={pagination}
                basePath="/episodes"
                currentFilters={resolvedSearchParams}
              />
            </Suspense>
          </div>
        </div>
      </div>
    </div>
  );
}
