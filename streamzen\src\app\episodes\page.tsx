import { Suspense } from 'react';
import { Metadata } from 'next';
import { apiClient } from '@/lib/api';
import ContentGrid from '@/components/ContentGrid';
import ModernFilterSystem from '@/components/ModernFilterSystem';
import LoadingSpinner from '@/components/LoadingSpinner';
import ClientSearchBar from '@/components/ClientSearchBar';
import { SEOGenerator } from '@/lib/seo';
import { SchemaGenerator } from '@/lib/schema';

interface EpisodesPageProps {
  searchParams: Promise<{
    page?: string;
    genre?: string;
    quality?: string;
    sortBy?: string;
    sortOrder?: string;
    search?: string;
  }>;
}

export async function generateMetadata({ searchParams }: EpisodesPageProps): Promise<Metadata> {
  const resolvedSearchParams = await searchParams;
  const { genre, search, page } = resolvedSearchParams;

  let title = 'Latest Episodes - Watch TV Episodes Online Free';
  let description = 'Watch the latest TV episodes online free in HD quality. Discover new episodes from your favorite series and shows updated daily.';
  let keywords = ['latest episodes', 'watch episodes online', 'free episodes', 'HD episodes', 'TV episodes', 'new episodes'];

  // Dynamic title and description based on filters
  if (search) {
    title = `Search Results for "${search}" - Episodes`;
    description = `Search results for "${search}" episodes. Watch ${search} episodes online free in HD quality on StreamZen.`;
    keywords.push(search, `${search} episodes`, `watch ${search}`);
  } else if (genre) {
    title = `Latest ${genre} Episodes - Watch Online Free`;
    description = `Watch the latest ${genre} episodes online free in HD quality. Discover new ${genre} episodes from your favorite series.`;
    keywords.push(genre, `${genre} episodes`, `latest ${genre} episodes`);
  }

  if (page && parseInt(page) > 1) {
    title += ` - Page ${page}`;
    description += ` Browse page ${page} for more episodes.`;
  }

  const path = `/episodes${Object.keys(resolvedSearchParams).length > 0 ? '?' + new URLSearchParams(resolvedSearchParams as any).toString() : ''}`;

  return SEOGenerator.generatePageMetadata(title, description, path, keywords);
}

async function getEpisodes(searchParams: Awaited<EpisodesPageProps['searchParams']>) {
  try {
    const filters = {
      page: searchParams.page ? parseInt(searchParams.page) : 1,
      limit: 24,
      genre: searchParams.genre,
      quality: searchParams.quality,
      sortBy: (searchParams.sortBy as any) || 'createdAt',
      sortOrder: (searchParams.sortOrder as 'asc' | 'desc') || 'desc',
      search: searchParams.search,
    };

    const response = await apiClient.getEpisodes(filters);
    return response;
  } catch (error) {
    console.error('Error fetching episodes:', error);
    return {
      data: [],
      pagination: {
        page: 1,
        limit: 24,
        total: 0,
        pages: 0
      }
    };
  }
}

function transformEpisodeToContentItem(episode: any) {
  return {
    id: episode._id,
    imdbId: episode.imdbId,
    title: episode.episodeTitle || `Episode ${episode.episode}`,
    season: episode.season,
    episode: episode.episode,
    seriesTitle: episode.seriesTitle,
    posterUrl: episode.posterUrl,
    seriesPosterUrl: episode.seriesPosterUrl, // Use series poster for episodes
    imdbRating: episode.imdbRating,
    description: episode.description,
    type: 'episode' as const
  };
}

export default async function EpisodesPage({ searchParams }: EpisodesPageProps) {
  const resolvedSearchParams = await searchParams;
  const { data: episodes, pagination } = await getEpisodes(resolvedSearchParams);

  return (
    <div className="min-h-screen bg-black">
      {/* Enhanced Hero Header */}
      <div className="relative bg-black border-b border-gray-800/50 overflow-hidden">
        {/* Premium Background Effects */}
        <div className="absolute inset-0">
          <div className="absolute top-0 left-1/3 w-96 h-96 bg-orange-900/20 rounded-full blur-3xl animate-pulse" />
          <div className="absolute top-1/2 right-1/3 w-80 h-80 bg-gray-800/15 rounded-full blur-3xl animate-pulse" style={{ animationDelay: '1s' }} />
          <div className="absolute bottom-0 left-1/2 transform -translate-x-1/2 w-[600px] h-32 bg-gradient-to-t from-gray-900/30 to-transparent blur-xl" />
        </div>

        <div className="relative max-w-[2560px] mx-auto px-6 lg:px-12 py-16 lg:py-24">
          <div className="mb-12">
            {/* Premium Title with Gradient Text */}
            <div className="mb-6">
              <h1 className="text-5xl lg:text-7xl xl:text-8xl font-black text-transparent bg-clip-text bg-gradient-to-r from-white via-orange-200 to-gray-400 mb-6 tracking-tight leading-none">
                Latest Episodes
              </h1>
              <div className="w-24 h-1 bg-gradient-to-r from-orange-500 to-gray-600 rounded-full mb-8"></div>
            </div>

            <p className="text-xl lg:text-2xl text-gray-300 max-w-3xl leading-relaxed font-light mb-8">
              Stay up to date with the newest episodes from your favorite series. Never miss a moment of the action with our real-time episode updates.
            </p>


          </div>

          {/* Enhanced Stats Cards */}
          <div className="flex flex-wrap items-center gap-6">
            <div className="glass-elevated px-6 py-4 rounded-2xl border border-gray-700/50 backdrop-blur-sm hover:border-gray-600/50 transition-all duration-300">
              <div className="flex items-center space-x-3">
                <div className="w-3 h-3 bg-orange-500 rounded-full animate-pulse"></div>
                <span className="text-orange-400 font-bold text-lg">{pagination.total.toLocaleString()}</span>
                <span className="text-gray-400 text-lg">Episodes Available</span>
              </div>
            </div>
            <div className="glass-elevated px-6 py-4 rounded-2xl border border-gray-700/50 backdrop-blur-sm hover:border-gray-600/50 transition-all duration-300">
              <div className="flex items-center space-x-3">
                <div className="w-3 h-3 bg-green-500 rounded-full animate-pulse" style={{ animationDelay: '1s' }}></div>
                <span className="text-green-400 font-bold text-lg">Fresh Daily</span>
                <span className="text-gray-400 text-lg">New Episodes</span>
              </div>
            </div>
            <div className="glass-elevated px-6 py-4 rounded-2xl border border-gray-700/50 backdrop-blur-sm hover:border-gray-600/50 transition-all duration-300">
              <div className="flex items-center space-x-3">
                <div className="w-3 h-3 bg-blue-500 rounded-full animate-pulse" style={{ animationDelay: '2s' }}></div>
                <span className="text-blue-400 font-bold text-lg">Auto-Sync</span>
                <span className="text-gray-400 text-lg">Real-time Updates</span>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Enhanced Main Content */}
      <div className="relative">
        {/* Subtle Background Effects */}
        <div className="absolute inset-0 overflow-hidden pointer-events-none">
          <div className="absolute top-1/3 right-1/3 w-72 h-72 bg-orange-800/5 rounded-full blur-3xl" />
          <div className="absolute bottom-1/3 left-1/3 w-64 h-64 bg-gray-700/5 rounded-full blur-3xl" />
        </div>

        <div className="relative max-w-[2560px] mx-auto px-6 lg:px-12 py-12">
          <div className="space-y-8">
            {/* Page Header */}
            <div className="flex items-center justify-between">
              <div>
                <h1 className="text-4xl lg:text-5xl font-black text-white mb-2">
                  {resolvedSearchParams.search ? 'Search Results' : 'Latest Episodes'}
                </h1>
                {resolvedSearchParams.search && (
                  <p className="text-gray-400 text-lg">
                    Results for "{resolvedSearchParams.search}"
                  </p>
                )}
              </div>
              <div className="glass-elevated px-4 py-2 rounded-xl border border-gray-700/50">
                <span className="text-gray-300 text-base font-medium">
                  {pagination.total.toLocaleString()} episodes • Page {pagination.page} of {pagination.pages}
                </span>
              </div>
            </div>

            {/* Modern Filter System */}
            <div className="glass-elevated p-6 rounded-2xl border border-gray-700/50">
              <ModernFilterSystem
                currentFilters={resolvedSearchParams}
                basePath="/episodes"
                contentType="episodes"
              />
            </div>

            {/* Content Grid */}
            <Suspense fallback={<LoadingSpinner />}>
              <ContentGrid
                items={episodes.map(transformEpisodeToContentItem)}
                pagination={pagination}
                basePath="/episodes"
                currentFilters={resolvedSearchParams}
              />
            </Suspense>
          </div>
        </div>
      </div>
    </div>
  );
}
