'use client';

import React, { useState, useEffect } from 'react';
import Image from 'next/image';
import Link from 'next/link';
import { Play, Info, Star, ChevronLeft, ChevronRight, Sparkles, TrendingUp } from 'lucide-react';
import { motion, AnimatePresence } from 'framer-motion';
import { cn, getImageUrl, formatRating, truncateText } from '@/lib/utils';

interface HeroItem {
  id: string;
  imdbId: string;
  title: string;
  year?: number;
  posterUrl?: string;
  imdbRating?: number;
  description?: string;
  type: 'movie' | 'series';
}

interface PremiumHeroSectionProps {
  items: HeroItem[];
}

const PremiumHeroSection: React.FC<PremiumHeroSectionProps> = ({ items }) => {
  const [currentIndex, setCurrentIndex] = useState(0);
  const [isAutoPlaying, setIsAutoPlaying] = useState(true);
  const [isLoaded, setIsLoaded] = useState(false);

  // Auto-play functionality
  useEffect(() => {
    if (!isAutoPlaying || items.length <= 1) return;

    const interval = setInterval(() => {
      setCurrentIndex((prev) => (prev + 1) % items.length);
    }, 7000);

    return () => clearInterval(interval);
  }, [isAutoPlaying, items.length]);

  useEffect(() => {
    setIsLoaded(true);
  }, []);

  const currentItem = items[currentIndex];
  const watchHref = currentItem?.type === 'movie' 
    ? `/watch/movie/${currentItem.imdbId}` 
    : `/watch/series/${currentItem.imdbId}`;

  const nextSlide = () => {
    setCurrentIndex((prev) => (prev + 1) % items.length);
    setIsAutoPlaying(false);
    setTimeout(() => setIsAutoPlaying(true), 15000);
  };

  const prevSlide = () => {
    setCurrentIndex((prev) => (prev - 1 + items.length) % items.length);
    setIsAutoPlaying(false);
    setTimeout(() => setIsAutoPlaying(true), 15000);
  };

  if (!currentItem || items.length === 0) {
    return (
      <div className="relative h-screen bg-gradient-to-br from-gray-900 via-black to-gray-900 flex items-center justify-center">
        <div className="text-center">
          <Sparkles className="w-16 h-16 text-white/30 mx-auto mb-4 animate-pulse" />
          <h2 className="text-2xl font-bold text-white/50">Preparing your cinematic experience...</h2>
        </div>
      </div>
    );
  }

  return (
    <section className="relative h-screen overflow-hidden bg-black">
      {/* Background Image with Parallax Effect */}
      <AnimatePresence mode="wait">
        <motion.div
          key={currentIndex}
          className="absolute inset-0"
          initial={{ opacity: 0, scale: 1.05 }}
          animate={{ opacity: 1, scale: 1 }}
          exit={{ opacity: 0, scale: 0.95 }}
          transition={{ duration: 1.2, ease: "easeInOut" }}
        >
          <Image
            src={getImageUrl(currentItem.posterUrl)}
            alt={currentItem.title}
            fill
            className="object-cover object-center"
            priority
            sizes="100vw"
          />
          
          {/* Clean Gradient Overlays */}
          <div className="absolute inset-0 bg-gradient-to-r from-black via-black/60 to-transparent" />
          <div className="absolute inset-0 bg-gradient-to-t from-black/90 via-transparent to-black/20" />
        </motion.div>
      </AnimatePresence>

      {/* Main Content */}
      <div className="relative h-full flex items-center">
        <div className="max-w-7xl mx-auto px-6 lg:px-12 w-full">
          <div className="max-w-3xl">
            <AnimatePresence mode="wait">
              <motion.div
                key={currentIndex}
                initial={{ opacity: 0, y: 50 }}
                animate={{ opacity: 1, y: 0 }}
                exit={{ opacity: 0, y: -50 }}
                transition={{ duration: 0.8, ease: "easeOut" }}
                className="space-y-8"
              >
                {/* Minimal Badge */}
                <motion.div
                  initial={{ opacity: 0, x: -20 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ delay: 0.2, duration: 0.6 }}
                  className="flex items-center space-x-4"
                >
                  <div className="px-3 py-1 bg-white/10 border border-white/20 rounded-lg backdrop-blur-sm">
                    <span className="text-white/80 font-medium text-sm uppercase tracking-wider">
                      {currentItem.type === 'movie' ? 'Movie' : 'Series'}
                    </span>
                  </div>

                  {currentItem.imdbRating && (
                    <div className="flex items-center space-x-1">
                      <Star className="w-4 h-4 text-yellow-400 fill-current" />
                      <span className="text-white font-semibold">
                        {formatRating(currentItem.imdbRating)}
                      </span>
                    </div>
                  )}
                </motion.div>

                {/* Title */}
                <motion.div
                  initial={{ opacity: 0, y: 30 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: 0.4, duration: 0.8 }}
                  className="space-y-2"
                >
                  <h1 className="text-4xl lg:text-6xl xl:text-7xl font-bold text-white leading-tight tracking-tight">
                    {currentItem.title}
                  </h1>
                  {currentItem.year && (
                    <p className="text-xl text-gray-400 font-normal">
                      {currentItem.year}
                    </p>
                  )}
                </motion.div>

                {/* Description */}
                {currentItem.description && (
                  <motion.p
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: 0.6, duration: 0.6 }}
                    className="text-lg text-gray-300 leading-relaxed max-w-2xl"
                  >
                    {truncateText(currentItem.description, 160)}
                  </motion.p>
                )}

                {/* Action Buttons */}
                <motion.div
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: 0.8, duration: 0.6 }}
                  className="flex items-center space-x-4 pt-4"
                >
                  <Link href={watchHref}>
                    <motion.button
                      className="flex items-center space-x-3 px-6 py-3 bg-white text-black font-semibold rounded-lg hover:bg-gray-100 transition-all duration-300 shadow-lg"
                      whileHover={{ scale: 1.02 }}
                      whileTap={{ scale: 0.98 }}
                    >
                      <Play className="w-5 h-5 fill-current" />
                      <span>Watch Now</span>
                    </motion.button>
                  </Link>

                  <motion.button
                    className="flex items-center space-x-3 px-6 py-3 bg-white/10 text-white font-medium rounded-lg border border-white/20 backdrop-blur-sm hover:bg-white/20 transition-all duration-300"
                    whileHover={{ scale: 1.02 }}
                    whileTap={{ scale: 0.98 }}
                  >
                    <Info className="w-5 h-5" />
                    <span>More Info</span>
                  </motion.button>
                </motion.div>
              </motion.div>
            </AnimatePresence>
          </div>
        </div>
      </div>

      {/* Subtle Navigation Controls */}
      {items.length > 1 && (
        <>
          {/* Previous Button */}
          <motion.button
            onClick={prevSlide}
            className="absolute left-6 top-1/2 -translate-y-1/2 w-12 h-12 bg-white/10 backdrop-blur-sm border border-white/20 rounded-full flex items-center justify-center text-white hover:bg-white/20 transition-all duration-300 z-10 opacity-70 hover:opacity-100"
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
          >
            <ChevronLeft className="w-5 h-5" />
          </motion.button>

          {/* Next Button */}
          <motion.button
            onClick={nextSlide}
            className="absolute right-6 top-1/2 -translate-y-1/2 w-12 h-12 bg-white/10 backdrop-blur-sm border border-white/20 rounded-full flex items-center justify-center text-white hover:bg-white/20 transition-all duration-300 z-10 opacity-70 hover:opacity-100"
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
          >
            <ChevronRight className="w-5 h-5" />
          </motion.button>

          {/* Minimal Slide Indicators */}
          <div className="absolute bottom-6 left-1/2 -translate-x-1/2 flex space-x-2 z-10">
            {items.map((_, index) => (
              <button
                key={index}
                onClick={() => {
                  setCurrentIndex(index);
                  setIsAutoPlaying(false);
                  setTimeout(() => setIsAutoPlaying(true), 15000);
                }}
                className={cn(
                  'w-2 h-2 rounded-full transition-all duration-300',
                  index === currentIndex
                    ? 'bg-white scale-125'
                    : 'bg-white/30 hover:bg-white/50'
                )}
              />
            ))}
          </div>
        </>
      )}

      {/* Subtle Auto-play Progress Bar */}
      {isAutoPlaying && items.length > 1 && (
        <div className="absolute bottom-0 left-0 right-0 h-0.5 bg-white/5 z-10">
          <motion.div
            className="h-full bg-white/40"
            initial={{ width: '0%' }}
            animate={{ width: '100%' }}
            transition={{ duration: 7, ease: "linear" }}
            key={currentIndex}
          />
        </div>
      )}
    </section>
  );
};

export default PremiumHeroSection;
