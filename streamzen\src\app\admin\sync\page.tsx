'use client';

import { useState } from 'react';
import { RefreshCw, CheckCircle, XCircle, Clock, Database } from 'lucide-react';

export default function AdminSyncPage() {
  const [isLoading, setIsLoading] = useState(false);
  const [result, setResult] = useState<any>(null);
  const [error, setError] = useState<string | null>(null);

  const triggerSync = async () => {
    setIsLoading(true);
    setResult(null);
    setError(null);

    try {
      const response = await fetch('/api/admin/sync-episodes', {
        method: 'POST',
      });

      const data = await response.json();

      if (response.ok) {
        setResult(data);
      } else {
        setError(data.error || 'Sync failed');
      }
    } catch (err) {
      setError('Network error occurred');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-900 via-black to-gray-900">
      <div className="container mx-auto px-6 py-12">
        <div className="max-w-4xl mx-auto">
          {/* Header */}
          <div className="text-center mb-12">
            <h1 className="text-4xl lg:text-5xl font-bold text-white mb-4">
              VidSrc Episodes Sync
            </h1>
            <p className="text-xl text-gray-300">
              Manually trigger the daily episodes sync from VidSrc API
            </p>
          </div>

          {/* Sync Control */}
          <div className="bg-gradient-to-br from-gray-900/50 via-black/50 to-gray-900/50 backdrop-blur-xl rounded-3xl border border-white/10 p-8 mb-8">
            <div className="text-center">
              <div className="w-20 h-20 bg-gradient-to-br from-blue-500 to-purple-600 rounded-full flex items-center justify-center mx-auto mb-6">
                <Database className="w-10 h-10 text-white" />
              </div>
              
              <h2 className="text-2xl font-bold text-white mb-4">
                Episodes Database Sync
              </h2>
              
              <p className="text-gray-300 mb-8 max-w-2xl mx-auto">
                This will fetch the latest episodes from VidSrc API (pages 1-15) using parallel processing, 
                create missing series with IMDb metadata, and update the database.
              </p>

              <button
                onClick={triggerSync}
                disabled={isLoading}
                className={`
                  flex items-center space-x-3 px-8 py-4 rounded-xl font-semibold text-lg transition-all duration-300 mx-auto
                  ${isLoading 
                    ? 'bg-gray-600 text-gray-300 cursor-not-allowed' 
                    : 'bg-blue-600 hover:bg-blue-700 text-white shadow-lg hover:shadow-xl'
                  }
                `}
              >
                <RefreshCw className={`w-6 h-6 ${isLoading ? 'animate-spin' : ''}`} />
                <span>{isLoading ? 'Syncing...' : 'Start Sync'}</span>
              </button>
            </div>
          </div>

          {/* Loading State */}
          {isLoading && (
            <div className="bg-blue-500/20 border border-blue-500/30 rounded-xl p-6 mb-8">
              <div className="flex items-center space-x-3">
                <Clock className="w-6 h-6 text-blue-400 animate-pulse" />
                <div>
                  <h3 className="text-blue-400 font-semibold">Sync in Progress</h3>
                  <p className="text-blue-300 text-sm">
                    Fetching episodes from VidSrc and processing series data...
                  </p>
                </div>
              </div>
            </div>
          )}

          {/* Success Result */}
          {result && result.success && (
            <div className="bg-green-500/20 border border-green-500/30 rounded-xl p-6 mb-8">
              <div className="flex items-start space-x-3">
                <CheckCircle className="w-6 h-6 text-green-400 mt-1" />
                <div className="flex-1">
                  <h3 className="text-green-400 font-semibold mb-2">Sync Completed Successfully!</h3>
                  
                  {result.stats && (
                    <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mt-4">
                      <div className="bg-green-500/10 rounded-lg p-3">
                        <div className="text-green-400 text-2xl font-bold">
                          {result.stats.totalEpisodes || 0}
                        </div>
                        <div className="text-green-300 text-sm">Episodes Found</div>
                      </div>
                      
                      <div className="bg-green-500/10 rounded-lg p-3">
                        <div className="text-green-400 text-2xl font-bold">
                          {result.stats.uniqueSeries || 0}
                        </div>
                        <div className="text-green-300 text-sm">Unique Series</div>
                      </div>
                      
                      <div className="bg-green-500/10 rounded-lg p-3">
                        <div className="text-green-400 text-2xl font-bold">
                          {result.stats.processedSeries || 0}
                        </div>
                        <div className="text-green-300 text-sm">Processed</div>
                      </div>
                      
                      <div className="bg-green-500/10 rounded-lg p-3">
                        <div className="text-green-400 text-2xl font-bold">
                          {result.stats.duration || 'N/A'}
                        </div>
                        <div className="text-green-300 text-sm">Duration</div>
                      </div>
                    </div>
                  )}
                  
                  <p className="text-green-300 text-sm mt-4">
                    {result.message}
                  </p>
                </div>
              </div>
            </div>
          )}

          {/* Error Result */}
          {error && (
            <div className="bg-red-500/20 border border-red-500/30 rounded-xl p-6 mb-8">
              <div className="flex items-start space-x-3">
                <XCircle className="w-6 h-6 text-red-400 mt-1" />
                <div>
                  <h3 className="text-red-400 font-semibold">Sync Failed</h3>
                  <p className="text-red-300 text-sm mt-1">{error}</p>
                </div>
              </div>
            </div>
          )}

          {/* Instructions */}
          <div className="bg-gray-800/50 rounded-xl p-6">
            <h3 className="text-white font-semibold mb-4">How it works:</h3>
            <ul className="text-gray-300 space-y-2 text-sm">
              <li>• <strong>Parallel Fetching:</strong> Fetches all 15 VidSrc pages simultaneously</li>
              <li>• <strong>Series Creation:</strong> Creates missing series with full IMDb metadata</li>
              <li>• <strong>Batch Processing:</strong> Processes series in parallel batches for speed</li>
              <li>• <strong>Error Handling:</strong> Continues processing even if some operations fail</li>
              <li>• <strong>Database Update:</strong> Updates episodes page with fresh data</li>
            </ul>
            
            <div className="mt-4 p-4 bg-blue-500/10 rounded-lg">
              <p className="text-blue-300 text-sm">
                <strong>Note:</strong> This sync normally runs automatically once every 24 hours. 
                Use this manual trigger for testing or immediate updates.
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
