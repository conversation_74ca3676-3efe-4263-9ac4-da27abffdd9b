{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 24, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/New%20folder%20%2825%29/streamzen/src/components/EnhancedHeroSection.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/EnhancedHeroSection.tsx <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/EnhancedHeroSection.tsx <module evaluation>\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAA0S,GACvU,wEACA", "debugId": null}}, {"offset": {"line": 38, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/New%20folder%20%2825%29/streamzen/src/components/EnhancedHeroSection.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/EnhancedHeroSection.tsx from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/EnhancedHeroSection.tsx\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAAsR,GACnT,oDACA", "debugId": null}}, {"offset": {"line": 52, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 62, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/New%20folder%20%2825%29/streamzen/src/components/TrendingSection.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/TrendingSection.tsx <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/TrendingSection.tsx <module evaluation>\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAAsS,GACnU,oEACA", "debugId": null}}, {"offset": {"line": 76, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/New%20folder%20%2825%29/streamzen/src/components/TrendingSection.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/TrendingSection.tsx from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/TrendingSection.tsx\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAAkR,GAC/S,gDACA", "debugId": null}}, {"offset": {"line": 90, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 100, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/New%20folder%20%2825%29/streamzen/src/components/GenreSpotlight.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/GenreSpotlight.tsx <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/GenreSpotlight.tsx <module evaluation>\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAAqS,GAClU,mEACA", "debugId": null}}, {"offset": {"line": 114, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/New%20folder%20%2825%29/streamzen/src/components/GenreSpotlight.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/GenreSpotlight.tsx from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/GenreSpotlight.tsx\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAAiR,GAC9S,+CACA", "debugId": null}}, {"offset": {"line": 128, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 138, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/New%20folder%20%2825%29/streamzen/src/components/DecadeCollection.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/DecadeCollection.tsx <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/DecadeCollection.tsx <module evaluation>\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAAuS,GACpU,qEACA", "debugId": null}}, {"offset": {"line": 152, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/New%20folder%20%2825%29/streamzen/src/components/DecadeCollection.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/DecadeCollection.tsx from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/DecadeCollection.tsx\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAAmR,GAChT,iDACA", "debugId": null}}, {"offset": {"line": 166, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 176, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/New%20folder%20%2825%29/streamzen/src/components/ContentSection.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/ContentSection.tsx <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/ContentSection.tsx <module evaluation>\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAAqS,GAClU,mEACA", "debugId": null}}, {"offset": {"line": 190, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/New%20folder%20%2825%29/streamzen/src/components/ContentSection.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/ContentSection.tsx from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/ContentSection.tsx\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAAiR,GAC9S,+CACA", "debugId": null}}, {"offset": {"line": 204, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 214, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/New%20folder%20%2825%29/streamzen/src/lib/api.ts"], "sourcesContent": ["const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || (typeof window !== 'undefined' ? window.location.origin : 'http://localhost:3000');\n\nexport interface Movie {\n  _id: string;\n  imdbId: string;\n  tmdbId?: string;\n  title: string;\n  year: number;\n  rating?: string;\n  runtime?: string;\n  imdbRating?: number;\n  imdbVotes?: string;\n  popularity?: number;\n  popularityDelta?: number;\n  posterUrl?: string;\n  trailerUrl?: string;\n  trailerRuntime?: string;\n  trailerLikes?: string;\n  description?: string;\n  genres?: string[];\n  director?: string;\n  cast?: string[];\n  language?: string;\n  country?: string;\n  embedUrl: string;\n  embedUrlTmdb?: string;\n  quality?: string;\n  createdAt: string;\n  updatedAt: string;\n}\n\nexport interface Series {\n  _id: string;\n  imdbId: string;\n  tmdbId?: string;\n  title: string;\n  startYear: number;\n  endYear?: number;\n  rating?: string;\n  imdbRating?: number;\n  imdbVotes?: string;\n  popularity?: number;\n  popularityDelta?: number;\n  posterUrl?: string;\n  trailerUrl?: string;\n  description?: string;\n  genres?: string[];\n  creator?: string;\n  cast?: string[];\n  language?: string;\n  country?: string;\n  totalSeasons?: number;\n  status?: string;\n  embedUrl: string;\n  embedUrlTmdb?: string;\n  createdAt: string;\n  updatedAt: string;\n}\n\nexport interface Episode {\n  _id: string;\n  imdbId: string;\n  tmdbId?: string;\n  seriesTitle: string;\n  season: number;\n  episode: number;\n  episodeTitle?: string;\n  airDate?: string;\n  runtime?: string;\n  imdbRating?: number;\n  description?: string;\n  embedUrl: string;\n  embedUrlTmdb?: string;\n  quality?: string;\n  createdAt: string;\n  updatedAt: string;\n}\n\nexport interface PaginatedResponse<T> {\n  data: T[];\n  pagination: {\n    page: number;\n    limit: number;\n    total: number;\n    pages: number;\n  };\n}\n\nexport interface ContentFilters {\n  genre?: string;\n  year?: number;\n  language?: string;\n  country?: string;\n  rating?: string;\n  quality?: string;\n  sortBy?: 'title' | 'year' | 'imdbRating' | 'popularity' | 'createdAt';\n  sortOrder?: 'asc' | 'desc';\n  page?: number;\n  limit?: number;\n  search?: string;\n}\n\nexport interface GenreCount {\n  genre: string;\n  count: number;\n}\n\nexport interface LanguageCount {\n  language: string;\n  count: number;\n}\n\nexport interface CountryCount {\n  country: string;\n  count: number;\n}\n\nexport interface FilterOptions {\n  genres: GenreCount[];\n  languages: LanguageCount[];\n  countries: CountryCount[];\n  years: number[];\n  ratings: string[];\n  qualities: string[];\n}\n\nclass ApiClient {\n  private baseUrl: string;\n\n  constructor(baseUrl: string = API_BASE_URL) {\n    this.baseUrl = baseUrl;\n  }\n\n  private async request<T>(endpoint: string, options?: RequestInit): Promise<T> {\n    // For server-side rendering, use absolute URL\n    const baseUrl = this.baseUrl || (typeof window !== 'undefined' ? window.location.origin : 'http://localhost:3000');\n    const url = endpoint.startsWith('http') ? endpoint : `${baseUrl}${endpoint}`;\n\n    try {\n      const response = await fetch(url, {\n        headers: {\n          'Content-Type': 'application/json',\n          ...options?.headers,\n        },\n        ...options,\n      });\n\n      if (!response.ok) {\n        throw new Error(`HTTP error! status: ${response.status}`);\n      }\n\n      return await response.json();\n    } catch (error) {\n      console.error(`API request failed: ${url}`, error);\n      throw error;\n    }\n  }\n\n  // Movies\n  async getMovies(filters: ContentFilters = {}): Promise<PaginatedResponse<Movie>> {\n    const params = new URLSearchParams();\n    Object.entries(filters).forEach(([key, value]) => {\n      if (value !== undefined && value !== null) {\n        params.append(key, value.toString());\n      }\n    });\n    \n    return this.request<PaginatedResponse<Movie>>(`/api/movies?${params.toString()}`);\n  }\n\n  async getMovie(id: string): Promise<Movie> {\n    return this.request<Movie>(`/api/movies/${id}`);\n  }\n\n  // Series\n  async getSeries(filters: ContentFilters = {}): Promise<PaginatedResponse<Series>> {\n    const params = new URLSearchParams();\n    Object.entries(filters).forEach(([key, value]) => {\n      if (value !== undefined && value !== null) {\n        params.append(key, value.toString());\n      }\n    });\n    \n    return this.request<PaginatedResponse<Series>>(`/api/series?${params.toString()}`);\n  }\n\n  async getSeriesById(id: string): Promise<Series> {\n    return this.request<Series>(`/api/series/${id}`);\n  }\n\n  async getSeriesEpisodes(id: string, season?: number): Promise<Episode[]> {\n    const params = season ? `?season=${season}` : '';\n    return this.request<Episode[]>(`/api/series/${id}/episodes${params}`);\n  }\n\n  // Episodes\n  async getEpisodes(filters: ContentFilters = {}): Promise<PaginatedResponse<Episode>> {\n    const params = new URLSearchParams();\n    Object.entries(filters).forEach(([key, value]) => {\n      if (value !== undefined && value !== null) {\n        params.append(key, value.toString());\n      }\n    });\n    \n    return this.request<PaginatedResponse<Episode>>(`/api/episodes?${params.toString()}`);\n  }\n\n  // Requests\n  async createBulkRequest(imdbIds: string[], contentType: 'auto' | 'movie' | 'series' = 'auto'): Promise<{ requestId: string; status: string; totalCount: number; message: string }> {\n    return this.request('/api/requests', {\n      method: 'POST',\n      body: JSON.stringify({ imdbIds, contentType }),\n    });\n  }\n\n  async getRequestStatus(requestId: string): Promise<any> {\n    return this.request(`/api/requests/${requestId}`);\n  }\n\n  // Filter Options\n  async getMovieFilterOptions(): Promise<FilterOptions> {\n    return this.request<FilterOptions>('/api/movies/filters');\n  }\n\n  async getSeriesFilterOptions(): Promise<FilterOptions> {\n    return this.request<FilterOptions>('/api/series/filters');\n  }\n\n  async getEpisodeFilterOptions(): Promise<FilterOptions> {\n    return this.request<FilterOptions>('/api/episodes/filters');\n  }\n\n  // Search\n  async search(query: string, type: 'all' | 'movies' | 'series' | 'episodes' = 'all', page: number = 1, limit: number = 20): Promise<any> {\n    const params = new URLSearchParams({\n      q: query,\n      type,\n      page: page.toString(),\n      limit: limit.toString()\n    });\n    return this.request(`/api/search?${params.toString()}`);\n  }\n\n  async getSearchSuggestions(query: string, limit: number = 8): Promise<any> {\n    const params = new URLSearchParams({\n      q: query,\n      limit: limit.toString()\n    });\n    return this.request(`/api/search/suggestions?${params.toString()}`);\n  }\n\n  // Sync\n  async syncContent(): Promise<{ success: boolean; message: string; counts: { movies: number; series: number; episodes: number } }> {\n    return this.request('/api/sync', {\n      method: 'POST',\n    });\n  }\n}\n\nexport const apiClient = new ApiClient();\nexport default ApiClient;\n"], "names": [], "mappings": ";;;;AAAA,MAAM,eAAe,QAAQ,GAAG,CAAC,mBAAmB,IAAI,CAAC,6EAAyD,uBAAuB;AA8HzI,MAAM;IACI,QAAgB;IAExB,YAAY,UAAkB,YAAY,CAAE;QAC1C,IAAI,CAAC,OAAO,GAAG;IACjB;IAEA,MAAc,QAAW,QAAgB,EAAE,OAAqB,EAAc;QAC5E,8CAA8C;QAC9C,MAAM,UAAU,IAAI,CAAC,OAAO,IAAI,CAAC,6EAAyD,uBAAuB;QACjH,MAAM,MAAM,SAAS,UAAU,CAAC,UAAU,WAAW,GAAG,UAAU,UAAU;QAE5E,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,KAAK;gBAChC,SAAS;oBACP,gBAAgB;oBAChB,GAAG,SAAS,OAAO;gBACrB;gBACA,GAAG,OAAO;YACZ;YAEA,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,IAAI,MAAM,CAAC,oBAAoB,EAAE,SAAS,MAAM,EAAE;YAC1D;YAEA,OAAO,MAAM,SAAS,IAAI;QAC5B,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,CAAC,oBAAoB,EAAE,KAAK,EAAE;YAC5C,MAAM;QACR;IACF;IAEA,SAAS;IACT,MAAM,UAAU,UAA0B,CAAC,CAAC,EAAqC;QAC/E,MAAM,SAAS,IAAI;QACnB,OAAO,OAAO,CAAC,SAAS,OAAO,CAAC,CAAC,CAAC,KAAK,MAAM;YAC3C,IAAI,UAAU,aAAa,UAAU,MAAM;gBACzC,OAAO,MAAM,CAAC,KAAK,MAAM,QAAQ;YACnC;QACF;QAEA,OAAO,IAAI,CAAC,OAAO,CAA2B,CAAC,YAAY,EAAE,OAAO,QAAQ,IAAI;IAClF;IAEA,MAAM,SAAS,EAAU,EAAkB;QACzC,OAAO,IAAI,CAAC,OAAO,CAAQ,CAAC,YAAY,EAAE,IAAI;IAChD;IAEA,SAAS;IACT,MAAM,UAAU,UAA0B,CAAC,CAAC,EAAsC;QAChF,MAAM,SAAS,IAAI;QACnB,OAAO,OAAO,CAAC,SAAS,OAAO,CAAC,CAAC,CAAC,KAAK,MAAM;YAC3C,IAAI,UAAU,aAAa,UAAU,MAAM;gBACzC,OAAO,MAAM,CAAC,KAAK,MAAM,QAAQ;YACnC;QACF;QAEA,OAAO,IAAI,CAAC,OAAO,CAA4B,CAAC,YAAY,EAAE,OAAO,QAAQ,IAAI;IACnF;IAEA,MAAM,cAAc,EAAU,EAAmB;QAC/C,OAAO,IAAI,CAAC,OAAO,CAAS,CAAC,YAAY,EAAE,IAAI;IACjD;IAEA,MAAM,kBAAkB,EAAU,EAAE,MAAe,EAAsB;QACvE,MAAM,SAAS,SAAS,CAAC,QAAQ,EAAE,QAAQ,GAAG;QAC9C,OAAO,IAAI,CAAC,OAAO,CAAY,CAAC,YAAY,EAAE,GAAG,SAAS,EAAE,QAAQ;IACtE;IAEA,WAAW;IACX,MAAM,YAAY,UAA0B,CAAC,CAAC,EAAuC;QACnF,MAAM,SAAS,IAAI;QACnB,OAAO,OAAO,CAAC,SAAS,OAAO,CAAC,CAAC,CAAC,KAAK,MAAM;YAC3C,IAAI,UAAU,aAAa,UAAU,MAAM;gBACzC,OAAO,MAAM,CAAC,KAAK,MAAM,QAAQ;YACnC;QACF;QAEA,OAAO,IAAI,CAAC,OAAO,CAA6B,CAAC,cAAc,EAAE,OAAO,QAAQ,IAAI;IACtF;IAEA,WAAW;IACX,MAAM,kBAAkB,OAAiB,EAAE,cAA2C,MAAM,EAAuF;QACjL,OAAO,IAAI,CAAC,OAAO,CAAC,iBAAiB;YACnC,QAAQ;YACR,MAAM,KAAK,SAAS,CAAC;gBAAE;gBAAS;YAAY;QAC9C;IACF;IAEA,MAAM,iBAAiB,SAAiB,EAAgB;QACtD,OAAO,IAAI,CAAC,OAAO,CAAC,CAAC,cAAc,EAAE,WAAW;IAClD;IAEA,iBAAiB;IACjB,MAAM,wBAAgD;QACpD,OAAO,IAAI,CAAC,OAAO,CAAgB;IACrC;IAEA,MAAM,yBAAiD;QACrD,OAAO,IAAI,CAAC,OAAO,CAAgB;IACrC;IAEA,MAAM,0BAAkD;QACtD,OAAO,IAAI,CAAC,OAAO,CAAgB;IACrC;IAEA,SAAS;IACT,MAAM,OAAO,KAAa,EAAE,OAAiD,KAAK,EAAE,OAAe,CAAC,EAAE,QAAgB,EAAE,EAAgB;QACtI,MAAM,SAAS,IAAI,gBAAgB;YACjC,GAAG;YACH;YACA,MAAM,KAAK,QAAQ;YACnB,OAAO,MAAM,QAAQ;QACvB;QACA,OAAO,IAAI,CAAC,OAAO,CAAC,CAAC,YAAY,EAAE,OAAO,QAAQ,IAAI;IACxD;IAEA,MAAM,qBAAqB,KAAa,EAAE,QAAgB,CAAC,EAAgB;QACzE,MAAM,SAAS,IAAI,gBAAgB;YACjC,GAAG;YACH,OAAO,MAAM,QAAQ;QACvB;QACA,OAAO,IAAI,CAAC,OAAO,CAAC,CAAC,wBAAwB,EAAE,OAAO,QAAQ,IAAI;IACpE;IAEA,OAAO;IACP,MAAM,cAA4H;QAChI,OAAO,IAAI,CAAC,OAAO,CAAC,aAAa;YAC/B,QAAQ;QACV;IACF;AACF;AAEO,MAAM,YAAY,IAAI;uCACd", "debugId": null}}, {"offset": {"line": 340, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/New%20folder%20%2825%29/streamzen/src/components/ClientInitializeButton.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/ClientInitializeButton.tsx <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/ClientInitializeButton.tsx <module evaluation>\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAA6S,GAC1U,2EACA", "debugId": null}}, {"offset": {"line": 354, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/New%20folder%20%2825%29/streamzen/src/components/ClientInitializeButton.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/ClientInitializeButton.tsx from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/ClientInitializeButton.tsx\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAAyR,GACtT,uDACA", "debugId": null}}, {"offset": {"line": 368, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 378, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/New%20folder%20%2825%29/streamzen/src/app/page.tsx"], "sourcesContent": ["import { Suspense } from 'react';\nimport { Metadata } from 'next';\nimport EnhancedHeroSection from '@/components/EnhancedHeroSection';\nimport TrendingSection from '@/components/TrendingSection';\nimport GenreSpotlight from '@/components/GenreSpotlight';\nimport DecadeCollection from '@/components/DecadeCollection';\nimport DirectorSpotlight from '@/components/DirectorSpotlight';\nimport GlobalCinema from '@/components/GlobalCinema';\nimport ContentSection from '@/components/ContentSection';\nimport BingeWorthy from '@/components/BingeWorthy';\nimport HiddenGems from '@/components/HiddenGems';\nimport QuickPicks from '@/components/QuickPicks';\nimport CastSpotlight from '@/components/CastSpotlight';\nimport { apiClient } from '@/lib/api';\nimport InitializeButton from '@/components/ClientInitializeButton';\n// import { SchemaGenerator } from '@/lib/schema';\n\nasync function getEnhancedHomePageData() {\n  try {\n    const currentYear = new Date().getFullYear();\n\n    // Fetch diverse content for different sections\n    const [\n      // Hero content - top rated recent content\n      heroMoviesResponse,\n      heroSeriesResponse,\n\n      // Trending content - high popularity with positive delta\n      trendingMoviesResponse,\n      trendingSeriesResponse,\n\n      // Recently added content\n      recentMoviesResponse,\n      recentSeriesResponse,\n      recentEpisodesResponse,\n\n      // Top rated content by different criteria\n      topRatedMoviesResponse,\n      topRatedSeriesResponse,\n\n      // Genre-specific content (we'll fetch popular genres)\n      actionMoviesResponse,\n      dramaSeriesResponse,\n      comedyMoviesResponse,\n\n      // Decade collections\n      modernMoviesResponse, // 2020s\n      classicMoviesResponse, // 2000s-2010s\n\n      // Global content\n      internationalMoviesResponse,\n      internationalSeriesResponse,\n\n      // Binge-worthy series (ongoing with high ratings)\n      ongoingSeriesResponse,\n\n      // Quick picks (shorter content)\n      shortMoviesResponse\n    ] = await Promise.all([\n      // Hero content\n      apiClient.getMovies({ limit: 8, sortBy: 'imdbRating', sortOrder: 'desc', year: currentYear - 1 }),\n      apiClient.getSeries({ limit: 8, sortBy: 'imdbRating', sortOrder: 'desc' }),\n\n      // Trending (we'll simulate trending by using popularity)\n      apiClient.getMovies({ limit: 12, sortBy: 'popularity', sortOrder: 'desc' }),\n      apiClient.getSeries({ limit: 12, sortBy: 'popularity', sortOrder: 'desc' }),\n\n      // Recently added\n      apiClient.getMovies({ limit: 15, sortBy: 'createdAt', sortOrder: 'desc' }),\n      apiClient.getSeries({ limit: 15, sortBy: 'createdAt', sortOrder: 'desc' }),\n      apiClient.getEpisodes({ limit: 15, sortBy: 'createdAt', sortOrder: 'desc' }),\n\n      // Top rated\n      apiClient.getMovies({ limit: 20, sortBy: 'imdbRating', sortOrder: 'desc' }),\n      apiClient.getSeries({ limit: 20, sortBy: 'imdbRating', sortOrder: 'desc' }),\n\n      // Genre content\n      apiClient.getMovies({ limit: 12, genre: 'Action', sortBy: 'imdbRating', sortOrder: 'desc' }),\n      apiClient.getSeries({ limit: 12, genre: 'Drama', sortBy: 'imdbRating', sortOrder: 'desc' }),\n      apiClient.getMovies({ limit: 12, genre: 'Comedy', sortBy: 'imdbRating', sortOrder: 'desc' }),\n\n      // Decade collections\n      apiClient.getMovies({ limit: 15, year: currentYear - 4, sortBy: 'imdbRating', sortOrder: 'desc' }), // Recent years\n      apiClient.getMovies({ limit: 15, year: 2010, sortBy: 'imdbRating', sortOrder: 'desc' }), // 2010s\n\n      // International content\n      apiClient.getMovies({ limit: 12, language: 'English', sortBy: 'imdbRating', sortOrder: 'desc' }),\n      apiClient.getSeries({ limit: 12, language: 'English', sortBy: 'imdbRating', sortOrder: 'desc' }),\n\n      // Ongoing series\n      apiClient.getSeries({ limit: 12, sortBy: 'imdbRating', sortOrder: 'desc' }),\n\n      // Short content (we'll filter by runtime later)\n      apiClient.getMovies({ limit: 20, sortBy: 'imdbRating', sortOrder: 'desc' })\n    ]);\n\n    return {\n      hero: {\n        movies: heroMoviesResponse.data,\n        series: heroSeriesResponse.data\n      },\n      trending: {\n        movies: trendingMoviesResponse.data,\n        series: trendingSeriesResponse.data\n      },\n      recent: {\n        movies: recentMoviesResponse.data,\n        series: recentSeriesResponse.data,\n        episodes: recentEpisodesResponse.data\n      },\n      topRated: {\n        movies: topRatedMoviesResponse.data,\n        series: topRatedSeriesResponse.data\n      },\n      genres: {\n        action: actionMoviesResponse.data,\n        drama: dramaSeriesResponse.data,\n        comedy: comedyMoviesResponse.data\n      },\n      decades: {\n        modern: modernMoviesResponse.data,\n        classic: classicMoviesResponse.data\n      },\n      international: {\n        movies: internationalMoviesResponse.data,\n        series: internationalSeriesResponse.data\n      },\n      bingeWorthy: ongoingSeriesResponse.data,\n      quickPicks: shortMoviesResponse.data.filter(movie =>\n        movie.runtime && parseInt(movie.runtime.replace(/\\D/g, '')) < 120\n      )\n    };\n  } catch (error) {\n    console.error('Error fetching enhanced homepage data:', error);\n    return {\n      hero: { movies: [], series: [] },\n      trending: { movies: [], series: [] },\n      recent: { movies: [], series: [], episodes: [] },\n      topRated: { movies: [], series: [] },\n      genres: { action: [], drama: [], comedy: [] },\n      decades: { modern: [], classic: [] },\n      international: { movies: [], series: [] },\n      bingeWorthy: [],\n      quickPicks: []\n    };\n  }\n}\n\nfunction transformMovieToHeroItem(movie: any) {\n  return {\n    id: movie._id,\n    imdbId: movie.imdbId,\n    title: movie.title,\n    year: movie.year,\n    posterUrl: movie.posterUrl,\n    imdbRating: movie.imdbRating,\n    description: movie.description,\n    type: 'movie' as const\n  };\n}\n\nfunction transformSeriesToHeroItem(series: any) {\n  return {\n    id: series._id,\n    imdbId: series.imdbId,\n    title: series.title,\n    year: series.startYear,\n    posterUrl: series.posterUrl,\n    imdbRating: series.imdbRating,\n    description: series.description,\n    type: 'series' as const\n  };\n}\n\nfunction transformMovieToContentItem(movie: any) {\n  return {\n    id: movie._id,\n    imdbId: movie.imdbId,\n    title: movie.title,\n    year: movie.year,\n    posterUrl: movie.posterUrl,\n    imdbRating: movie.imdbRating,\n    description: movie.description,\n    type: 'movie' as const\n  };\n}\n\nfunction transformSeriesToContentItem(series: any) {\n  return {\n    id: series._id,\n    imdbId: series.imdbId,\n    title: series.title,\n    year: series.startYear,\n    posterUrl: series.posterUrl,\n    imdbRating: series.imdbRating,\n    description: series.description,\n    type: 'series' as const\n  };\n}\n\nfunction transformEpisodeToContentItem(episode: any) {\n  return {\n    id: episode._id,\n    imdbId: episode.imdbId,\n    title: episode.episodeTitle || `Episode ${episode.episode}`,\n    season: episode.season,\n    episode: episode.episode,\n    seriesTitle: episode.seriesTitle,\n    posterUrl: episode.posterUrl,\n    seriesPosterUrl: episode.seriesPosterUrl, // Use series poster for episodes\n    imdbRating: episode.imdbRating,\n    description: episode.description,\n    type: 'episode' as const\n  };\n}\n\nexport default async function Home() {\n  const data = await getEnhancedHomePageData();\n\n  // Check if we have any content at all\n  const hasContent = data.hero.movies.length > 0 || data.hero.series.length > 0 ||\n                    data.recent.movies.length > 0 || data.recent.series.length > 0;\n\n  // If no data, show initialization prompt\n  if (!hasContent) {\n    return (\n      <div className=\"min-h-screen bg-black flex items-center justify-center\">\n        <div className=\"text-center\">\n          <h1 className=\"text-4xl font-bold text-white mb-4\">Welcome to freeMoviesWatchNow</h1>\n          <p className=\"text-gray-400 mb-8\">Initialize the database to start streaming</p>\n          <InitializeButton />\n        </div>\n      </div>\n    );\n  }\n\n  // Create hero items from top-rated movies and series\n  const heroItems = [\n    ...data.hero.movies.slice(0, 4).map(transformMovieToHeroItem),\n    ...data.hero.series.slice(0, 4).map(transformSeriesToHeroItem)\n  ];\n\n  return (\n    <div className=\"min-h-screen bg-black overflow-hidden\">\n      {/* Structured Data for SEO - Temporarily commented out */}\n      {/* <script\n        type=\"application/ld+json\"\n        dangerouslySetInnerHTML={{\n          __html: JSON.stringify([\n            SchemaGenerator.generateWebsiteSchema(),\n            SchemaGenerator.generateCollectionPageSchema(\n              'Latest Movies and TV Series',\n              'Watch the latest movies and TV series online free in HD quality. Discover trending content updated daily.',\n              '/',\n              [\n                ...data.hero.movies.slice(0, 5).map(movie => ({\n                  name: `${movie.title} (${movie.year})`,\n                  url: `/watch/movie/${movie.imdbId}`,\n                  image: movie.posterUrl\n                })),\n                ...data.hero.series.slice(0, 5).map(show => ({\n                  name: `${show.title} (${show.startYear})`,\n                  url: `/watch/series/${show.imdbId}`,\n                  image: show.posterUrl\n                }))\n              ]\n            )\n          ])\n        }}\n      /> */}\n\n      {/* Enhanced Interactive Hero Section */}\n      <EnhancedHeroSection items={heroItems} />\n\n      {/* Dynamic Content Sections with Modern Design */}\n      <div className=\"relative\">\n        {/* Advanced Background Effects */}\n        <div className=\"absolute inset-0 overflow-hidden pointer-events-none\">\n          <div className=\"absolute top-0 left-0 w-full h-full bg-gradient-to-b from-black via-gray-900/50 to-black\" />\n          <div className=\"absolute top-1/4 left-1/4 w-96 h-96 bg-blue-600/5 rounded-full blur-3xl animate-pulse\" />\n          <div className=\"absolute top-1/2 right-1/4 w-80 h-80 bg-purple-600/5 rounded-full blur-3xl animate-pulse\" style={{ animationDelay: '2s' }} />\n          <div className=\"absolute bottom-1/4 left-1/3 w-72 h-72 bg-red-600/5 rounded-full blur-3xl animate-pulse\" style={{ animationDelay: '4s' }} />\n        </div>\n\n        <div className=\"relative space-y-24 py-20 pb-32\">\n          {/* Trending Now Section */}\n          <TrendingSection\n            movies={data.trending.movies}\n            series={data.trending.series}\n            className=\"animate-fade-in\"\n          />\n\n          {/* Genre Spotlights */}\n          <GenreSpotlight\n            actionMovies={data.genres.action}\n            dramaSeries={data.genres.drama}\n            comedyMovies={data.genres.comedy}\n            className=\"animate-fade-in\"\n            style={{ animationDelay: '0.2s' }}\n          />\n\n          {/* Decade Collections */}\n          <DecadeCollection\n            modernContent={data.decades.modern}\n            classicContent={data.decades.classic}\n            className=\"animate-fade-in\"\n            style={{ animationDelay: '0.4s' }}\n          />\n\n          {/* Binge-Worthy Series */}\n          <BingeWorthy\n            series={data.bingeWorthy}\n            className=\"animate-fade-in\"\n            style={{ animationDelay: '0.6s' }}\n          />\n\n          {/* Global Cinema */}\n          <GlobalCinema\n            movies={data.international.movies}\n            series={data.international.series}\n            className=\"animate-fade-in\"\n            style={{ animationDelay: '0.8s' }}\n          />\n\n          {/* Hidden Gems */}\n          <HiddenGems\n            movies={data.topRated.movies.filter(m => m.imdbRating && m.imdbRating >= 8.0)}\n            series={data.topRated.series.filter(s => s.imdbRating && s.imdbRating >= 8.0)}\n            className=\"animate-fade-in\"\n            style={{ animationDelay: '1.0s' }}\n          />\n\n          {/* Quick Picks */}\n          <QuickPicks\n            movies={data.quickPicks}\n            className=\"animate-fade-in\"\n            style={{ animationDelay: '1.2s' }}\n          />\n\n          {/* Recently Added */}\n          <ContentSection\n            title=\"🆕 Recently Added\"\n            items={[\n              ...data.recent.movies.slice(0, 8).map(transformMovieToContentItem),\n              ...data.recent.series.slice(0, 8).map(transformSeriesToContentItem)\n            ]}\n            viewAllHref=\"/movies\"\n            className=\"animate-fade-in\"\n            style={{ animationDelay: '1.4s' }}\n          />\n\n          {/* Latest Episodes */}\n          <ContentSection\n            title=\"📺 Latest Episodes\"\n            items={data.recent.episodes.map(transformEpisodeToContentItem)}\n            viewAllHref=\"/episodes\"\n            className=\"animate-fade-in\"\n            style={{ animationDelay: '1.6s' }}\n          />\n        </div>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;;;;;;AAGA;;;;;;;;;;;;;;;;AAKA;AACA;;;;;;;;;;;;;AACA,kDAAkD;AAElD,eAAe;IACb,IAAI;QACF,MAAM,cAAc,IAAI,OAAO,WAAW;QAE1C,+CAA+C;QAC/C,MAAM,CACJ,0CAA0C;QAC1C,oBACA,oBAEA,yDAAyD;QACzD,wBACA,wBAEA,yBAAyB;QACzB,sBACA,sBACA,wBAEA,0CAA0C;QAC1C,wBACA,wBAEA,sDAAsD;QACtD,sBACA,qBACA,sBAEA,qBAAqB;QACrB,sBACA,uBAEA,iBAAiB;QACjB,6BACA,6BAEA,kDAAkD;QAClD,uBAEA,gCAAgC;QAChC,oBACD,GAAG,MAAM,QAAQ,GAAG,CAAC;YACpB,eAAe;YACf,iHAAA,CAAA,YAAS,CAAC,SAAS,CAAC;gBAAE,OAAO;gBAAG,QAAQ;gBAAc,WAAW;gBAAQ,MAAM,cAAc;YAAE;YAC/F,iHAAA,CAAA,YAAS,CAAC,SAAS,CAAC;gBAAE,OAAO;gBAAG,QAAQ;gBAAc,WAAW;YAAO;YAExE,yDAAyD;YACzD,iHAAA,CAAA,YAAS,CAAC,SAAS,CAAC;gBAAE,OAAO;gBAAI,QAAQ;gBAAc,WAAW;YAAO;YACzE,iHAAA,CAAA,YAAS,CAAC,SAAS,CAAC;gBAAE,OAAO;gBAAI,QAAQ;gBAAc,WAAW;YAAO;YAEzE,iBAAiB;YACjB,iHAAA,CAAA,YAAS,CAAC,SAAS,CAAC;gBAAE,OAAO;gBAAI,QAAQ;gBAAa,WAAW;YAAO;YACxE,iHAAA,CAAA,YAAS,CAAC,SAAS,CAAC;gBAAE,OAAO;gBAAI,QAAQ;gBAAa,WAAW;YAAO;YACxE,iHAAA,CAAA,YAAS,CAAC,WAAW,CAAC;gBAAE,OAAO;gBAAI,QAAQ;gBAAa,WAAW;YAAO;YAE1E,YAAY;YACZ,iHAAA,CAAA,YAAS,CAAC,SAAS,CAAC;gBAAE,OAAO;gBAAI,QAAQ;gBAAc,WAAW;YAAO;YACzE,iHAAA,CAAA,YAAS,CAAC,SAAS,CAAC;gBAAE,OAAO;gBAAI,QAAQ;gBAAc,WAAW;YAAO;YAEzE,gBAAgB;YAChB,iHAAA,CAAA,YAAS,CAAC,SAAS,CAAC;gBAAE,OAAO;gBAAI,OAAO;gBAAU,QAAQ;gBAAc,WAAW;YAAO;YAC1F,iHAAA,CAAA,YAAS,CAAC,SAAS,CAAC;gBAAE,OAAO;gBAAI,OAAO;gBAAS,QAAQ;gBAAc,WAAW;YAAO;YACzF,iHAAA,CAAA,YAAS,CAAC,SAAS,CAAC;gBAAE,OAAO;gBAAI,OAAO;gBAAU,QAAQ;gBAAc,WAAW;YAAO;YAE1F,qBAAqB;YACrB,iHAAA,CAAA,YAAS,CAAC,SAAS,CAAC;gBAAE,OAAO;gBAAI,MAAM,cAAc;gBAAG,QAAQ;gBAAc,WAAW;YAAO;YAChG,iHAAA,CAAA,YAAS,CAAC,SAAS,CAAC;gBAAE,OAAO;gBAAI,MAAM;gBAAM,QAAQ;gBAAc,WAAW;YAAO;YAErF,wBAAwB;YACxB,iHAAA,CAAA,YAAS,CAAC,SAAS,CAAC;gBAAE,OAAO;gBAAI,UAAU;gBAAW,QAAQ;gBAAc,WAAW;YAAO;YAC9F,iHAAA,CAAA,YAAS,CAAC,SAAS,CAAC;gBAAE,OAAO;gBAAI,UAAU;gBAAW,QAAQ;gBAAc,WAAW;YAAO;YAE9F,iBAAiB;YACjB,iHAAA,CAAA,YAAS,CAAC,SAAS,CAAC;gBAAE,OAAO;gBAAI,QAAQ;gBAAc,WAAW;YAAO;YAEzE,gDAAgD;YAChD,iHAAA,CAAA,YAAS,CAAC,SAAS,CAAC;gBAAE,OAAO;gBAAI,QAAQ;gBAAc,WAAW;YAAO;SAC1E;QAED,OAAO;YACL,MAAM;gBACJ,QAAQ,mBAAmB,IAAI;gBAC/B,QAAQ,mBAAmB,IAAI;YACjC;YACA,UAAU;gBACR,QAAQ,uBAAuB,IAAI;gBACnC,QAAQ,uBAAuB,IAAI;YACrC;YACA,QAAQ;gBACN,QAAQ,qBAAqB,IAAI;gBACjC,QAAQ,qBAAqB,IAAI;gBACjC,UAAU,uBAAuB,IAAI;YACvC;YACA,UAAU;gBACR,QAAQ,uBAAuB,IAAI;gBACnC,QAAQ,uBAAuB,IAAI;YACrC;YACA,QAAQ;gBACN,QAAQ,qBAAqB,IAAI;gBACjC,OAAO,oBAAoB,IAAI;gBAC/B,QAAQ,qBAAqB,IAAI;YACnC;YACA,SAAS;gBACP,QAAQ,qBAAqB,IAAI;gBACjC,SAAS,sBAAsB,IAAI;YACrC;YACA,eAAe;gBACb,QAAQ,4BAA4B,IAAI;gBACxC,QAAQ,4BAA4B,IAAI;YAC1C;YACA,aAAa,sBAAsB,IAAI;YACvC,YAAY,oBAAoB,IAAI,CAAC,MAAM,CAAC,CAAA,QAC1C,MAAM,OAAO,IAAI,SAAS,MAAM,OAAO,CAAC,OAAO,CAAC,OAAO,OAAO;QAElE;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,0CAA0C;QACxD,OAAO;YACL,MAAM;gBAAE,QAAQ,EAAE;gBAAE,QAAQ,EAAE;YAAC;YAC/B,UAAU;gBAAE,QAAQ,EAAE;gBAAE,QAAQ,EAAE;YAAC;YACnC,QAAQ;gBAAE,QAAQ,EAAE;gBAAE,QAAQ,EAAE;gBAAE,UAAU,EAAE;YAAC;YAC/C,UAAU;gBAAE,QAAQ,EAAE;gBAAE,QAAQ,EAAE;YAAC;YACnC,QAAQ;gBAAE,QAAQ,EAAE;gBAAE,OAAO,EAAE;gBAAE,QAAQ,EAAE;YAAC;YAC5C,SAAS;gBAAE,QAAQ,EAAE;gBAAE,SAAS,EAAE;YAAC;YACnC,eAAe;gBAAE,QAAQ,EAAE;gBAAE,QAAQ,EAAE;YAAC;YACxC,aAAa,EAAE;YACf,YAAY,EAAE;QAChB;IACF;AACF;AAEA,SAAS,yBAAyB,KAAU;IAC1C,OAAO;QACL,IAAI,MAAM,GAAG;QACb,QAAQ,MAAM,MAAM;QACpB,OAAO,MAAM,KAAK;QAClB,MAAM,MAAM,IAAI;QAChB,WAAW,MAAM,SAAS;QAC1B,YAAY,MAAM,UAAU;QAC5B,aAAa,MAAM,WAAW;QAC9B,MAAM;IACR;AACF;AAEA,SAAS,0BAA0B,MAAW;IAC5C,OAAO;QACL,IAAI,OAAO,GAAG;QACd,QAAQ,OAAO,MAAM;QACrB,OAAO,OAAO,KAAK;QACnB,MAAM,OAAO,SAAS;QACtB,WAAW,OAAO,SAAS;QAC3B,YAAY,OAAO,UAAU;QAC7B,aAAa,OAAO,WAAW;QAC/B,MAAM;IACR;AACF;AAEA,SAAS,4BAA4B,KAAU;IAC7C,OAAO;QACL,IAAI,MAAM,GAAG;QACb,QAAQ,MAAM,MAAM;QACpB,OAAO,MAAM,KAAK;QAClB,MAAM,MAAM,IAAI;QAChB,WAAW,MAAM,SAAS;QAC1B,YAAY,MAAM,UAAU;QAC5B,aAAa,MAAM,WAAW;QAC9B,MAAM;IACR;AACF;AAEA,SAAS,6BAA6B,MAAW;IAC/C,OAAO;QACL,IAAI,OAAO,GAAG;QACd,QAAQ,OAAO,MAAM;QACrB,OAAO,OAAO,KAAK;QACnB,MAAM,OAAO,SAAS;QACtB,WAAW,OAAO,SAAS;QAC3B,YAAY,OAAO,UAAU;QAC7B,aAAa,OAAO,WAAW;QAC/B,MAAM;IACR;AACF;AAEA,SAAS,8BAA8B,OAAY;IACjD,OAAO;QACL,IAAI,QAAQ,GAAG;QACf,QAAQ,QAAQ,MAAM;QACtB,OAAO,QAAQ,YAAY,IAAI,CAAC,QAAQ,EAAE,QAAQ,OAAO,EAAE;QAC3D,QAAQ,QAAQ,MAAM;QACtB,SAAS,QAAQ,OAAO;QACxB,aAAa,QAAQ,WAAW;QAChC,WAAW,QAAQ,SAAS;QAC5B,iBAAiB,QAAQ,eAAe;QACxC,YAAY,QAAQ,UAAU;QAC9B,aAAa,QAAQ,WAAW;QAChC,MAAM;IACR;AACF;AAEe,eAAe;IAC5B,MAAM,OAAO,MAAM;IAEnB,sCAAsC;IACtC,MAAM,aAAa,KAAK,IAAI,CAAC,MAAM,CAAC,MAAM,GAAG,KAAK,KAAK,IAAI,CAAC,MAAM,CAAC,MAAM,GAAG,KAC1D,KAAK,MAAM,CAAC,MAAM,CAAC,MAAM,GAAG,KAAK,KAAK,MAAM,CAAC,MAAM,CAAC,MAAM,GAAG;IAE/E,yCAAyC;IACzC,IAAI,CAAC,YAAY;QACf,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;kCAAqC;;;;;;kCACnD,8OAAC;wBAAE,WAAU;kCAAqB;;;;;;kCAClC,8OAAC,4IAAA,CAAA,UAAgB;;;;;;;;;;;;;;;;IAIzB;IAEA,qDAAqD;IACrD,MAAM,YAAY;WACb,KAAK,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC;WACjC,KAAK,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC;KACrC;IAED,qBACE,8OAAC;QAAI,WAAU;;0BA6Bb,8OAAC,yIAAA,CAAA,UAAmB;gBAAC,OAAO;;;;;;0BAG5B,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;;;;;0CACf,8OAAC;gCAAI,WAAU;;;;;;0CACf,8OAAC;gCAAI,WAAU;gCAA2F,OAAO;oCAAE,gBAAgB;gCAAK;;;;;;0CACxI,8OAAC;gCAAI,WAAU;gCAA0F,OAAO;oCAAE,gBAAgB;gCAAK;;;;;;;;;;;;kCAGzI,8OAAC;wBAAI,WAAU;;0CAEb,8OAAC,qIAAA,CAAA,UAAe;gCACd,QAAQ,KAAK,QAAQ,CAAC,MAAM;gCAC5B,QAAQ,KAAK,QAAQ,CAAC,MAAM;gCAC5B,WAAU;;;;;;0CAIZ,8OAAC,oIAAA,CAAA,UAAc;gCACb,cAAc,KAAK,MAAM,CAAC,MAAM;gCAChC,aAAa,KAAK,MAAM,CAAC,KAAK;gCAC9B,cAAc,KAAK,MAAM,CAAC,MAAM;gCAChC,WAAU;gCACV,OAAO;oCAAE,gBAAgB;gCAAO;;;;;;0CAIlC,8OAAC,sIAAA,CAAA,UAAgB;gCACf,eAAe,KAAK,OAAO,CAAC,MAAM;gCAClC,gBAAgB,KAAK,OAAO,CAAC,OAAO;gCACpC,WAAU;gCACV,OAAO;oCAAE,gBAAgB;gCAAO;;;;;;0CAIlC,8OAAC;gCACC,QAAQ,KAAK,WAAW;gCACxB,WAAU;gCACV,OAAO;oCAAE,gBAAgB;gCAAO;;;;;;0CAIlC,8OAAC;gCACC,QAAQ,KAAK,aAAa,CAAC,MAAM;gCACjC,QAAQ,KAAK,aAAa,CAAC,MAAM;gCACjC,WAAU;gCACV,OAAO;oCAAE,gBAAgB;gCAAO;;;;;;0CAIlC,8OAAC;gCACC,QAAQ,KAAK,QAAQ,CAAC,MAAM,CAAC,MAAM,CAAC,CAAA,IAAK,EAAE,UAAU,IAAI,EAAE,UAAU,IAAI;gCACzE,QAAQ,KAAK,QAAQ,CAAC,MAAM,CAAC,MAAM,CAAC,CAAA,IAAK,EAAE,UAAU,IAAI,EAAE,UAAU,IAAI;gCACzE,WAAU;gCACV,OAAO;oCAAE,gBAAgB;gCAAO;;;;;;0CAIlC,8OAAC;gCACC,QAAQ,KAAK,UAAU;gCACvB,WAAU;gCACV,OAAO;oCAAE,gBAAgB;gCAAO;;;;;;0CAIlC,8OAAC,oIAAA,CAAA,UAAc;gCACb,OAAM;gCACN,OAAO;uCACF,KAAK,MAAM,CAAC,MAAM,CAAC,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC;uCACnC,KAAK,MAAM,CAAC,MAAM,CAAC,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC;iCACvC;gCACD,aAAY;gCACZ,WAAU;gCACV,OAAO;oCAAE,gBAAgB;gCAAO;;;;;;0CAIlC,8OAAC,oIAAA,CAAA,UAAc;gCACb,OAAM;gCACN,OAAO,KAAK,MAAM,CAAC,QAAQ,CAAC,GAAG,CAAC;gCAChC,aAAY;gCACZ,WAAU;gCACV,OAAO;oCAAE,gBAAgB;gCAAO;;;;;;;;;;;;;;;;;;;;;;;;AAM5C", "debugId": null}}, {"offset": {"line": 949, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/New%20folder%20%2825%29/streamzen/node_modules/next/dist/src/server/route-modules/app-page/module.compiled.js"], "sourcesContent": ["if (process.env.NEXT_RUNTIME === 'edge') {\n  module.exports = require('next/dist/server/route-modules/app-page/module.js')\n} else {\n  if (process.env.__NEXT_EXPERIMENTAL_REACT) {\n    if (process.env.NODE_ENV === 'development') {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo-experimental.runtime.dev.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page-experimental.runtime.dev.js')\n      }\n    } else {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo-experimental.runtime.prod.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page-experimental.runtime.prod.js')\n      }\n    }\n  } else {\n    if (process.env.NODE_ENV === 'development') {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo.runtime.dev.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page.runtime.dev.js')\n      }\n    } else {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo.runtime.prod.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page.runtime.prod.js')\n      }\n    }\n  }\n}\n"], "names": ["process", "env", "NEXT_RUNTIME", "module", "exports", "require", "__NEXT_EXPERIMENTAL_REACT", "NODE_ENV", "TURBOPACK"], "mappings": "AAAA,IAAIA,QAAQC,GAAG,CAACC,YAAY,KAAK,MAAQ;;AAEzC,OAAO;IACL,IAAIF,QAAQC,GAAG,CAACK,uBAA2B,EAAF;;IAczC,OAAO;QACL,IAAIN,QAAQC,GAAG,CAACM,QAAQ,KAAK,WAAe;YAC1C,IAAIP,QAAQC,GAAG,CAACO,SAAS,eAAE;gBACzBL,OAAOC,OAAO,GAAGC,QAAQ;YAC3B,OAAO;;YAEP;QACF,OAAO;;QAMP;IACF;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 987, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/New%20folder%20%2825%29/streamzen/node_modules/next/dist/src/build/templates/app-page.ts"], "sourcesContent": ["import type { LoaderTree } from '../../server/lib/app-dir-module'\nimport { AppPageRouteModule } from '../../server/route-modules/app-page/module.compiled' with { 'turbopack-transition': 'next-ssr' }\nimport { RouteKind } from '../../server/route-kind' with { 'turbopack-transition': 'next-server-utility' }\n\n// These are injected by the loader afterwards.\n\n/**\n * The tree created in next-app-loader that holds component segments and modules\n * and I've updated it.\n */\ndeclare const tree: LoaderTree\ndeclare const pages: any\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\n// INJECT:tree\n// INJECT:pages\n\nexport { tree, pages }\n\nexport { default as GlobalError } from 'VAR_MODULE_GLOBAL_ERROR' with { 'turbopack-transition': 'next-server-utility' }\n\n// These are injected by the loader afterwards.\ndeclare const __next_app_require__: (id: string | number) => unknown\ndeclare const __next_app_load_chunk__: (id: string | number) => Promise<unknown>\n\n// INJECT:__next_app_require__\n// INJECT:__next_app_load_chunk__\n\nexport const __next_app__ = {\n  require: __next_app_require__,\n  loadChunk: __next_app_load_chunk__,\n}\n\nexport * from '../../server/app-render/entry-base' with { 'turbopack-transition': 'next-server-utility' }\n\n// Create and export the route module that will be consumed.\nexport const routeModule = new AppPageRouteModule({\n  definition: {\n    kind: RouteKind.APP_PAGE,\n    page: 'VAR_DEFINITION_PAGE',\n    pathname: 'VAR_DEFINITION_PATHNAME',\n    // The following aren't used in production.\n    bundlePath: '',\n    filename: '',\n    appPaths: [],\n  },\n  userland: {\n    loaderTree: tree,\n  },\n})\n"], "names": ["AppPageRouteModule", "RouteKind", "tree", "pages", "default", "GlobalError", "__next_app__", "require", "__next_app_require__", "loadChunk", "__next_app_load_chunk__", "routeModule", "definition", "kind", "APP_PAGE", "page", "pathname", "bundlePath", "filename", "appPaths", "userland", "loaderTree"], "mappings": ";;;;;;AACA,SAASA,kBAAkB,QAAQ,2DAA2D;IAAE,wBAAwB;AAAW,EAAC;IACzE,wBAAwB;AAWnF,yEAAyE;AAEzE,cAAc;AAGd,SAASE,IAAI,EAAEC,KAAK,GAAE;IAEkD,wBAAwB;AAOhG,iCAAiC;;;;;;;;;;;;IAI/BM,WAAWC,0DAAAA;AACb,EAAC,QAAA;AAED,MAAA,OAAA;IAAc;IAAA,sCAA0C;YAAE,QAAA;YAAA;YAAA,CAA8C,EAAtB,AAAuB;YAAA;gBAEzG,UAAA,CAAA;gBAAA,QAAA;oBAAA,IAAA,0BAA4D;oBAAA;iBAAA;YAC5D;SAAA,KAAO,MAAMC,cAAc,IAAIX,mBAAmB;;KAChDY,YAAY;cACVC,IAAAA,EAAMZ;YAAAA,MAAAA,CAAUa,QAAQ;iBACxBC,MAAM,QAAA;wBAAA;4BACNC,KAAAA,CAAAA,GAAAA,4MAAAA,CAAAA,KAAU,iBAAA,EAAA,MAAA,MAAA,MAAA,MAAA,EAAA,iBAAA,CAAA,CAAA,EAAA,6SAAA,CAAA,UAAA,CAAA,GAAA,CAAA,KAAA,CAAA,KAAA,MAAA,CAAA,CAAA,EAAA,CAAA,EAAA,EAAA;4BACV,OAAA,GAAA,6SAAA,CAAA,UAAA,CAAA,KAAA,CAA2C,CAAA,EAAA,6SAAA,CAAA,UAAA,CAAA,MAAA,EAAA;4BAC3CC,MAAAA,CAAAA,KAAY,OAAA,CAAA;;qBACZC,UAAU;gBACVC,UAAU,EAAE;UACd;QAAA,UAAA;YAAA,IAAA;YAAA;SAAA;UACAC,UAAU,CAAA;YAAA,IAAA;YAAA;SAAA;cACRC,OAAAA;YAAAA,IAAYnB;YAAAA;SAAAA;UACd,cAAA;YAAA,IAAA;YAAA;SAAA;IACF;CAAA,CAAE", "ignoreList": [0], "debugId": null}}]}