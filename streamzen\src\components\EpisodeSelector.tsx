'use client';

import { useState, useEffect } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import { Episode } from '@/types';

interface EpisodeSelectorProps {
  seriesId: string;
  episodes: Episode[];
  currentSeason: number;
  currentEpisode: number;
}

export default function EpisodeSelector({
  seriesId,
  episodes,
  currentSeason,
  currentEpisode
}: EpisodeSelectorProps) {
  const router = useRouter();
  const searchParams = useSearchParams();
  const [selectedSeason, setSelectedSeason] = useState(currentSeason);

  // Group episodes by season
  const episodesBySeason = episodes.reduce((acc, episode) => {
    if (!acc[episode.season]) {
      acc[episode.season] = [];
    }
    acc[episode.season].push(episode);
    return acc;
  }, {} as Record<number, Episode[]>);

  // Get available seasons
  const availableSeasons = Object.keys(episodesBySeason)
    .map(Number)
    .sort((a, b) => a - b);

  // Get episodes for selected season
  const currentSeasonEpisodes = episodesBySeason[selectedSeason] || [];

  const handleSeasonChange = (season: number) => {
    setSelectedSeason(season);
    // Navigate to first episode of the selected season
    const firstEpisode = episodesBySeason[season]?.[0];
    if (firstEpisode) {
      router.push(`/watch/series/${seriesId}?season=${season}&episode=${firstEpisode.episode}`);
    }
  };

  const handleEpisodeChange = (episode: number) => {
    router.push(`/watch/series/${seriesId}?season=${selectedSeason}&episode=${episode}`);
  };

  return (
    <div className="glass-elevated p-6 rounded-2xl border border-gray-700/50">
      <h3 className="text-xl font-bold text-white mb-4">Episodes</h3>
      
      {/* Season Selector */}
      {availableSeasons.length > 1 && (
        <div className="mb-6">
          <label className="block text-sm font-medium text-gray-300 mb-2">
            Season
          </label>
          <select
            value={selectedSeason}
            onChange={(e) => handleSeasonChange(Number(e.target.value))}
            className="w-full bg-gray-800/50 border border-gray-600 rounded-lg px-3 py-2 text-white focus:outline-none focus:ring-2 focus:ring-blue-500"
          >
            {availableSeasons.map((season) => (
              <option key={season} value={season}>
                Season {season}
              </option>
            ))}
          </select>
        </div>
      )}

      {/* Episode Grid */}
      <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 gap-3">
        {currentSeasonEpisodes
          .sort((a, b) => a.episode - b.episode)
          .map((episode) => {
            const isActive = episode.season === currentSeason && episode.episode === currentEpisode;
            
            return (
              <button
                key={`${episode.season}-${episode.episode}`}
                onClick={() => handleEpisodeChange(episode.episode)}
                className={`
                  relative p-3 rounded-lg border transition-all duration-200 text-left
                  ${isActive 
                    ? 'bg-blue-600 border-blue-500 text-white' 
                    : 'bg-gray-800/50 border-gray-600 text-gray-300 hover:bg-gray-700/50 hover:border-gray-500'
                  }
                `}
              >
                <div className="font-medium text-sm">
                  Episode {episode.episode}
                </div>
                {episode.episodeTitle && (
                  <div className={`text-xs mt-1 line-clamp-2 ${isActive ? 'text-blue-100' : 'text-gray-400'}`}>
                    {episode.episodeTitle}
                  </div>
                )}
                {episode.runtime && (
                  <div className={`text-xs mt-1 ${isActive ? 'text-blue-200' : 'text-gray-500'}`}>
                    {episode.runtime}
                  </div>
                )}
                
                {/* Active indicator */}
                {isActive && (
                  <div className="absolute top-1 right-1">
                    <div className="w-2 h-2 bg-white rounded-full"></div>
                  </div>
                )}
              </button>
            );
          })}
      </div>

      {/* Episode count info */}
      <div className="mt-4 text-sm text-gray-400">
        {currentSeasonEpisodes.length} episode{currentSeasonEpisodes.length !== 1 ? 's' : ''} in Season {selectedSeason}
      </div>

      {/* Navigation buttons */}
      <div className="flex justify-between mt-6">
        <button
          onClick={() => {
            const prevEpisode = currentSeasonEpisodes.find(ep => ep.episode === currentEpisode - 1);
            if (prevEpisode) {
              handleEpisodeChange(prevEpisode.episode);
            }
          }}
          disabled={!currentSeasonEpisodes.find(ep => ep.episode === currentEpisode - 1)}
          className="px-4 py-2 bg-gray-700 text-white rounded-lg disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-600 transition-colors"
        >
          ← Previous
        </button>
        
        <button
          onClick={() => {
            const nextEpisode = currentSeasonEpisodes.find(ep => ep.episode === currentEpisode + 1);
            if (nextEpisode) {
              handleEpisodeChange(nextEpisode.episode);
            }
          }}
          disabled={!currentSeasonEpisodes.find(ep => ep.episode === currentEpisode + 1)}
          className="px-4 py-2 bg-blue-600 text-white rounded-lg disabled:opacity-50 disabled:cursor-not-allowed hover:bg-blue-500 transition-colors"
        >
          Next →
        </button>
      </div>
    </div>
  );
}
