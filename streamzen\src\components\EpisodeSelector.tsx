'use client';

import React, { useState, useEffect } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import { Play, ChevronDown, ChevronUp, Clock, Star, Calendar, RefreshCw, AlertCircle } from 'lucide-react';
import { cn } from '@/lib/utils';
import { useEpisodeChecker } from '@/hooks/useEpisodeChecker';

interface Episode {
  _id: string;
  season: number;
  episode: number;
  episodeTitle?: string;
  airDate?: string;
  runtime?: string;
  imdbRating?: number;
  description?: string;
}

interface EpisodeSelectorProps {
  seriesId: string;
  episodes: Episode[];
  currentSeason: number;
  currentEpisode: number;
}

const EpisodeSelector: React.FC<EpisodeSelectorProps> = ({
  seriesId,
  episodes: initialEpisodes,
  currentSeason,
  currentEpisode
}) => {
  const router = useRouter();
  const searchParams = useSearchParams();
  const [expandedSeasons, setExpandedSeasons] = useState<Record<number, boolean>>({
    [currentSeason]: true
  });
  const [episodes, setEpisodes] = useState<Episode[]>(initialEpisodes);
  const [showCheckButton, setShowCheckButton] = useState(true);
  const { isChecking, checkEpisodes, lastCheckResult, error } = useEpisodeChecker();

  // Auto-check for episodes on component mount
  useEffect(() => {
    const autoCheckEpisodes = async () => {
      // Always trigger episode sync to ensure we have the latest episodes
      console.log(`🔄 Auto-triggering episode sync for series ${seriesId} (S${currentSeason}E${currentEpisode})...`);
      await handleCheckEpisodes();
    };

    autoCheckEpisodes();
  }, [seriesId, currentSeason, currentEpisode]); // Run when series or episode changes

  // Handle episode checking
  const handleCheckEpisodes = async () => {
    const result = await checkEpisodes(seriesId);
    if (result && result.episodes) {
      setEpisodes(result.episodes);

      if (result.stats.newEpisodesAdded > 0 || result.stats.existingEpisodesUpdated > 0) {
        // Refresh the page to update the URL and episode selector
        router.refresh();
      }
    }
  };

  // Group episodes by season
  const episodesBySeason = episodes.reduce((acc, episode) => {
    if (!acc[episode.season]) {
      acc[episode.season] = [];
    }
    acc[episode.season].push(episode);
    return acc;
  }, {} as Record<number, Episode[]>);

  const seasons = Object.keys(episodesBySeason)
    .map(Number)
    .sort((a, b) => a - b);

  const toggleSeason = (season: number) => {
    setExpandedSeasons(prev => ({
      ...prev,
      [season]: !prev[season]
    }));
  };

  const playEpisode = (season: number, episode: number) => {
    const params = new URLSearchParams(searchParams.toString());
    params.set('season', season.toString());
    params.set('episode', episode.toString());
    router.push(`/watch/series/${seriesId}?${params.toString()}`);
  };

  const formatAirDate = (airDate?: string) => {
    if (!airDate) return '';
    return new Date(airDate).toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric',
      year: 'numeric'
    });
  };

  return (
    <div className="glass-elevated rounded-3xl overflow-hidden">
      {/* Header */}
      <div className="p-8 border-b border-white/10">
        <div className="flex items-center justify-between mb-4">
          <h3 className="text-white text-2xl font-bold">Episodes</h3>
          <div className="glass px-4 py-2 rounded-full">
            <span className="text-gray-300 text-sm font-medium">
              {episodes.length} Episodes
            </span>
          </div>
        </div>
        <p className="text-gray-400 text-lg">
          Choose an episode to continue watching
        </p>

        {/* Episode Checker */}
        <div className="mt-6 flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <button
              onClick={handleCheckEpisodes}
              disabled={isChecking}
              className={cn(
                'flex items-center space-x-2 px-4 py-2 rounded-xl font-medium transition-all duration-300',
                isChecking
                  ? 'bg-gray-600 text-gray-300 cursor-not-allowed'
                  : 'bg-blue-600 hover:bg-blue-700 text-white shadow-lg hover:shadow-xl'
              )}
            >
              <RefreshCw className={cn('w-4 h-4', isChecking && 'animate-spin')} />
              <span>{isChecking ? 'Checking...' : 'Check for New Episodes'}</span>
            </button>

            {lastCheckResult && (
              <div className="text-sm space-y-2">
                {lastCheckResult.stats.newEpisodesAdded > 0 || lastCheckResult.stats.existingEpisodesUpdated > 0 ? (
                  <div className="space-y-1">
                    {lastCheckResult.stats.newEpisodesAdded > 0 && (
                      <div className="text-green-400">
                        ✅ {lastCheckResult.stats.newEpisodesAdded} new episodes added
                      </div>
                    )}
                    {lastCheckResult.stats.existingEpisodesUpdated > 0 && (
                      <div className="text-blue-400">
                        🔄 {lastCheckResult.stats.existingEpisodesUpdated} episodes updated
                      </div>
                    )}
                  </div>
                ) : (
                  <div className="text-gray-400">
                    ✅ Up to date
                  </div>
                )}


              </div>
            )}

            {error && (
              <div className="flex items-center space-x-1 text-red-400 text-sm">
                <AlertCircle className="w-4 h-4" />
                <span>{error}</span>
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Seasons List */}
      <div className="max-h-[70vh] overflow-y-auto scrollbar-hide">
        {seasons.map((season) => {
          const seasonEpisodes = episodesBySeason[season].sort((a, b) => a.episode - b.episode);
          const isExpanded = expandedSeasons[season];
          const hasCurrentEpisode = seasonEpisodes.some(ep => ep.season === currentSeason);

          return (
            <div key={season} className="border-b border-white/5 last:border-b-0">
              {/* Season Header */}
              <button
                onClick={() => toggleSeason(season)}
                className="w-full p-6 flex items-center justify-between hover:bg-white/5 transition-all duration-300 focus-ring"
              >
                <div className="flex items-center space-x-4">
                  <div className={cn(
                    'w-12 h-12 rounded-2xl flex items-center justify-center font-bold text-lg transition-all duration-300',
                    hasCurrentEpisode
                      ? 'bg-red-600 text-white shadow-lg'
                      : 'bg-white/10 text-gray-300'
                  )}>
                    {season}
                  </div>
                  <div className="text-left">
                    <h4 className="text-white text-xl font-bold">Season {season}</h4>
                    <p className="text-gray-400 text-sm">
                      {seasonEpisodes.length} episode{seasonEpisodes.length !== 1 ? 's' : ''}
                    </p>
                  </div>
                </div>

                <div className="flex items-center space-x-3">
                  {hasCurrentEpisode && (
                    <div className="w-3 h-3 bg-blue-400 rounded-full animate-pulse" />
                  )}
                  {isExpanded ? (
                    <ChevronUp size={24} className="text-gray-400" />
                  ) : (
                    <ChevronDown size={24} className="text-gray-400" />
                  )}
                </div>
              </button>

              {/* Episodes List */}
              {isExpanded && (
                <div className="px-6 pb-6 space-y-3 animate-fade-in">
                  {seasonEpisodes.map((episode) => {
                    const isCurrentEpisode = episode.season === currentSeason && episode.episode === currentEpisode;

                    return (
                      <div
                        key={`${episode.season}-${episode.episode}`}
                        className={cn(
                          'relative p-6 rounded-2xl border transition-all duration-300 group hover:scale-[1.02] cursor-pointer',
                          isCurrentEpisode
                            ? 'bg-red-600/20 border-red-500/50 shadow-2xl'
                            : 'glass border-white/10 hover:bg-white/10 hover:border-white/20'
                        )}
                        onClick={() => playEpisode(episode.season, episode.episode)}
                      >
                        <div className="flex items-start justify-between">
                          <div className="flex items-start space-x-4 flex-1">
                            {/* Episode Number/Play Button */}
                            <div className={cn(
                              'w-14 h-14 rounded-2xl flex items-center justify-center font-bold text-lg transition-all duration-300',
                              isCurrentEpisode
                                ? 'bg-white/20 text-white'
                                : 'bg-white/10 text-gray-300 group-hover:bg-white/20'
                            )}>
                              {isCurrentEpisode ? (
                                <Play size={20} fill="currentColor" />
                              ) : (
                                <span>{episode.episode}</span>
                              )}
                            </div>

                            {/* Episode Info */}
                            <div className="flex-1 min-w-0">
                              <h5 className={cn(
                                'font-bold text-lg mb-2 line-clamp-1',
                                isCurrentEpisode ? 'text-white' : 'text-gray-200 group-hover:text-white'
                              )}>
                                Episode {episode.episode}
                                {episode.episodeTitle && `: ${episode.episodeTitle}`}
                              </h5>

                              {/* Episode Meta */}
                              <div className="flex items-center space-x-4 mb-3">
                                {episode.runtime && (
                                  <div className="flex items-center text-gray-400 text-sm">
                                    <Clock size={14} className="mr-1.5" />
                                    {episode.runtime}
                                  </div>
                                )}
                                {episode.imdbRating && (
                                  <div className="flex items-center text-gray-400 text-sm">
                                    <Star size={14} className="mr-1.5 text-yellow-400 fill-current" />
                                    {episode.imdbRating}
                                  </div>
                                )}
                                {episode.airDate && (
                                  <div className="flex items-center text-gray-400 text-sm">
                                    <Calendar size={14} className="mr-1.5" />
                                    {formatAirDate(episode.airDate)}
                                  </div>
                                )}
                                {isCurrentEpisode && (
                                  <div className="flex items-center text-red-400 text-sm font-medium">
                                    <Star size={14} className="mr-1.5 fill-current" />
                                    Now Playing
                                  </div>
                                )}
                              </div>

                              {/* Episode Description */}
                              {episode.description && (
                                <p className="text-gray-400 text-sm line-clamp-2 leading-relaxed">
                                  {episode.description}
                                </p>
                              )}
                            </div>
                          </div>
                        </div>

                        {/* Active Episode Glow */}
                        {isCurrentEpisode && (
                          <div className="absolute -inset-1 bg-red-500/20 rounded-2xl blur-xl -z-10" />
                        )}
                      </div>
                    );
                  })}
                </div>
              )}
            </div>
          );
        })}
      </div>

      {/* Footer */}
      <div className="p-6 border-t border-white/10 text-center">
        <p className="text-gray-400 text-sm">
          Episodes are automatically updated with the latest content
        </p>
      </div>
    </div>
  );
};

export default EpisodeSelector;
