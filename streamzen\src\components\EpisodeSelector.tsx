'use client';

import React, { useState, useEffect } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import { Play, ChevronDown, ChevronUp, Clock, Star, Calendar, RefreshCw, AlertCircle } from 'lucide-react';
import { cn } from '@/lib/utils';
import { useEpisodeChecker } from '@/hooks/useEpisodeChecker';

interface Episode {
  _id: string;
  season: number;
  episode: number;
  episodeTitle?: string;
  airDate?: string;
  runtime?: string;
  imdbRating?: number;
  description?: string;
}

interface EpisodeSelectorProps {
  seriesId: string;
  episodes: Episode[];
  currentSeason: number;
  currentEpisode: number;
}

const EpisodeSelector: React.FC<EpisodeSelectorProps> = ({
  seriesId,
  episodes: initialEpisodes,
  currentSeason,
  currentEpisode
}) => {
  const router = useRouter();
  const searchParams = useSearchParams();
  const [episodes, setEpisodes] = useState<Episode[]>(initialEpisodes);
  const [expandedSeasons, setExpandedSeasons] = useState<Record<number, boolean>>({});

  const {
    checkEpisodes,
    isChecking,
    lastCheckResult,
    error
  } = useEpisodeChecker(seriesId);

  // Initialize expanded seasons - expand current season by default
  useEffect(() => {
    const initialExpanded: Record<number, boolean> = {};
    const seasons = [...new Set(episodes.map(ep => ep.season))];
    
    seasons.forEach(season => {
      initialExpanded[season] = season === currentSeason;
    });
    
    setExpandedSeasons(initialExpanded);
  }, [episodes, currentSeason]);

  const handleCheckEpisodes = async () => {
    try {
      const result = await checkEpisodes();
      if (result && result.episodes) {
        setEpisodes(result.episodes);
      }
    } catch (error) {
      console.error('Failed to check episodes:', error);
    }
  };

  // Group episodes by season
  const episodesBySeason = episodes.reduce((acc, episode) => {
    if (!acc[episode.season]) {
      acc[episode.season] = [];
    }
    acc[episode.season].push(episode);
    return acc;
  }, {} as Record<number, Episode[]>);

  const seasons = Object.keys(episodesBySeason)
    .map(Number)
    .sort((a, b) => a - b);

  const toggleSeason = (season: number) => {
    setExpandedSeasons(prev => ({
      ...prev,
      [season]: !prev[season]
    }));
  };

  const playEpisode = (season: number, episode: number) => {
    const params = new URLSearchParams(searchParams.toString());
    params.set('season', season.toString());
    params.set('episode', episode.toString());
    router.push(`/watch/series/${seriesId}?${params.toString()}`);
  };

  const formatAirDate = (airDate?: string) => {
    if (!airDate) return '';
    return new Date(airDate).toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric',
      year: 'numeric'
    });
  };

  return (
    <div className="bg-gradient-to-br from-gray-900/50 via-black/50 to-gray-900/50 backdrop-blur-xl rounded-3xl overflow-hidden border border-white/10 shadow-2xl">
      {/* Modern Header */}
      <div className="relative bg-gradient-to-r from-red-500/10 via-purple-500/10 to-blue-500/10 border-b border-white/10 p-6">
        <div className="flex items-center justify-between mb-3">
          <div className="flex items-center space-x-3">
            <div className="w-12 h-12 bg-gradient-to-br from-red-500 to-purple-600 rounded-xl flex items-center justify-center shadow-lg">
              <Play size={18} className="text-white fill-current" />
            </div>
            <div>
              <h3 className="text-white text-xl font-bold">Episodes</h3>
              <p className="text-gray-300 text-sm">
                {episodes.length} episodes available
              </p>
            </div>
          </div>
          <div className="px-3 py-1.5 bg-white/10 rounded-full border border-white/20">
            <span className="text-gray-300 text-sm font-medium">
              {seasons.length} seasons
            </span>
          </div>
        </div>

        {/* Episode Checker */}
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <button
              onClick={handleCheckEpisodes}
              disabled={isChecking}
              className={cn(
                'flex items-center space-x-2 px-4 py-2 rounded-xl font-medium transition-all duration-300',
                isChecking
                  ? 'bg-gray-600/50 text-gray-300 cursor-not-allowed'
                  : 'bg-blue-500/20 hover:bg-blue-500/30 text-blue-400 border border-blue-500/30'
              )}
            >
              <RefreshCw className={cn('w-4 h-4', isChecking && 'animate-spin')} />
              <span className="text-sm">{isChecking ? 'Checking...' : 'Check Episodes'}</span>
            </button>

            {lastCheckResult && (
              <div className="text-sm">
                {lastCheckResult.stats.newEpisodesAdded > 0 || lastCheckResult.stats.existingEpisodesUpdated > 0 ? (
                  <div className="flex items-center space-x-2">
                    {lastCheckResult.stats.newEpisodesAdded > 0 && (
                      <span className="text-green-400 text-xs">
                        ✅ {lastCheckResult.stats.newEpisodesAdded} new
                      </span>
                    )}
                    {lastCheckResult.stats.existingEpisodesUpdated > 0 && (
                      <span className="text-blue-400 text-xs">
                        🔄 {lastCheckResult.stats.existingEpisodesUpdated} updated
                      </span>
                    )}
                  </div>
                ) : (
                  <span className="text-gray-400 text-xs">✅ Up to date</span>
                )}
              </div>
            )}

            {error && (
              <div className="flex items-center space-x-1 text-red-400">
                <AlertCircle className="w-4 h-4" />
                <span className="text-xs">{error}</span>
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Seasons List */}
      <div className="max-h-[70vh] overflow-y-auto scrollbar-hide">
        {seasons.map((season) => {
          const seasonEpisodes = episodesBySeason[season].sort((a, b) => a.episode - b.episode);
          const isExpanded = expandedSeasons[season];
          const hasCurrentEpisode = seasonEpisodes.some(ep => ep.season === currentSeason);

          return (
            <div key={season} className="border-b border-white/5 last:border-b-0">
              {/* Season Header */}
              <button
                onClick={() => toggleSeason(season)}
                className="w-full p-5 flex items-center justify-between hover:bg-white/5 transition-all duration-300 group"
              >
                <div className="flex items-center space-x-3">
                  <div className={cn(
                    'w-10 h-10 rounded-xl flex items-center justify-center font-bold text-sm transition-all duration-300',
                    hasCurrentEpisode
                      ? 'bg-gradient-to-br from-red-500 to-red-600 text-white shadow-lg'
                      : 'bg-white/10 text-gray-300 group-hover:bg-white/20'
                  )}>
                    {season}
                  </div>
                  <div className="text-left">
                    <h4 className="text-white text-lg font-bold">Season {season}</h4>
                    <p className="text-gray-400 text-sm">
                      {seasonEpisodes.length} episodes
                    </p>
                  </div>
                </div>

                <div className="flex items-center space-x-2">
                  {hasCurrentEpisode && (
                    <div className="w-2 h-2 bg-red-500 rounded-full animate-pulse" />
                  )}
                  {isExpanded ? (
                    <ChevronUp className="w-5 h-5 text-gray-400 group-hover:text-white transition-colors" />
                  ) : (
                    <ChevronDown className="w-5 h-5 text-gray-400 group-hover:text-white transition-colors" />
                  )}
                </div>
              </button>

              {/* Episodes Grid */}
              {isExpanded && (
                <div className="p-4 space-y-3">
                  {seasonEpisodes.map((episode) => {
                    const isCurrentEpisode = episode.season === currentSeason && episode.episode === currentEpisode;

                    return (
                      <div
                        key={`${episode.season}-${episode.episode}`}
                        className={cn(
                          'relative p-4 rounded-xl border transition-all duration-300 group hover:scale-[1.01] cursor-pointer',
                          isCurrentEpisode
                            ? 'bg-gradient-to-r from-red-500/20 to-purple-500/20 border-red-500/50 shadow-lg'
                            : 'bg-white/5 border-white/10 hover:bg-white/10 hover:border-white/20'
                        )}
                        onClick={() => playEpisode(episode.season, episode.episode)}
                      >
                        <div className="flex items-center space-x-3">
                          {/* Episode Number/Play Button */}
                          <div className={cn(
                            'w-12 h-12 rounded-xl flex items-center justify-center font-bold transition-all duration-300',
                            isCurrentEpisode
                              ? 'bg-gradient-to-br from-red-500 to-red-600 text-white shadow-lg'
                              : 'bg-white/10 text-gray-300 group-hover:bg-white/20'
                          )}>
                            {isCurrentEpisode ? (
                              <Play size={16} fill="currentColor" />
                            ) : (
                              <span className="text-sm">{episode.episode}</span>
                            )}
                          </div>

                          {/* Episode Info */}
                          <div className="flex-1 min-w-0">
                            <h5 className={cn(
                              'font-bold text-base mb-1 line-clamp-1',
                              isCurrentEpisode ? 'text-white' : 'text-gray-200 group-hover:text-white'
                            )}>
                              Episode {episode.episode}
                              {episode.episodeTitle && `: ${episode.episodeTitle}`}
                            </h5>

                            {/* Episode Meta */}
                            <div className="flex items-center space-x-3 text-xs text-gray-400">
                              {episode.runtime && (
                                <div className="flex items-center">
                                  <Clock size={12} className="mr-1" />
                                  {episode.runtime}
                                </div>
                              )}
                              {episode.imdbRating && (
                                <div className="flex items-center">
                                  <Star size={12} className="mr-1 text-yellow-400 fill-current" />
                                  {episode.imdbRating}
                                </div>
                              )}
                              {episode.airDate && (
                                <div className="flex items-center">
                                  <Calendar size={12} className="mr-1" />
                                  {formatAirDate(episode.airDate)}
                                </div>
                              )}
                              {isCurrentEpisode && (
                                <div className="flex items-center text-red-400 font-medium">
                                  <span>● Now Playing</span>
                                </div>
                              )}
                            </div>

                            {/* Episode Description */}
                            {episode.description && (
                              <p className="text-gray-400 text-xs mt-2 line-clamp-2 leading-relaxed">
                                {episode.description}
                              </p>
                            )}
                          </div>
                        </div>
                      </div>
                    );
                  })}
                </div>
              )}
            </div>
          );
        })}
      </div>
    </div>
  );
};

export default EpisodeSelector;
