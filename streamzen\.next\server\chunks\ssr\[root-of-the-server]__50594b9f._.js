module.exports = {

"[project]/.next-internal/server/app/watch/series/[id]/page/actions.js [app-rsc] (server actions loader, ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
}}),
"[project]/src/app/favicon.ico.mjs { IMAGE => \"[project]/src/app/favicon.ico (static in ecmascript)\" } [app-rsc] (structured image object, ecmascript, Next.js server component)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.n(__turbopack_context__.i("[project]/src/app/favicon.ico.mjs { IMAGE => \"[project]/src/app/favicon.ico (static in ecmascript)\" } [app-rsc] (structured image object, ecmascript)"));
}}),
"[project]/src/app/layout.tsx [app-rsc] (ecmascript, Next.js server component)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.n(__turbopack_context__.i("[project]/src/app/layout.tsx [app-rsc] (ecmascript)"));
}}),
"[project]/src/lib/api.ts [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "apiClient": (()=>apiClient),
    "default": (()=>__TURBOPACK__default__export__)
});
const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || (("TURBOPACK compile-time falsy", 0) ? ("TURBOPACK unreachable", undefined) : 'http://localhost:3000');
class ApiClient {
    baseUrl;
    constructor(baseUrl = API_BASE_URL){
        this.baseUrl = baseUrl;
    }
    async request(endpoint, options) {
        // For server-side rendering, use absolute URL
        const baseUrl = this.baseUrl || (("TURBOPACK compile-time falsy", 0) ? ("TURBOPACK unreachable", undefined) : 'http://localhost:3000');
        const url = endpoint.startsWith('http') ? endpoint : `${baseUrl}${endpoint}`;
        try {
            const response = await fetch(url, {
                headers: {
                    'Content-Type': 'application/json',
                    ...options?.headers
                },
                ...options
            });
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }
            return await response.json();
        } catch (error) {
            console.error(`API request failed: ${url}`, error);
            throw error;
        }
    }
    // Movies
    async getMovies(filters = {}) {
        const params = new URLSearchParams();
        Object.entries(filters).forEach(([key, value])=>{
            if (value !== undefined && value !== null) {
                params.append(key, value.toString());
            }
        });
        return this.request(`/api/movies?${params.toString()}`);
    }
    async getMovie(id) {
        return this.request(`/api/movies/${id}`);
    }
    // Series
    async getSeries(filters = {}) {
        const params = new URLSearchParams();
        Object.entries(filters).forEach(([key, value])=>{
            if (value !== undefined && value !== null) {
                params.append(key, value.toString());
            }
        });
        return this.request(`/api/series?${params.toString()}`);
    }
    async getSeriesById(id) {
        return this.request(`/api/series/${id}`);
    }
    async getSeriesEpisodes(id, season) {
        const params = season ? `?season=${season}` : '';
        return this.request(`/api/series/${id}/episodes${params}`);
    }
    // Episodes
    async getEpisodes(filters = {}) {
        const params = new URLSearchParams();
        Object.entries(filters).forEach(([key, value])=>{
            if (value !== undefined && value !== null) {
                params.append(key, value.toString());
            }
        });
        return this.request(`/api/episodes?${params.toString()}`);
    }
    // Requests
    async createBulkRequest(imdbIds, contentType = 'auto') {
        return this.request('/api/requests', {
            method: 'POST',
            body: JSON.stringify({
                imdbIds,
                contentType
            })
        });
    }
    async getRequestStatus(requestId) {
        return this.request(`/api/requests/${requestId}`);
    }
    // Filter Options
    async getMovieFilterOptions() {
        return this.request('/api/movies/filters');
    }
    async getSeriesFilterOptions() {
        return this.request('/api/series/filters');
    }
    async getEpisodeFilterOptions() {
        return this.request('/api/episodes/filters');
    }
    // Search
    async search(query, type = 'all', page = 1, limit = 20) {
        const params = new URLSearchParams({
            q: query,
            type,
            page: page.toString(),
            limit: limit.toString()
        });
        return this.request(`/api/search?${params.toString()}`);
    }
    async getSearchSuggestions(query, limit = 8) {
        const params = new URLSearchParams({
            q: query,
            limit: limit.toString()
        });
        return this.request(`/api/search/suggestions?${params.toString()}`);
    }
    // Sync
    async syncContent() {
        return this.request('/api/sync', {
            method: 'POST'
        });
    }
}
const apiClient = new ApiClient();
const __TURBOPACK__default__export__ = ApiClient;
}}),
"[project]/src/components/VideoPlayer.tsx (client reference/proxy) <module evaluation>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2d$edge$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-turbopack-server-edge.js [app-rsc] (ecmascript)");
;
const __TURBOPACK__default__export__ = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2d$edge$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerClientReference"])(function() {
    throw new Error("Attempted to call the default export of [project]/src/components/VideoPlayer.tsx <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.");
}, "[project]/src/components/VideoPlayer.tsx <module evaluation>", "default");
}}),
"[project]/src/components/VideoPlayer.tsx (client reference/proxy)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2d$edge$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-turbopack-server-edge.js [app-rsc] (ecmascript)");
;
const __TURBOPACK__default__export__ = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2d$edge$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerClientReference"])(function() {
    throw new Error("Attempted to call the default export of [project]/src/components/VideoPlayer.tsx from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.");
}, "[project]/src/components/VideoPlayer.tsx", "default");
}}),
"[project]/src/components/VideoPlayer.tsx [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$VideoPlayer$2e$tsx__$28$client__reference$2f$proxy$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/src/components/VideoPlayer.tsx (client reference/proxy) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$VideoPlayer$2e$tsx__$28$client__reference$2f$proxy$29$__ = __turbopack_context__.i("[project]/src/components/VideoPlayer.tsx (client reference/proxy)");
;
__turbopack_context__.n(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$VideoPlayer$2e$tsx__$28$client__reference$2f$proxy$29$__);
}}),
"[project]/src/components/EpisodeSelector.tsx (client reference/proxy) <module evaluation>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2d$edge$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-turbopack-server-edge.js [app-rsc] (ecmascript)");
;
const __TURBOPACK__default__export__ = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2d$edge$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerClientReference"])(function() {
    throw new Error("Attempted to call the default export of [project]/src/components/EpisodeSelector.tsx <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.");
}, "[project]/src/components/EpisodeSelector.tsx <module evaluation>", "default");
}}),
"[project]/src/components/EpisodeSelector.tsx (client reference/proxy)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2d$edge$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-turbopack-server-edge.js [app-rsc] (ecmascript)");
;
const __TURBOPACK__default__export__ = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2d$edge$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerClientReference"])(function() {
    throw new Error("Attempted to call the default export of [project]/src/components/EpisodeSelector.tsx from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.");
}, "[project]/src/components/EpisodeSelector.tsx", "default");
}}),
"[project]/src/components/EpisodeSelector.tsx [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$EpisodeSelector$2e$tsx__$28$client__reference$2f$proxy$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/src/components/EpisodeSelector.tsx (client reference/proxy) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$EpisodeSelector$2e$tsx__$28$client__reference$2f$proxy$29$__ = __turbopack_context__.i("[project]/src/components/EpisodeSelector.tsx (client reference/proxy)");
;
__turbopack_context__.n(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$EpisodeSelector$2e$tsx__$28$client__reference$2f$proxy$29$__);
}}),
"[externals]/util [external] (util, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("util", () => require("util"));

module.exports = mod;
}}),
"[externals]/stream [external] (stream, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("stream", () => require("stream"));

module.exports = mod;
}}),
"[externals]/http [external] (http, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("http", () => require("http"));

module.exports = mod;
}}),
"[externals]/https [external] (https, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("https", () => require("https"));

module.exports = mod;
}}),
"[externals]/fs [external] (fs, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("fs", () => require("fs"));

module.exports = mod;
}}),
"[externals]/crypto [external] (crypto, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("crypto", () => require("crypto"));

module.exports = mod;
}}),
"[externals]/assert [external] (assert, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("assert", () => require("assert"));

module.exports = mod;
}}),
"[externals]/tty [external] (tty, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("tty", () => require("tty"));

module.exports = mod;
}}),
"[externals]/os [external] (os, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("os", () => require("os"));

module.exports = mod;
}}),
"[externals]/zlib [external] (zlib, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("zlib", () => require("zlib"));

module.exports = mod;
}}),
"[externals]/events [external] (events, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("events", () => require("events"));

module.exports = mod;
}}),
"[project]/src/lib/vidsrc.ts [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "STREAMING_SOURCES": (()=>STREAMING_SOURCES),
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$axios$2f$lib$2f$axios$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/axios/lib/axios.js [app-rsc] (ecmascript)");
;
const VIDSRC_BASE_URL = process.env.VIDSRC_BASE_URL || 'https://vidsrc.xyz';
const STREAMING_SOURCES = {
    vidsrc_xyz: {
        name: 'VidSrc XYZ',
        baseUrl: 'https://vidsrc.xyz',
        quality: 'HD',
        priority: 1
    },
    autoembed: {
        name: 'AutoEmbed',
        baseUrl: 'https://player.autoembed.cc',
        quality: 'Premium HD',
        priority: 2
    },
    vidsrc_icu: {
        name: 'VidSrc ICU',
        baseUrl: 'https://vidsrc.icu',
        quality: 'HD',
        priority: 3
    },
    vidsrc_cc_v2: {
        name: 'VidSrc CC v2',
        baseUrl: 'https://vidsrc.cc/v2',
        quality: 'HD',
        priority: 4
    },
    vidsrc_cc_v3: {
        name: 'VidSrc CC v3',
        baseUrl: 'https://vidsrc.cc/v3',
        quality: 'HD',
        priority: 5
    }
};
class VidSrcAPI {
    static instance;
    static getInstance() {
        if (!VidSrcAPI.instance) {
            VidSrcAPI.instance = new VidSrcAPI();
        }
        return VidSrcAPI.instance;
    }
    /**
   * Generate movie embed URLs for all sources
   */ generateAllMovieEmbedUrls(imdbId, tmdbId) {
        const cleanImdbId = imdbId.replace('tt', '');
        const urls = [];
        // VidSrc XYZ
        urls.push({
            source: 'vidsrc_xyz',
            name: STREAMING_SOURCES.vidsrc_xyz.name,
            url: `${STREAMING_SOURCES.vidsrc_xyz.baseUrl}/embed/movie?imdb=${imdbId}`,
            quality: STREAMING_SOURCES.vidsrc_xyz.quality,
            priority: STREAMING_SOURCES.vidsrc_xyz.priority
        });
        // AutoEmbed
        urls.push({
            source: 'autoembed',
            name: STREAMING_SOURCES.autoembed.name,
            url: `${STREAMING_SOURCES.autoembed.baseUrl}/embed/movie/${imdbId}`,
            quality: STREAMING_SOURCES.autoembed.quality,
            priority: STREAMING_SOURCES.autoembed.priority
        });
        // VidSrc ICU
        urls.push({
            source: 'vidsrc_icu',
            name: STREAMING_SOURCES.vidsrc_icu.name,
            url: `${STREAMING_SOURCES.vidsrc_icu.baseUrl}/embed/movie/${imdbId}`,
            quality: STREAMING_SOURCES.vidsrc_icu.quality,
            priority: STREAMING_SOURCES.vidsrc_icu.priority
        });
        // VidSrc CC v2
        urls.push({
            source: 'vidsrc_cc_v2',
            name: STREAMING_SOURCES.vidsrc_cc_v2.name,
            url: `${STREAMING_SOURCES.vidsrc_cc_v2.baseUrl}/embed/movie/${imdbId}`,
            quality: STREAMING_SOURCES.vidsrc_cc_v2.quality,
            priority: STREAMING_SOURCES.vidsrc_cc_v2.priority
        });
        // VidSrc CC v3
        urls.push({
            source: 'vidsrc_cc_v3',
            name: STREAMING_SOURCES.vidsrc_cc_v3.name,
            url: `${STREAMING_SOURCES.vidsrc_cc_v3.baseUrl}/embed/movie/${imdbId}`,
            quality: STREAMING_SOURCES.vidsrc_cc_v3.quality,
            priority: STREAMING_SOURCES.vidsrc_cc_v3.priority
        });
        return urls.sort((a, b)=>a.priority - b.priority);
    }
    /**
   * Generate movie embed URL (legacy method for backward compatibility)
   */ generateMovieEmbedUrl(imdbId, options) {
        const baseUrl = `${VIDSRC_BASE_URL}/embed/movie`;
        const params = new URLSearchParams();
        if (options?.tmdbId) {
            params.append('tmdb', options.tmdbId);
        } else {
            params.append('imdb', imdbId);
        }
        if (options?.subUrl) {
            params.append('sub_url', encodeURIComponent(options.subUrl));
        }
        if (options?.dsLang) {
            params.append('ds_lang', options.dsLang);
        }
        if (options?.autoplay !== undefined) {
            params.append('autoplay', options.autoplay ? '1' : '0');
        }
        return `${baseUrl}?${params.toString()}`;
    }
    /**
   * Generate episode embed URLs for all sources
   */ generateAllEpisodeEmbedUrls(imdbId, season, episode, tmdbId) {
        const cleanImdbId = imdbId.replace('tt', '');
        const urls = [];
        // VidSrc XYZ
        urls.push({
            source: 'vidsrc_xyz',
            name: STREAMING_SOURCES.vidsrc_xyz.name,
            url: `${STREAMING_SOURCES.vidsrc_xyz.baseUrl}/embed/tv?imdb=${imdbId}&season=${season}&episode=${episode}`,
            quality: STREAMING_SOURCES.vidsrc_xyz.quality,
            priority: STREAMING_SOURCES.vidsrc_xyz.priority
        });
        // AutoEmbed
        urls.push({
            source: 'autoembed',
            name: STREAMING_SOURCES.autoembed.name,
            url: `${STREAMING_SOURCES.autoembed.baseUrl}/embed/tv/${imdbId}/${season}/${episode}`,
            quality: STREAMING_SOURCES.autoembed.quality,
            priority: STREAMING_SOURCES.autoembed.priority
        });
        // VidSrc ICU
        urls.push({
            source: 'vidsrc_icu',
            name: STREAMING_SOURCES.vidsrc_icu.name,
            url: `${STREAMING_SOURCES.vidsrc_icu.baseUrl}/embed/tv/${imdbId}/${season}/${episode}`,
            quality: STREAMING_SOURCES.vidsrc_icu.quality,
            priority: STREAMING_SOURCES.vidsrc_icu.priority
        });
        // VidSrc CC v2
        urls.push({
            source: 'vidsrc_cc_v2',
            name: STREAMING_SOURCES.vidsrc_cc_v2.name,
            url: `${STREAMING_SOURCES.vidsrc_cc_v2.baseUrl}/embed/tv/${imdbId}/${season}/${episode}`,
            quality: STREAMING_SOURCES.vidsrc_cc_v2.quality,
            priority: STREAMING_SOURCES.vidsrc_cc_v2.priority
        });
        // VidSrc CC v3
        urls.push({
            source: 'vidsrc_cc_v3',
            name: STREAMING_SOURCES.vidsrc_cc_v3.name,
            url: `${STREAMING_SOURCES.vidsrc_cc_v3.baseUrl}/embed/tv/${imdbId}/${season}/${episode}`,
            quality: STREAMING_SOURCES.vidsrc_cc_v3.quality,
            priority: STREAMING_SOURCES.vidsrc_cc_v3.priority
        });
        return urls.sort((a, b)=>a.priority - b.priority);
    }
    /**
   * Generate series embed URL (legacy method for backward compatibility)
   */ generateSeriesEmbedUrl(imdbId, options) {
        const baseUrl = `${VIDSRC_BASE_URL}/embed/tv`;
        const params = new URLSearchParams();
        if (options?.tmdbId) {
            params.append('tmdb', options.tmdbId);
        } else {
            params.append('imdb', imdbId);
        }
        if (options?.dsLang) {
            params.append('ds_lang', options.dsLang);
        }
        return `${baseUrl}?${params.toString()}`;
    }
    /**
   * Generate episode embed URL
   */ generateEpisodeEmbedUrl(imdbId, season, episode, options) {
        const baseUrl = `${VIDSRC_BASE_URL}/embed/tv`;
        const params = new URLSearchParams();
        if (options?.tmdbId) {
            params.append('tmdb', options.tmdbId);
        } else {
            params.append('imdb', imdbId);
        }
        params.append('season', season.toString());
        params.append('episode', episode.toString());
        if (options?.subUrl) {
            params.append('sub_url', encodeURIComponent(options.subUrl));
        }
        if (options?.dsLang) {
            params.append('ds_lang', options.dsLang);
        }
        if (options?.autoplay !== undefined) {
            params.append('autoplay', options.autoplay ? '1' : '0');
        }
        if (options?.autonext !== undefined) {
            params.append('autonext', options.autonext ? '1' : '0');
        }
        return `${baseUrl}?${params.toString()}`;
    }
    /**
   * Fetch latest movies from VidSrc
   */ async getLatestMovies(page = 1) {
        try {
            const url = `${VIDSRC_BASE_URL}/movies/latest/page-${page}.json`;
            const response = await __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$axios$2f$lib$2f$axios$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["default"].get(url);
            return response.data.result || [];
        } catch (error) {
            console.error(`Error fetching latest movies from VidSrc (page ${page}):`, error);
            return [];
        }
    }
    /**
   * Fetch latest TV shows from VidSrc
   */ async getLatestSeries(page = 1) {
        try {
            const url = `${VIDSRC_BASE_URL}/tvshows/latest/page-${page}.json`;
            const response = await __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$axios$2f$lib$2f$axios$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["default"].get(url);
            return response.data.result || [];
        } catch (error) {
            console.error(`Error fetching latest series from VidSrc (page ${page}):`, error);
            return [];
        }
    }
    /**
   * Fetch latest episodes from VidSrc
   */ async getLatestEpisodes(page = 1) {
        try {
            const url = `${VIDSRC_BASE_URL}/episodes/latest/page-${page}.json`;
            const response = await __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$axios$2f$lib$2f$axios$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["default"].get(url);
            return response.data.result || [];
        } catch (error) {
            console.error(`Error fetching latest episodes from VidSrc (page ${page}):`, error);
            return [];
        }
    }
    /**
   * SIMPLE: Get episodes for a specific series from VidSrc (for embed URLs only)
   * This is now used only to get streaming links, not for episode discovery
   */ async getSeriesEpisodes(imdbId) {
        try {
            console.log(`🔍 ADVANCED: Comprehensive episode search for series: ${imdbId}`);
            const allEpisodes = new Map();
            // Strategy 1: Search through ALL latest episodes (not just 15 pages)
            console.log(`📡 Strategy 1: Scanning latest episodes across all pages...`);
            let foundEpisodesInLatest = 0;
            for(let page = 1; page <= 50; page++){
                try {
                    const episodes = await this.getLatestEpisodes(page);
                    if (episodes.length === 0) break; // No more episodes
                    const seriesEpisodes = episodes.filter((episode)=>episode.imdb_id === imdbId);
                    seriesEpisodes.forEach((episode)=>{
                        const key = `S${episode.season}E${episode.episode}`;
                        if (!allEpisodes.has(key)) {
                            allEpisodes.set(key, {
                                season: episode.season,
                                episode: episode.episode,
                                embed_url: episode.embed_url,
                                embed_url_tmdb: episode.embed_url_tmdb
                            });
                            foundEpisodesInLatest++;
                        }
                    });
                    if (seriesEpisodes.length > 0) {
                        console.log(`📺 Page ${page}: Found ${seriesEpisodes.length} episodes`);
                    }
                    // If no episodes found in last 10 pages, likely reached the end
                    if (page > 10 && seriesEpisodes.length === 0) {
                        let emptyPages = 0;
                        for(let checkPage = page - 9; checkPage <= page; checkPage++){
                            const checkEpisodes = await this.getLatestEpisodes(checkPage);
                            if (checkEpisodes.filter((ep)=>ep.imdb_id === imdbId).length === 0) {
                                emptyPages++;
                            }
                        }
                        if (emptyPages >= 8) break; // Stop if 8/10 recent pages are empty
                    }
                } catch (error) {
                    console.error(`Error fetching episodes page ${page}:`, error);
                }
            }
            console.log(`✅ Strategy 1 Complete: Found ${foundEpisodesInLatest} episodes in latest pages`);
            // Strategy 2: Search through series-specific pages (if available)
            console.log(`📡 Strategy 2: Searching series-specific endpoints...`);
            let foundEpisodesInSeries = 0;
            for(let page = 1; page <= 20; page++){
                try {
                    const seriesEpisodes = await this.getLatestSeries(page);
                    const matchingSeries = seriesEpisodes.filter((series)=>series.imdb_id === imdbId);
                    if (matchingSeries.length > 0) {
                        console.log(`📺 Series page ${page}: Found matching series, checking for episode data`);
                    // If series data includes episode information, extract it
                    // This is a placeholder for potential series-specific episode data
                    }
                } catch (error) {
                // Series endpoint might not exist, continue
                }
            }
            // Strategy 3: Systematic season-by-season verification
            console.log(`📡 Strategy 3: Systematic season verification...`);
            const episodesBySeason = new Map();
            // Group found episodes by season
            allEpisodes.forEach((episode, key)=>{
                if (!episodesBySeason.has(episode.season)) {
                    episodesBySeason.set(episode.season, []);
                }
                episodesBySeason.get(episode.season).push(episode.episode);
            });
            // Analyze each season for gaps and missing episodes
            for (const [season, episodes] of episodesBySeason){
                episodes.sort((a, b)=>a - b);
                const minEp = Math.min(...episodes);
                const maxEp = Math.max(...episodes);
                const missingEpisodes = [];
                for(let ep = 1; ep <= maxEp; ep++){
                    if (!episodes.includes(ep)) {
                        missingEpisodes.push(ep);
                    }
                }
                if (missingEpisodes.length > 0) {
                    console.log(`⚠️ Season ${season}: Missing episodes ${missingEpisodes.join(', ')} (Have: ${episodes.join(', ')})`);
                    // Strategy 3a: Search for missing episodes specifically
                    console.log(`🔍 Searching for missing episodes in Season ${season}...`);
                // This would involve more targeted searches if VidSrc had episode-specific endpoints
                }
            }
            // Convert Map back to array and sort
            const uniqueEpisodes = Array.from(allEpisodes.values()).sort((a, b)=>{
                if (a.season !== b.season) return a.season - b.season;
                return a.episode - b.episode;
            });
            // Final analysis
            const totalSeasons = episodesBySeason.size;
            const totalEpisodes = uniqueEpisodes.length;
            const seasonRanges = Array.from(episodesBySeason.keys()).sort((a, b)=>a - b);
            const minSeason = seasonRanges[0] || 0;
            const maxSeason = seasonRanges[seasonRanges.length - 1] || 0;
            console.log(`🎯 COMPREHENSIVE SEARCH COMPLETE:`);
            console.log(`   📺 Total Episodes Found: ${totalEpisodes}`);
            console.log(`   🗂️ Seasons Found: ${totalSeasons} (S${minSeason}-S${maxSeason})`);
            console.log(`   📊 Episodes per Season:`);
            episodesBySeason.forEach((episodes, season)=>{
                episodes.sort((a, b)=>a - b);
                const gaps = [];
                const maxEp = Math.max(...episodes);
                for(let ep = 1; ep <= maxEp; ep++){
                    if (!episodes.includes(ep)) gaps.push(ep);
                }
                console.log(`      S${season}: ${episodes.length} episodes (${episodes.join(', ')})${gaps.length > 0 ? ` - Missing: ${gaps.join(', ')}` : ' - Complete'}`);
            });
            return uniqueEpisodes;
        } catch (error) {
            console.error(`Error in comprehensive episode search for series ${imdbId}:`, error);
            return [];
        }
    }
    /**
   * Sync latest content from VidSrc to our database
   */ async syncLatestContent(pages = 5) {
        const movies = [];
        const series = [];
        const episodes = [];
        // Fetch multiple pages in parallel
        const moviePromises = Array.from({
            length: pages
        }, (_, i)=>this.getLatestMovies(i + 1));
        const seriesPromises = Array.from({
            length: pages
        }, (_, i)=>this.getLatestSeries(i + 1));
        const episodePromises = Array.from({
            length: pages
        }, (_, i)=>this.getLatestEpisodes(i + 1));
        try {
            const [movieResults, seriesResults, episodeResults] = await Promise.all([
                Promise.all(moviePromises),
                Promise.all(seriesPromises),
                Promise.all(episodePromises)
            ]);
            // Flatten results
            movieResults.forEach((pageMovies)=>movies.push(...pageMovies));
            seriesResults.forEach((pageSeries)=>series.push(...pageSeries));
            episodeResults.forEach((pageEpisodes)=>episodes.push(...pageEpisodes));
            return {
                movies,
                series,
                episodes
            };
        } catch (error) {
            console.error('Error syncing latest content from VidSrc:', error);
            return {
                movies,
                series,
                episodes
            };
        }
    }
}
const __TURBOPACK__default__export__ = VidSrcAPI;
}}),
"[project]/src/lib/seo.ts [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "SEOGenerator": (()=>SEOGenerator)
});
class SEOGenerator {
    static baseUrl = process.env.NEXT_PUBLIC_BASE_URL || 'https://freemovieswatchnow.com';
    static siteName = 'freeMoviesWatchNow';
    static defaultDescription = 'Watch free movies and TV series online in HD quality. Stream the latest movies, TV shows, and episodes with multiple streaming sources.';
    static generateMetadata(config) {
        const { title, description, keywords = [], canonical, ogImage, ogType = 'website', publishedTime, modifiedTime, authors = [], section, tags = [] } = config;
        const fullTitle = title.includes(this.siteName) ? title : `${title} | ${this.siteName}`;
        const url = canonical ? `${this.baseUrl}${canonical}` : this.baseUrl;
        const defaultImage = `${this.baseUrl}/og-default.jpg`;
        return {
            title: fullTitle,
            description,
            keywords: keywords.join(', '),
            authors: authors.map((name)=>({
                    name
                })),
            creator: this.siteName,
            publisher: this.siteName,
            formatDetection: {
                email: false,
                address: false,
                telephone: false
            },
            metadataBase: new URL(this.baseUrl),
            alternates: {
                canonical: url
            },
            openGraph: {
                title: fullTitle,
                description,
                url,
                siteName: this.siteName,
                images: [
                    {
                        url: ogImage || defaultImage,
                        width: 1200,
                        height: 630,
                        alt: title
                    }
                ],
                locale: 'en_US',
                type: ogType,
                ...publishedTime && {
                    publishedTime
                },
                ...modifiedTime && {
                    modifiedTime
                },
                ...section && {
                    section
                },
                ...tags.length > 0 && {
                    tags
                }
            },
            twitter: {
                card: 'summary_large_image',
                title: fullTitle,
                description,
                images: [
                    ogImage || defaultImage
                ],
                creator: '@freemovieswatchnow',
                site: '@freemovieswatchnow'
            },
            robots: {
                index: true,
                follow: true,
                googleBot: {
                    index: true,
                    follow: true,
                    'max-video-preview': -1,
                    'max-image-preview': 'large',
                    'max-snippet': -1
                }
            },
            verification: {
                google: process.env.GOOGLE_VERIFICATION_ID,
                yandex: process.env.YANDEX_VERIFICATION_ID,
                yahoo: process.env.YAHOO_VERIFICATION_ID
            }
        };
    }
    static generateMovieMetadata(movie) {
        const title = `Watch ${movie.title} (${movie.year}) Online Free`;
        const description = `Watch ${movie.title} (${movie.year}) online free in HD quality. ${movie.description || `Starring ${movie.cast?.slice(0, 3).join(', ') || 'top actors'}. Stream now on ${this.siteName}.`}`;
        const keywords = [
            movie.title,
            `${movie.title} ${movie.year}`,
            `watch ${movie.title}`,
            `${movie.title} online`,
            `${movie.title} free`,
            'watch movies online',
            'free movies',
            'HD movies',
            ...movie.genres || [],
            ...movie.cast?.slice(0, 5) || [],
            movie.director,
            movie.language,
            movie.country
        ].filter(Boolean);
        return this.generateMetadata({
            title,
            description,
            keywords,
            canonical: `/watch/movie/${movie.imdbId}`,
            ogImage: movie.posterUrl,
            ogType: 'video.movie',
            publishedTime: movie.createdAt ? new Date(movie.createdAt).toISOString() : undefined,
            modifiedTime: movie.updatedAt ? new Date(movie.updatedAt).toISOString() : undefined,
            authors: [
                movie.director
            ].filter(Boolean),
            section: 'Movies',
            tags: movie.genres
        });
    }
    static generateSeriesMetadata(series) {
        const title = `Watch ${series.title} (${series.startYear}${series.endYear ? `-${series.endYear}` : ''}) Online Free`;
        const description = `Watch ${series.title} TV series online free in HD quality. ${series.description || `${series.totalSeasons} seasons available. Starring ${series.cast?.slice(0, 3).join(', ') || 'top actors'}. Stream all episodes now on ${this.siteName}.`}`;
        const keywords = [
            series.title,
            `${series.title} ${series.startYear}`,
            `watch ${series.title}`,
            `${series.title} online`,
            `${series.title} free`,
            `${series.title} episodes`,
            'watch series online',
            'free TV shows',
            'HD series',
            ...series.genres || [],
            ...series.cast?.slice(0, 5) || [],
            series.language,
            series.country
        ].filter(Boolean);
        return this.generateMetadata({
            title,
            description,
            keywords,
            canonical: `/watch/series/${series.imdbId}`,
            ogImage: series.posterUrl,
            ogType: 'video.tv_show',
            publishedTime: series.createdAt ? new Date(series.createdAt).toISOString() : undefined,
            modifiedTime: series.updatedAt ? new Date(series.updatedAt).toISOString() : undefined,
            section: 'TV Series',
            tags: series.genres
        });
    }
    static generateEpisodeMetadata(episode, series) {
        const episodeTitle = episode.episodeTitle || `Episode ${episode.episode}`;
        const title = `Watch ${episode.seriesTitle} S${episode.season}E${episode.episode} - ${episodeTitle} Online Free`;
        const description = `Watch ${episode.seriesTitle} Season ${episode.season} Episode ${episode.episode} "${episodeTitle}" online free in HD quality. ${episode.description || `Latest episode from ${episode.seriesTitle}. Stream now on ${this.siteName}.`}`;
        const keywords = [
            episode.seriesTitle,
            `${episode.seriesTitle} S${episode.season}E${episode.episode}`,
            `${episode.seriesTitle} season ${episode.season}`,
            `watch ${episode.seriesTitle}`,
            episodeTitle,
            'watch episodes online',
            'free episodes',
            'HD episodes',
            ...episode.genres || series?.genres || []
        ].filter(Boolean);
        return this.generateMetadata({
            title,
            description,
            keywords,
            canonical: `/watch/series/${episode.imdbId}?season=${episode.season}&episode=${episode.episode}`,
            ogImage: series?.posterUrl,
            ogType: 'video.episode',
            publishedTime: episode.createdAt ? new Date(episode.createdAt).toISOString() : undefined,
            modifiedTime: episode.updatedAt ? new Date(episode.updatedAt).toISOString() : undefined,
            section: 'Episodes',
            tags: episode.genres || series?.genres
        });
    }
    static generatePageMetadata(title, description, path, additionalKeywords = []) {
        const keywords = [
            'watch movies online',
            'free movies',
            'HD movies',
            'TV series online',
            'free episodes',
            'streaming platform',
            this.siteName,
            ...additionalKeywords
        ];
        return this.generateMetadata({
            title,
            description,
            keywords,
            canonical: path
        });
    }
}
}}),
"[project]/src/lib/schema.ts [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "SchemaGenerator": (()=>SchemaGenerator)
});
class SchemaGenerator {
    static baseUrl = process.env.NEXT_PUBLIC_BASE_URL || 'https://freemovieswatchnow.com';
    static siteName = 'freeMoviesWatchNow';
    static generateWebsiteSchema() {
        return {
            '@context': 'https://schema.org',
            '@type': 'WebSite',
            name: this.siteName,
            url: this.baseUrl,
            description: 'Watch free movies and TV series online in HD quality. Stream the latest movies, TV shows, and episodes with multiple streaming sources.',
            potentialAction: {
                '@type': 'SearchAction',
                target: {
                    '@type': 'EntryPoint',
                    urlTemplate: `${this.baseUrl}/search?q={search_term_string}`
                },
                'query-input': 'required name=search_term_string'
            },
            publisher: {
                '@type': 'Organization',
                name: this.siteName,
                url: this.baseUrl,
                logo: {
                    '@type': 'ImageObject',
                    url: `${this.baseUrl}/logo.png`
                }
            }
        };
    }
    static generateMovieSchema(movie) {
        const schema = {
            '@context': 'https://schema.org',
            '@type': 'Movie',
            name: movie.title,
            url: `${this.baseUrl}/watch/movie/${movie.imdbId}`,
            description: movie.description,
            image: movie.posterUrl,
            datePublished: movie.year?.toString(),
            genre: movie.genres,
            duration: movie.runtime,
            contentRating: movie.rating,
            aggregateRating: movie.imdbRating ? {
                '@type': 'AggregateRating',
                ratingValue: movie.imdbRating,
                ratingCount: movie.imdbVotes?.replace(/,/g, '') || '1000',
                bestRating: '10',
                worstRating: '1'
            } : undefined,
            director: movie.director ? {
                '@type': 'Person',
                name: movie.director
            } : undefined,
            actor: movie.cast?.slice(0, 5).map((actor)=>({
                    '@type': 'Person',
                    name: actor
                })),
            productionCompany: {
                '@type': 'Organization',
                name: movie.country || 'Unknown'
            },
            inLanguage: movie.language,
            keywords: [
                movie.title,
                `${movie.title} ${movie.year}`,
                'watch online',
                'free movie',
                'HD movie',
                ...movie.genres || []
            ].join(', '),
            offers: {
                '@type': 'Offer',
                price: '0',
                priceCurrency: 'USD',
                availability: 'https://schema.org/InStock',
                url: `${this.baseUrl}/watch/movie/${movie.imdbId}`
            },
            potentialAction: {
                '@type': 'WatchAction',
                target: {
                    '@type': 'EntryPoint',
                    urlTemplate: `${this.baseUrl}/watch/movie/${movie.imdbId}`,
                    actionPlatform: [
                        'https://schema.org/DesktopWebPlatform',
                        'https://schema.org/MobileWebPlatform'
                    ]
                },
                expectsAcceptanceOf: {
                    '@type': 'Offer',
                    price: '0',
                    priceCurrency: 'USD',
                    eligibleRegion: {
                        '@type': 'Country',
                        name: 'US'
                    }
                }
            }
        };
        // Remove undefined values
        return JSON.parse(JSON.stringify(schema));
    }
    static generateSeriesSchema(series) {
        const schema = {
            '@context': 'https://schema.org',
            '@type': 'TVSeries',
            name: series.title,
            url: `${this.baseUrl}/watch/series/${series.imdbId}`,
            description: series.description,
            image: series.posterUrl,
            startDate: series.startYear?.toString(),
            endDate: series.endYear?.toString(),
            numberOfSeasons: series.totalSeasons,
            numberOfEpisodes: series.totalEpisodes,
            genre: series.genres,
            contentRating: series.rating,
            aggregateRating: series.imdbRating ? {
                '@type': 'AggregateRating',
                ratingValue: series.imdbRating,
                ratingCount: series.imdbVotes?.replace(/,/g, '') || '1000',
                bestRating: '10',
                worstRating: '1'
            } : undefined,
            actor: series.cast?.slice(0, 5).map((actor)=>({
                    '@type': 'Person',
                    name: actor
                })),
            productionCompany: {
                '@type': 'Organization',
                name: series.country || 'Unknown'
            },
            inLanguage: series.language,
            keywords: [
                series.title,
                `${series.title} ${series.startYear}`,
                'watch online',
                'free series',
                'HD series',
                'TV show',
                ...series.genres || []
            ].join(', '),
            offers: {
                '@type': 'Offer',
                price: '0',
                priceCurrency: 'USD',
                availability: 'https://schema.org/InStock',
                url: `${this.baseUrl}/watch/series/${series.imdbId}`
            },
            potentialAction: {
                '@type': 'WatchAction',
                target: {
                    '@type': 'EntryPoint',
                    urlTemplate: `${this.baseUrl}/watch/series/${series.imdbId}`,
                    actionPlatform: [
                        'https://schema.org/DesktopWebPlatform',
                        'https://schema.org/MobileWebPlatform'
                    ]
                },
                expectsAcceptanceOf: {
                    '@type': 'Offer',
                    price: '0',
                    priceCurrency: 'USD',
                    eligibleRegion: {
                        '@type': 'Country',
                        name: 'US'
                    }
                }
            }
        };
        return JSON.parse(JSON.stringify(schema));
    }
    static generateEpisodeSchema(episode, series) {
        const episodeTitle = episode.episodeTitle || `Episode ${episode.episode}`;
        const schema = {
            '@context': 'https://schema.org',
            '@type': 'TVEpisode',
            name: episodeTitle,
            episodeNumber: episode.episode,
            seasonNumber: episode.season,
            url: `${this.baseUrl}/watch/series/${episode.imdbId}?season=${episode.season}&episode=${episode.episode}`,
            description: episode.description || `${episode.seriesTitle} Season ${episode.season} Episode ${episode.episode}`,
            image: series?.posterUrl,
            datePublished: episode.airDate ? new Date(episode.airDate).toISOString() : undefined,
            duration: episode.runtime,
            partOfSeries: {
                '@type': 'TVSeries',
                name: episode.seriesTitle,
                url: `${this.baseUrl}/watch/series/${episode.imdbId}`
            },
            partOfSeason: {
                '@type': 'TVSeason',
                seasonNumber: episode.season,
                partOfSeries: {
                    '@type': 'TVSeries',
                    name: episode.seriesTitle
                }
            },
            aggregateRating: episode.imdbRating ? {
                '@type': 'AggregateRating',
                ratingValue: episode.imdbRating,
                bestRating: '10',
                worstRating: '1'
            } : undefined,
            genre: episode.genres || series?.genres,
            inLanguage: series?.language,
            keywords: [
                episode.seriesTitle,
                episodeTitle,
                `S${episode.season}E${episode.episode}`,
                'watch online',
                'free episode',
                'HD episode',
                ...episode.genres || series?.genres || []
            ].join(', '),
            offers: {
                '@type': 'Offer',
                price: '0',
                priceCurrency: 'USD',
                availability: 'https://schema.org/InStock',
                url: `${this.baseUrl}/watch/series/${episode.imdbId}?season=${episode.season}&episode=${episode.episode}`
            },
            potentialAction: {
                '@type': 'WatchAction',
                target: {
                    '@type': 'EntryPoint',
                    urlTemplate: `${this.baseUrl}/watch/series/${episode.imdbId}?season=${episode.season}&episode=${episode.episode}`,
                    actionPlatform: [
                        'https://schema.org/DesktopWebPlatform',
                        'https://schema.org/MobileWebPlatform'
                    ]
                }
            }
        };
        return JSON.parse(JSON.stringify(schema));
    }
    static generateBreadcrumbSchema(items) {
        return {
            '@context': 'https://schema.org',
            '@type': 'BreadcrumbList',
            itemListElement: items.map((item, index)=>({
                    '@type': 'ListItem',
                    position: index + 1,
                    name: item.name,
                    item: `${this.baseUrl}${item.url}`
                }))
        };
    }
    static generateCollectionPageSchema(name, description, url, items) {
        return {
            '@context': 'https://schema.org',
            '@type': 'CollectionPage',
            name,
            description,
            url: `${this.baseUrl}${url}`,
            mainEntity: {
                '@type': 'ItemList',
                numberOfItems: items.length,
                itemListElement: items.map((item, index)=>({
                        '@type': 'ListItem',
                        position: index + 1,
                        url: `${this.baseUrl}${item.url}`,
                        name: item.name,
                        ...item.image && {
                            image: item.image
                        }
                    }))
            }
        };
    }
}
}}),
"[project]/src/app/watch/series/[id]/page.tsx [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>SeriesWatchPage),
    "generateMetadata": (()=>generateMetadata)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$api$2f$navigation$2e$react$2d$server$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/next/dist/api/navigation.react-server.js [app-rsc] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$client$2f$components$2f$navigation$2e$react$2d$server$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/client/components/navigation.react-server.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/api.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$VideoPlayer$2e$tsx__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/VideoPlayer.tsx [app-rsc] (ecmascript)");
(()=>{
    const e = new Error("Cannot find module '@/components/ContentInfo'");
    e.code = 'MODULE_NOT_FOUND';
    throw e;
})();
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$EpisodeSelector$2e$tsx__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/EpisodeSelector.tsx [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$vidsrc$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/vidsrc.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$seo$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/seo.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$schema$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/schema.ts [app-rsc] (ecmascript)");
;
;
;
;
;
;
;
;
;
async function getSeriesData(id, season) {
    try {
        const [series, episodes] = await Promise.all([
            __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["apiClient"].getSeriesById(id),
            __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["apiClient"].getSeriesEpisodes(id, season)
        ]);
        return {
            series,
            episodes
        };
    } catch (error) {
        // Only log error if it's not a 404 (which is expected for non-existent content)
        if (!error.message?.includes('404')) {
            console.error('Error fetching series data:', error);
        }
        return {
            series: null,
            episodes: []
        };
    }
}
async function generateMetadata({ params, searchParams }) {
    const { id } = await params;
    const { season, episode } = await searchParams;
    const selectedSeason = season ? parseInt(season) : 1;
    const selectedEpisode = episode ? parseInt(episode) : 1;
    const { series, episodes } = await getSeriesData(id, selectedSeason);
    if (!series) {
        return {
            title: 'Series Not Found | freeMoviesWatchNow',
            description: 'The requested series could not be found.'
        };
    }
    // If specific episode is requested, generate episode metadata
    if (season && episode) {
        const currentEpisode = episodes.find((ep)=>ep.season === selectedSeason && ep.episode === selectedEpisode);
        if (currentEpisode) {
            return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$seo$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["SEOGenerator"].generateEpisodeMetadata(currentEpisode, series);
        }
    }
    // Otherwise generate series metadata
    return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$seo$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["SEOGenerator"].generateSeriesMetadata(series);
}
async function SeriesWatchPage({ params, searchParams }) {
    const { id } = await params;
    const { season, episode } = await searchParams;
    const selectedSeason = season ? parseInt(season) : 1;
    const selectedEpisode = episode ? parseInt(episode) : 1;
    const { series, episodes } = await getSeriesData(id, selectedSeason);
    if (!series) {
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$client$2f$components$2f$navigation$2e$react$2d$server$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["notFound"])();
    }
    // Find the current episode
    const currentEpisode = episodes.find((ep)=>ep.season === selectedSeason && ep.episode === selectedEpisode);
    const contentInfo = {
        title: series.title,
        year: series.startYear,
        rating: series.rating,
        imdbRating: series.imdbRating,
        description: series.description,
        genres: series.genres,
        creator: series.creator,
        cast: series.cast,
        language: series.language,
        country: series.country,
        posterUrl: series.posterUrl,
        type: 'series',
        totalSeasons: series.totalSeasons,
        status: series.status
    };
    // Generate all streaming sources for the current episode
    const vidsrc = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$vidsrc$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["default"].getInstance();
    const streamingSources = vidsrc.generateAllEpisodeEmbedUrls(id, selectedSeason, selectedEpisode, series.tmdbId);
    // Generate structured data
    const isEpisodeView = season && episode && currentEpisode;
    const schema = isEpisodeView ? __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$schema$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["SchemaGenerator"].generateEpisodeSchema(currentEpisode, series) : __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$schema$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["SchemaGenerator"].generateSeriesSchema(series);
    const breadcrumbSchema = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$schema$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["SchemaGenerator"].generateBreadcrumbSchema([
        {
            name: 'Home',
            url: '/'
        },
        {
            name: 'TV Series',
            url: '/series'
        },
        {
            name: series.title,
            url: `/watch/series/${series.imdbId}`
        },
        ...isEpisodeView ? [
            {
                name: `S${selectedSeason}E${selectedEpisode}`,
                url: `/watch/series/${series.imdbId}?season=${selectedSeason}&episode=${selectedEpisode}`
            }
        ] : []
    ]);
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        className: "min-h-screen bg-black",
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("script", {
                type: "application/ld+json",
                dangerouslySetInnerHTML: {
                    __html: JSON.stringify([
                        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$schema$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["SchemaGenerator"].generateWebsiteSchema(),
                        schema,
                        breadcrumbSchema
                    ])
                }
            }, void 0, false, {
                fileName: "[project]/src/app/watch/series/[id]/page.tsx",
                lineNumber: 124,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$VideoPlayer$2e$tsx__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["default"], {
                streamingSources: streamingSources,
                title: `${series.title} - S${selectedSeason}E${selectedEpisode}`,
                type: "series"
            }, void 0, false, {
                fileName: "[project]/src/app/watch/series/[id]/page.tsx",
                lineNumber: 134,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "max-w-[2560px] mx-auto px-8 lg:px-24 py-12",
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "grid grid-cols-1 xl:grid-cols-5 gap-16",
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "xl:col-span-3",
                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])(ContentInfo, {
                                    content: contentInfo
                                }, void 0, false, {
                                    fileName: "[project]/src/app/watch/series/[id]/page.tsx",
                                    lineNumber: 144,
                                    columnNumber: 13
                                }, this)
                            }, void 0, false, {
                                fileName: "[project]/src/app/watch/series/[id]/page.tsx",
                                lineNumber: 143,
                                columnNumber: 11
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "xl:col-span-2",
                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$EpisodeSelector$2e$tsx__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["default"], {
                                    seriesId: id,
                                    episodes: episodes,
                                    currentSeason: selectedSeason,
                                    currentEpisode: selectedEpisode
                                }, void 0, false, {
                                    fileName: "[project]/src/app/watch/series/[id]/page.tsx",
                                    lineNumber: 149,
                                    columnNumber: 13
                                }, this)
                            }, void 0, false, {
                                fileName: "[project]/src/app/watch/series/[id]/page.tsx",
                                lineNumber: 148,
                                columnNumber: 11
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/app/watch/series/[id]/page.tsx",
                        lineNumber: 141,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "mt-16 max-w-4xl mx-auto",
                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("article", {
                            className: "prose prose-invert prose-lg max-w-none",
                            children: [
                                isEpisodeView && currentEpisode ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["Fragment"], {
                                    children: [
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("h2", {
                                            className: "text-3xl font-bold text-white mb-6",
                                            children: [
                                                series.title,
                                                " Season ",
                                                selectedSeason,
                                                " Episode ",
                                                selectedEpisode,
                                                currentEpisode.episodeTitle && ` - ${currentEpisode.episodeTitle}`
                                            ]
                                        }, void 0, true, {
                                            fileName: "[project]/src/app/watch/series/[id]/page.tsx",
                                            lineNumber: 163,
                                            columnNumber: 17
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                            className: "text-gray-300 leading-relaxed space-y-4",
                                            children: [
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                                    children: [
                                                        "Watch ",
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("strong", {
                                                            children: series.title
                                                        }, void 0, false, {
                                                            fileName: "[project]/src/app/watch/series/[id]/page.tsx",
                                                            lineNumber: 170,
                                                            columnNumber: 27
                                                        }, this),
                                                        " Season ",
                                                        selectedSeason,
                                                        " Episode ",
                                                        selectedEpisode,
                                                        currentEpisode.episodeTitle && ` "${currentEpisode.episodeTitle}"`,
                                                        " online free in HD quality.",
                                                        currentEpisode.description && ` ${currentEpisode.description}`
                                                    ]
                                                }, void 0, true, {
                                                    fileName: "[project]/src/app/watch/series/[id]/page.tsx",
                                                    lineNumber: 169,
                                                    columnNumber: 19
                                                }, this),
                                                currentEpisode.airDate && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                                    children: [
                                                        "This episode originally aired on ",
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("strong", {
                                                            children: new Date(currentEpisode.airDate).toLocaleDateString()
                                                        }, void 0, false, {
                                                            fileName: "[project]/src/app/watch/series/[id]/page.tsx",
                                                            lineNumber: 177,
                                                            columnNumber: 56
                                                        }, this),
                                                        "."
                                                    ]
                                                }, void 0, true, {
                                                    fileName: "[project]/src/app/watch/series/[id]/page.tsx",
                                                    lineNumber: 176,
                                                    columnNumber: 21
                                                }, this),
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                                    children: [
                                                        "Continue following the story of ",
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("strong", {
                                                            children: series.title
                                                        }, void 0, false, {
                                                            fileName: "[project]/src/app/watch/series/[id]/page.tsx",
                                                            lineNumber: 182,
                                                            columnNumber: 53
                                                        }, this),
                                                        ", a captivating ",
                                                        series.genres?.join(', ').toLowerCase(),
                                                        " series that has been entertaining audiences since ",
                                                        series.startYear,
                                                        "."
                                                    ]
                                                }, void 0, true, {
                                                    fileName: "[project]/src/app/watch/series/[id]/page.tsx",
                                                    lineNumber: 181,
                                                    columnNumber: 19
                                                }, this)
                                            ]
                                        }, void 0, true, {
                                            fileName: "[project]/src/app/watch/series/[id]/page.tsx",
                                            lineNumber: 168,
                                            columnNumber: 17
                                        }, this)
                                    ]
                                }, void 0, true) : /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["Fragment"], {
                                    children: [
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("h2", {
                                            className: "text-3xl font-bold text-white mb-6",
                                            children: [
                                                "About ",
                                                series.title,
                                                " (",
                                                series.startYear,
                                                series.endYear ? `-${series.endYear}` : '',
                                                ")"
                                            ]
                                        }, void 0, true, {
                                            fileName: "[project]/src/app/watch/series/[id]/page.tsx",
                                            lineNumber: 189,
                                            columnNumber: 17
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                            className: "text-gray-300 leading-relaxed space-y-4",
                                            children: [
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                                    children: [
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("strong", {
                                                            children: series.title
                                                        }, void 0, false, {
                                                            fileName: "[project]/src/app/watch/series/[id]/page.tsx",
                                                            lineNumber: 195,
                                                            columnNumber: 21
                                                        }, this),
                                                        " is a ",
                                                        series.genres?.join(', ').toLowerCase(),
                                                        " series that premiered in ",
                                                        series.startYear,
                                                        ".",
                                                        series.description && ` ${series.description}`
                                                    ]
                                                }, void 0, true, {
                                                    fileName: "[project]/src/app/watch/series/[id]/page.tsx",
                                                    lineNumber: 194,
                                                    columnNumber: 19
                                                }, this),
                                                series.creator && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                                    children: [
                                                        "Created by ",
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("strong", {
                                                            children: series.creator
                                                        }, void 0, false, {
                                                            fileName: "[project]/src/app/watch/series/[id]/page.tsx",
                                                            lineNumber: 201,
                                                            columnNumber: 34
                                                        }, this),
                                                        ", this series has captivated audiences with its compelling storylines and character development."
                                                    ]
                                                }, void 0, true, {
                                                    fileName: "[project]/src/app/watch/series/[id]/page.tsx",
                                                    lineNumber: 200,
                                                    columnNumber: 21
                                                }, this),
                                                series.cast && series.cast.length > 0 && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                                    children: [
                                                        "The series features an exceptional cast including ",
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("strong", {
                                                            children: series.cast.slice(0, 5).join(', ')
                                                        }, void 0, false, {
                                                            fileName: "[project]/src/app/watch/series/[id]/page.tsx",
                                                            lineNumber: 207,
                                                            columnNumber: 73
                                                        }, this),
                                                        series.cast.length > 5 && ' and many more talented actors',
                                                        "."
                                                    ]
                                                }, void 0, true, {
                                                    fileName: "[project]/src/app/watch/series/[id]/page.tsx",
                                                    lineNumber: 206,
                                                    columnNumber: 21
                                                }, this),
                                                series.totalSeasons && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                                    children: [
                                                        "With ",
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("strong", {
                                                            children: [
                                                                series.totalSeasons,
                                                                " season",
                                                                series.totalSeasons > 1 ? 's' : ''
                                                            ]
                                                        }, void 0, true, {
                                                            fileName: "[project]/src/app/watch/series/[id]/page.tsx",
                                                            lineNumber: 214,
                                                            columnNumber: 28
                                                        }, this),
                                                        " available,",
                                                        series.title,
                                                        " offers hours of premium entertainment for binge-watching."
                                                    ]
                                                }, void 0, true, {
                                                    fileName: "[project]/src/app/watch/series/[id]/page.tsx",
                                                    lineNumber: 213,
                                                    columnNumber: 21
                                                }, this),
                                                series.imdbRating && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                                    children: [
                                                        "Rated ",
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("strong", {
                                                            children: [
                                                                series.imdbRating,
                                                                "/10"
                                                            ]
                                                        }, void 0, true, {
                                                            fileName: "[project]/src/app/watch/series/[id]/page.tsx",
                                                            lineNumber: 221,
                                                            columnNumber: 29
                                                        }, this),
                                                        " on IMDb, ",
                                                        series.title,
                                                        " has received",
                                                        series.imdbRating >= 8 ? ' critical acclaim' : series.imdbRating >= 7 ? ' positive reviews' : ' mixed reviews',
                                                        "from viewers worldwide."
                                                    ]
                                                }, void 0, true, {
                                                    fileName: "[project]/src/app/watch/series/[id]/page.tsx",
                                                    lineNumber: 220,
                                                    columnNumber: 21
                                                }, this)
                                            ]
                                        }, void 0, true, {
                                            fileName: "[project]/src/app/watch/series/[id]/page.tsx",
                                            lineNumber: 193,
                                            columnNumber: 17
                                        }, this)
                                    ]
                                }, void 0, true),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                    className: "text-gray-300 leading-relaxed mt-6",
                                    children: [
                                        "Watch ",
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("strong", {
                                            children: series.title
                                        }, void 0, false, {
                                            fileName: "[project]/src/app/watch/series/[id]/page.tsx",
                                            lineNumber: 231,
                                            columnNumber: 21
                                        }, this),
                                        " online free in HD quality on freeMoviesWatchNow. Our platform provides multiple streaming sources and all episodes for the ultimate binge-watching experience."
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/src/app/watch/series/[id]/page.tsx",
                                    lineNumber: 230,
                                    columnNumber: 13
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: "mt-8 p-6 bg-gray-900/50 rounded-lg border border-gray-800",
                                    children: [
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("h3", {
                                            className: "text-xl font-semibold text-white mb-4",
                                            children: "Why Choose freeMoviesWatchNow for TV Series?"
                                        }, void 0, false, {
                                            fileName: "[project]/src/app/watch/series/[id]/page.tsx",
                                            lineNumber: 236,
                                            columnNumber: 15
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("ul", {
                                            className: "text-gray-300 space-y-2",
                                            children: [
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("li", {
                                                    children: "• All episodes available in HD quality"
                                                }, void 0, false, {
                                                    fileName: "[project]/src/app/watch/series/[id]/page.tsx",
                                                    lineNumber: 238,
                                                    columnNumber: 17
                                                }, this),
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("li", {
                                                    children: "• Multiple streaming sources for reliability"
                                                }, void 0, false, {
                                                    fileName: "[project]/src/app/watch/series/[id]/page.tsx",
                                                    lineNumber: 239,
                                                    columnNumber: 17
                                                }, this),
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("li", {
                                                    children: "• Easy episode navigation and season selection"
                                                }, void 0, false, {
                                                    fileName: "[project]/src/app/watch/series/[id]/page.tsx",
                                                    lineNumber: 240,
                                                    columnNumber: 17
                                                }, this),
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("li", {
                                                    children: "• No ads interrupting your viewing experience"
                                                }, void 0, false, {
                                                    fileName: "[project]/src/app/watch/series/[id]/page.tsx",
                                                    lineNumber: 241,
                                                    columnNumber: 17
                                                }, this),
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("li", {
                                                    children: "• Compatible with all devices and browsers"
                                                }, void 0, false, {
                                                    fileName: "[project]/src/app/watch/series/[id]/page.tsx",
                                                    lineNumber: 242,
                                                    columnNumber: 17
                                                }, this),
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("li", {
                                                    children: "• Latest episodes added as soon as they air"
                                                }, void 0, false, {
                                                    fileName: "[project]/src/app/watch/series/[id]/page.tsx",
                                                    lineNumber: 243,
                                                    columnNumber: 17
                                                }, this)
                                            ]
                                        }, void 0, true, {
                                            fileName: "[project]/src/app/watch/series/[id]/page.tsx",
                                            lineNumber: 237,
                                            columnNumber: 15
                                        }, this)
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/src/app/watch/series/[id]/page.tsx",
                                    lineNumber: 235,
                                    columnNumber: 13
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "[project]/src/app/watch/series/[id]/page.tsx",
                            lineNumber: 160,
                            columnNumber: 11
                        }, this)
                    }, void 0, false, {
                        fileName: "[project]/src/app/watch/series/[id]/page.tsx",
                        lineNumber: 159,
                        columnNumber: 9
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/src/app/watch/series/[id]/page.tsx",
                lineNumber: 140,
                columnNumber: 7
            }, this)
        ]
    }, void 0, true, {
        fileName: "[project]/src/app/watch/series/[id]/page.tsx",
        lineNumber: 122,
        columnNumber: 5
    }, this);
}
}}),
"[project]/src/app/watch/series/[id]/page.tsx [app-rsc] (ecmascript, Next.js server component)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.n(__turbopack_context__.i("[project]/src/app/watch/series/[id]/page.tsx [app-rsc] (ecmascript)"));
}}),

};

//# sourceMappingURL=%5Broot-of-the-server%5D__50594b9f._.js.map