{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/New%20folder%20%2825%29/streamzen/node_modules/iconv-lite/lib/bom-handling.js"], "sourcesContent": ["\"use strict\";\n\nvar BOMChar = '\\uFEFF';\n\nexports.PrependBOM = PrependBOMWrapper\nfunction PrependBOMWrapper(encoder, options) {\n    this.encoder = encoder;\n    this.addBOM = true;\n}\n\nPrependBOMWrapper.prototype.write = function(str) {\n    if (this.addBOM) {\n        str = BOMChar + str;\n        this.addBOM = false;\n    }\n\n    return this.encoder.write(str);\n}\n\nPrependBOMWrapper.prototype.end = function() {\n    return this.encoder.end();\n}\n\n\n//------------------------------------------------------------------------------\n\nexports.StripBOM = StripBOMWrapper;\nfunction StripBOMWrapper(decoder, options) {\n    this.decoder = decoder;\n    this.pass = false;\n    this.options = options || {};\n}\n\nStripBOMWrapper.prototype.write = function(buf) {\n    var res = this.decoder.write(buf);\n    if (this.pass || !res)\n        return res;\n\n    if (res[0] === BOMChar) {\n        res = res.slice(1);\n        if (typeof this.options.stripBOM === 'function')\n            this.options.stripBOM();\n    }\n\n    this.pass = true;\n    return res;\n}\n\nStripBOMWrapper.prototype.end = function() {\n    return this.decoder.end();\n}\n\n"], "names": [], "mappings": "AAAA;AAEA,IAAI,UAAU;AAEd,QAAQ,UAAU,GAAG;AACrB,SAAS,kBAAkB,OAAO,EAAE,OAAO;IACvC,IAAI,CAAC,OAAO,GAAG;IACf,IAAI,CAAC,MAAM,GAAG;AAClB;AAEA,kBAAkB,SAAS,CAAC,KAAK,GAAG,SAAS,GAAG;IAC5C,IAAI,IAAI,CAAC,MAAM,EAAE;QACb,MAAM,UAAU;QAChB,IAAI,CAAC,MAAM,GAAG;IAClB;IAEA,OAAO,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC;AAC9B;AAEA,kBAAkB,SAAS,CAAC,GAAG,GAAG;IAC9B,OAAO,IAAI,CAAC,OAAO,CAAC,GAAG;AAC3B;AAGA,gFAAgF;AAEhF,QAAQ,QAAQ,GAAG;AACnB,SAAS,gBAAgB,OAAO,EAAE,OAAO;IACrC,IAAI,CAAC,OAAO,GAAG;IACf,IAAI,CAAC,IAAI,GAAG;IACZ,IAAI,CAAC,OAAO,GAAG,WAAW,CAAC;AAC/B;AAEA,gBAAgB,SAAS,CAAC,KAAK,GAAG,SAAS,GAAG;IAC1C,IAAI,MAAM,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC;IAC7B,IAAI,IAAI,CAAC,IAAI,IAAI,CAAC,KACd,OAAO;IAEX,IAAI,GAAG,CAAC,EAAE,KAAK,SAAS;QACpB,MAAM,IAAI,KAAK,CAAC;QAChB,IAAI,OAAO,IAAI,CAAC,OAAO,CAAC,QAAQ,KAAK,YACjC,IAAI,CAAC,OAAO,CAAC,QAAQ;IAC7B;IAEA,IAAI,CAAC,IAAI,GAAG;IACZ,OAAO;AACX;AAEA,gBAAgB,SAAS,CAAC,GAAG,GAAG;IAC5B,OAAO,IAAI,CAAC,OAAO,CAAC,GAAG;AAC3B", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 48, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/New%20folder%20%2825%29/streamzen/node_modules/iconv-lite/encodings/internal.js"], "sourcesContent": ["\"use strict\";\nvar Buffer = require(\"safer-buffer\").Buffer;\n\n// Export Node.js internal encodings.\n\nmodule.exports = {\n    // Encodings\n    utf8:   { type: \"_internal\", bomAware: true},\n    cesu8:  { type: \"_internal\", bomAware: true},\n    unicode11utf8: \"utf8\",\n\n    ucs2:   { type: \"_internal\", bomAware: true},\n    utf16le: \"ucs2\",\n\n    binary: { type: \"_internal\" },\n    base64: { type: \"_internal\" },\n    hex:    { type: \"_internal\" },\n\n    // Codec.\n    _internal: InternalCodec,\n};\n\n//------------------------------------------------------------------------------\n\nfunction InternalCodec(codecOptions, iconv) {\n    this.enc = codecOptions.encodingName;\n    this.bomAware = codecOptions.bomAware;\n\n    if (this.enc === \"base64\")\n        this.encoder = InternalEncoderBase64;\n    else if (this.enc === \"cesu8\") {\n        this.enc = \"utf8\"; // Use utf8 for decoding.\n        this.encoder = InternalEncoderCesu8;\n\n        // Add decoder for versions of Node not supporting CESU-8\n        if (Buffer.from('eda0bdedb2a9', 'hex').toString() !== '💩') {\n            this.decoder = InternalDecoderCesu8;\n            this.defaultCharUnicode = iconv.defaultCharUnicode;\n        }\n    }\n}\n\nInternalCodec.prototype.encoder = InternalEncoder;\nInternalCodec.prototype.decoder = InternalDecoder;\n\n//------------------------------------------------------------------------------\n\n// We use node.js internal decoder. Its signature is the same as ours.\nvar StringDecoder = require('string_decoder').StringDecoder;\n\nif (!StringDecoder.prototype.end) // Node v0.8 doesn't have this method.\n    StringDecoder.prototype.end = function() {};\n\n\nfunction InternalDecoder(options, codec) {\n    this.decoder = new StringDecoder(codec.enc);\n}\n\nInternalDecoder.prototype.write = function(buf) {\n    if (!Buffer.isBuffer(buf)) {\n        buf = Buffer.from(buf);\n    }\n\n    return this.decoder.write(buf);\n}\n\nInternalDecoder.prototype.end = function() {\n    return this.decoder.end();\n}\n\n\n//------------------------------------------------------------------------------\n// Encoder is mostly trivial\n\nfunction InternalEncoder(options, codec) {\n    this.enc = codec.enc;\n}\n\nInternalEncoder.prototype.write = function(str) {\n    return Buffer.from(str, this.enc);\n}\n\nInternalEncoder.prototype.end = function() {\n}\n\n\n//------------------------------------------------------------------------------\n// Except base64 encoder, which must keep its state.\n\nfunction InternalEncoderBase64(options, codec) {\n    this.prevStr = '';\n}\n\nInternalEncoderBase64.prototype.write = function(str) {\n    str = this.prevStr + str;\n    var completeQuads = str.length - (str.length % 4);\n    this.prevStr = str.slice(completeQuads);\n    str = str.slice(0, completeQuads);\n\n    return Buffer.from(str, \"base64\");\n}\n\nInternalEncoderBase64.prototype.end = function() {\n    return Buffer.from(this.prevStr, \"base64\");\n}\n\n\n//------------------------------------------------------------------------------\n// CESU-8 encoder is also special.\n\nfunction InternalEncoderCesu8(options, codec) {\n}\n\nInternalEncoderCesu8.prototype.write = function(str) {\n    var buf = Buffer.alloc(str.length * 3), bufIdx = 0;\n    for (var i = 0; i < str.length; i++) {\n        var charCode = str.charCodeAt(i);\n        // Naive implementation, but it works because CESU-8 is especially easy\n        // to convert from UTF-16 (which all JS strings are encoded in).\n        if (charCode < 0x80)\n            buf[bufIdx++] = charCode;\n        else if (charCode < 0x800) {\n            buf[bufIdx++] = 0xC0 + (charCode >>> 6);\n            buf[bufIdx++] = 0x80 + (charCode & 0x3f);\n        }\n        else { // charCode will always be < 0x10000 in javascript.\n            buf[bufIdx++] = 0xE0 + (charCode >>> 12);\n            buf[bufIdx++] = 0x80 + ((charCode >>> 6) & 0x3f);\n            buf[bufIdx++] = 0x80 + (charCode & 0x3f);\n        }\n    }\n    return buf.slice(0, bufIdx);\n}\n\nInternalEncoderCesu8.prototype.end = function() {\n}\n\n//------------------------------------------------------------------------------\n// CESU-8 decoder is not implemented in Node v4.0+\n\nfunction InternalDecoderCesu8(options, codec) {\n    this.acc = 0;\n    this.contBytes = 0;\n    this.accBytes = 0;\n    this.defaultCharUnicode = codec.defaultCharUnicode;\n}\n\nInternalDecoderCesu8.prototype.write = function(buf) {\n    var acc = this.acc, contBytes = this.contBytes, accBytes = this.accBytes, \n        res = '';\n    for (var i = 0; i < buf.length; i++) {\n        var curByte = buf[i];\n        if ((curByte & 0xC0) !== 0x80) { // Leading byte\n            if (contBytes > 0) { // Previous code is invalid\n                res += this.defaultCharUnicode;\n                contBytes = 0;\n            }\n\n            if (curByte < 0x80) { // Single-byte code\n                res += String.fromCharCode(curByte);\n            } else if (curByte < 0xE0) { // Two-byte code\n                acc = curByte & 0x1F;\n                contBytes = 1; accBytes = 1;\n            } else if (curByte < 0xF0) { // Three-byte code\n                acc = curByte & 0x0F;\n                contBytes = 2; accBytes = 1;\n            } else { // Four or more are not supported for CESU-8.\n                res += this.defaultCharUnicode;\n            }\n        } else { // Continuation byte\n            if (contBytes > 0) { // We're waiting for it.\n                acc = (acc << 6) | (curByte & 0x3f);\n                contBytes--; accBytes++;\n                if (contBytes === 0) {\n                    // Check for overlong encoding, but support Modified UTF-8 (encoding NULL as C0 80)\n                    if (accBytes === 2 && acc < 0x80 && acc > 0)\n                        res += this.defaultCharUnicode;\n                    else if (accBytes === 3 && acc < 0x800)\n                        res += this.defaultCharUnicode;\n                    else\n                        // Actually add character.\n                        res += String.fromCharCode(acc);\n                }\n            } else { // Unexpected continuation byte\n                res += this.defaultCharUnicode;\n            }\n        }\n    }\n    this.acc = acc; this.contBytes = contBytes; this.accBytes = accBytes;\n    return res;\n}\n\nInternalDecoderCesu8.prototype.end = function() {\n    var res = 0;\n    if (this.contBytes > 0)\n        res += this.defaultCharUnicode;\n    return res;\n}\n"], "names": [], "mappings": "AAAA;AACA,IAAI,SAAS,+FAAwB,MAAM;AAE3C,qCAAqC;AAErC,OAAO,OAAO,GAAG;IACb,YAAY;IACZ,MAAQ;QAAE,MAAM;QAAa,UAAU;IAAI;IAC3C,OAAQ;QAAE,MAAM;QAAa,UAAU;IAAI;IAC3C,eAAe;IAEf,MAAQ;QAAE,MAAM;QAAa,UAAU;IAAI;IAC3C,SAAS;IAET,QAAQ;QAAE,MAAM;IAAY;IAC5B,QAAQ;QAAE,MAAM;IAAY;IAC5B,KAAQ;QAAE,MAAM;IAAY;IAE5B,SAAS;IACT,WAAW;AACf;AAEA,gFAAgF;AAEhF,SAAS,cAAc,YAAY,EAAE,KAAK;IACtC,IAAI,CAAC,GAAG,GAAG,aAAa,YAAY;IACpC,IAAI,CAAC,QAAQ,GAAG,aAAa,QAAQ;IAErC,IAAI,IAAI,CAAC,GAAG,KAAK,UACb,IAAI,CAAC,OAAO,GAAG;SACd,IAAI,IAAI,CAAC,GAAG,KAAK,SAAS;QAC3B,IAAI,CAAC,GAAG,GAAG,QAAQ,yBAAyB;QAC5C,IAAI,CAAC,OAAO,GAAG;QAEf,yDAAyD;QACzD,IAAI,OAAO,IAAI,CAAC,gBAAgB,OAAO,QAAQ,OAAO,MAAM;YACxD,IAAI,CAAC,OAAO,GAAG;YACf,IAAI,CAAC,kBAAkB,GAAG,MAAM,kBAAkB;QACtD;IACJ;AACJ;AAEA,cAAc,SAAS,CAAC,OAAO,GAAG;AAClC,cAAc,SAAS,CAAC,OAAO,GAAG;AAElC,gFAAgF;AAEhF,sEAAsE;AACtE,IAAI,gBAAgB,uFAA0B,aAAa;AAE3D,IAAI,CAAC,cAAc,SAAS,CAAC,GAAG,EAC5B,cAAc,SAAS,CAAC,GAAG,GAAG,YAAY;AAG9C,SAAS,gBAAgB,OAAO,EAAE,KAAK;IACnC,IAAI,CAAC,OAAO,GAAG,IAAI,cAAc,MAAM,GAAG;AAC9C;AAEA,gBAAgB,SAAS,CAAC,KAAK,GAAG,SAAS,GAAG;IAC1C,IAAI,CAAC,OAAO,QAAQ,CAAC,MAAM;QACvB,MAAM,OAAO,IAAI,CAAC;IACtB;IAEA,OAAO,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC;AAC9B;AAEA,gBAAgB,SAAS,CAAC,GAAG,GAAG;IAC5B,OAAO,IAAI,CAAC,OAAO,CAAC,GAAG;AAC3B;AAGA,gFAAgF;AAChF,4BAA4B;AAE5B,SAAS,gBAAgB,OAAO,EAAE,KAAK;IACnC,IAAI,CAAC,GAAG,GAAG,MAAM,GAAG;AACxB;AAEA,gBAAgB,SAAS,CAAC,KAAK,GAAG,SAAS,GAAG;IAC1C,OAAO,OAAO,IAAI,CAAC,KAAK,IAAI,CAAC,GAAG;AACpC;AAEA,gBAAgB,SAAS,CAAC,GAAG,GAAG,YAChC;AAGA,gFAAgF;AAChF,oDAAoD;AAEpD,SAAS,sBAAsB,OAAO,EAAE,KAAK;IACzC,IAAI,CAAC,OAAO,GAAG;AACnB;AAEA,sBAAsB,SAAS,CAAC,KAAK,GAAG,SAAS,GAAG;IAChD,MAAM,IAAI,CAAC,OAAO,GAAG;IACrB,IAAI,gBAAgB,IAAI,MAAM,GAAI,IAAI,MAAM,GAAG;IAC/C,IAAI,CAAC,OAAO,GAAG,IAAI,KAAK,CAAC;IACzB,MAAM,IAAI,KAAK,CAAC,GAAG;IAEnB,OAAO,OAAO,IAAI,CAAC,KAAK;AAC5B;AAEA,sBAAsB,SAAS,CAAC,GAAG,GAAG;IAClC,OAAO,OAAO,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE;AACrC;AAGA,gFAAgF;AAChF,kCAAkC;AAElC,SAAS,qBAAqB,OAAO,EAAE,KAAK,GAC5C;AAEA,qBAAqB,SAAS,CAAC,KAAK,GAAG,SAAS,GAAG;IAC/C,IAAI,MAAM,OAAO,KAAK,CAAC,IAAI,MAAM,GAAG,IAAI,SAAS;IACjD,IAAK,IAAI,IAAI,GAAG,IAAI,IAAI,MAAM,EAAE,IAAK;QACjC,IAAI,WAAW,IAAI,UAAU,CAAC;QAC9B,uEAAuE;QACvE,gEAAgE;QAChE,IAAI,WAAW,MACX,GAAG,CAAC,SAAS,GAAG;aACf,IAAI,WAAW,OAAO;YACvB,GAAG,CAAC,SAAS,GAAG,OAAO,CAAC,aAAa,CAAC;YACtC,GAAG,CAAC,SAAS,GAAG,OAAO,CAAC,WAAW,IAAI;QAC3C,OACK;YACD,GAAG,CAAC,SAAS,GAAG,OAAO,CAAC,aAAa,EAAE;YACvC,GAAG,CAAC,SAAS,GAAG,OAAO,CAAC,AAAC,aAAa,IAAK,IAAI;YAC/C,GAAG,CAAC,SAAS,GAAG,OAAO,CAAC,WAAW,IAAI;QAC3C;IACJ;IACA,OAAO,IAAI,KAAK,CAAC,GAAG;AACxB;AAEA,qBAAqB,SAAS,CAAC,GAAG,GAAG,YACrC;AAEA,gFAAgF;AAChF,kDAAkD;AAElD,SAAS,qBAAqB,OAAO,EAAE,KAAK;IACxC,IAAI,CAAC,GAAG,GAAG;IACX,IAAI,CAAC,SAAS,GAAG;IACjB,IAAI,CAAC,QAAQ,GAAG;IAChB,IAAI,CAAC,kBAAkB,GAAG,MAAM,kBAAkB;AACtD;AAEA,qBAAqB,SAAS,CAAC,KAAK,GAAG,SAAS,GAAG;IAC/C,IAAI,MAAM,IAAI,CAAC,GAAG,EAAE,YAAY,IAAI,CAAC,SAAS,EAAE,WAAW,IAAI,CAAC,QAAQ,EACpE,MAAM;IACV,IAAK,IAAI,IAAI,GAAG,IAAI,IAAI,MAAM,EAAE,IAAK;QACjC,IAAI,UAAU,GAAG,CAAC,EAAE;QACpB,IAAI,CAAC,UAAU,IAAI,MAAM,MAAM;YAC3B,IAAI,YAAY,GAAG;gBACf,OAAO,IAAI,CAAC,kBAAkB;gBAC9B,YAAY;YAChB;YAEA,IAAI,UAAU,MAAM;gBAChB,OAAO,OAAO,YAAY,CAAC;YAC/B,OAAO,IAAI,UAAU,MAAM;gBACvB,MAAM,UAAU;gBAChB,YAAY;gBAAG,WAAW;YAC9B,OAAO,IAAI,UAAU,MAAM;gBACvB,MAAM,UAAU;gBAChB,YAAY;gBAAG,WAAW;YAC9B,OAAO;gBACH,OAAO,IAAI,CAAC,kBAAkB;YAClC;QACJ,OAAO;YACH,IAAI,YAAY,GAAG;gBACf,MAAM,AAAC,OAAO,IAAM,UAAU;gBAC9B;gBAAa;gBACb,IAAI,cAAc,GAAG;oBACjB,mFAAmF;oBACnF,IAAI,aAAa,KAAK,MAAM,QAAQ,MAAM,GACtC,OAAO,IAAI,CAAC,kBAAkB;yBAC7B,IAAI,aAAa,KAAK,MAAM,OAC7B,OAAO,IAAI,CAAC,kBAAkB;yBAE9B,0BAA0B;oBAC1B,OAAO,OAAO,YAAY,CAAC;gBACnC;YACJ,OAAO;gBACH,OAAO,IAAI,CAAC,kBAAkB;YAClC;QACJ;IACJ;IACA,IAAI,CAAC,GAAG,GAAG;IAAK,IAAI,CAAC,SAAS,GAAG;IAAW,IAAI,CAAC,QAAQ,GAAG;IAC5D,OAAO;AACX;AAEA,qBAAqB,SAAS,CAAC,GAAG,GAAG;IACjC,IAAI,MAAM;IACV,IAAI,IAAI,CAAC,SAAS,GAAG,GACjB,OAAO,IAAI,CAAC,kBAAkB;IAClC,OAAO;AACX", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 220, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/New%20folder%20%2825%29/streamzen/node_modules/iconv-lite/encodings/utf32.js"], "sourcesContent": ["'use strict';\n\nvar Buffer = require('safer-buffer').Buffer;\n\n// == UTF32-LE/BE codec. ==========================================================\n\nexports._utf32 = Utf32Codec;\n\nfunction Utf32Codec(codecOptions, iconv) {\n    this.iconv = iconv;\n    this.bomAware = true;\n    this.isLE = codecOptions.isLE;\n}\n\nexports.utf32le = { type: '_utf32', isLE: true };\nexports.utf32be = { type: '_utf32', isLE: false };\n\n// Aliases\nexports.ucs4le = 'utf32le';\nexports.ucs4be = 'utf32be';\n\nUtf32Codec.prototype.encoder = Utf32Encoder;\nUtf32Codec.prototype.decoder = Utf32Decoder;\n\n// -- Encoding\n\nfunction Utf32Encoder(options, codec) {\n    this.isLE = codec.isLE;\n    this.highSurrogate = 0;\n}\n\nUtf32Encoder.prototype.write = function(str) {\n    var src = Buffer.from(str, 'ucs2');\n    var dst = Buffer.alloc(src.length * 2);\n    var write32 = this.isLE ? dst.writeUInt32LE : dst.writeUInt32BE;\n    var offset = 0;\n\n    for (var i = 0; i < src.length; i += 2) {\n        var code = src.readUInt16LE(i);\n        var isHighSurrogate = (0xD800 <= code && code < 0xDC00);\n        var isLowSurrogate = (0xDC00 <= code && code < 0xE000);\n\n        if (this.highSurrogate) {\n            if (isHighSurrogate || !isLowSurrogate) {\n                // There shouldn't be two high surrogates in a row, nor a high surrogate which isn't followed by a low\n                // surrogate. If this happens, keep the pending high surrogate as a stand-alone semi-invalid character\n                // (technically wrong, but expected by some applications, like Windows file names).\n                write32.call(dst, this.highSurrogate, offset);\n                offset += 4;\n            }\n            else {\n                // Create 32-bit value from high and low surrogates;\n                var codepoint = (((this.highSurrogate - 0xD800) << 10) | (code - 0xDC00)) + 0x10000;\n\n                write32.call(dst, codepoint, offset);\n                offset += 4;\n                this.highSurrogate = 0;\n\n                continue;\n            }\n        }\n\n        if (isHighSurrogate)\n            this.highSurrogate = code;\n        else {\n            // Even if the current character is a low surrogate, with no previous high surrogate, we'll\n            // encode it as a semi-invalid stand-alone character for the same reasons expressed above for\n            // unpaired high surrogates.\n            write32.call(dst, code, offset);\n            offset += 4;\n            this.highSurrogate = 0;\n        }\n    }\n\n    if (offset < dst.length)\n        dst = dst.slice(0, offset);\n\n    return dst;\n};\n\nUtf32Encoder.prototype.end = function() {\n    // Treat any leftover high surrogate as a semi-valid independent character.\n    if (!this.highSurrogate)\n        return;\n\n    var buf = Buffer.alloc(4);\n\n    if (this.isLE)\n        buf.writeUInt32LE(this.highSurrogate, 0);\n    else\n        buf.writeUInt32BE(this.highSurrogate, 0);\n\n    this.highSurrogate = 0;\n\n    return buf;\n};\n\n// -- Decoding\n\nfunction Utf32Decoder(options, codec) {\n    this.isLE = codec.isLE;\n    this.badChar = codec.iconv.defaultCharUnicode.charCodeAt(0);\n    this.overflow = [];\n}\n\nUtf32Decoder.prototype.write = function(src) {\n    if (src.length === 0)\n        return '';\n\n    var i = 0;\n    var codepoint = 0;\n    var dst = Buffer.alloc(src.length + 4);\n    var offset = 0;\n    var isLE = this.isLE;\n    var overflow = this.overflow;\n    var badChar = this.badChar;\n\n    if (overflow.length > 0) {\n        for (; i < src.length && overflow.length < 4; i++)\n            overflow.push(src[i]);\n        \n        if (overflow.length === 4) {\n            // NOTE: codepoint is a signed int32 and can be negative.\n            // NOTE: We copied this block from below to help V8 optimize it (it works with array, not buffer).\n            if (isLE) {\n                codepoint = overflow[i] | (overflow[i+1] << 8) | (overflow[i+2] << 16) | (overflow[i+3] << 24);\n            } else {\n                codepoint = overflow[i+3] | (overflow[i+2] << 8) | (overflow[i+1] << 16) | (overflow[i] << 24);\n            }\n            overflow.length = 0;\n\n            offset = _writeCodepoint(dst, offset, codepoint, badChar);\n        }\n    }\n\n    // Main loop. Should be as optimized as possible.\n    for (; i < src.length - 3; i += 4) {\n        // NOTE: codepoint is a signed int32 and can be negative.\n        if (isLE) {\n            codepoint = src[i] | (src[i+1] << 8) | (src[i+2] << 16) | (src[i+3] << 24);\n        } else {\n            codepoint = src[i+3] | (src[i+2] << 8) | (src[i+1] << 16) | (src[i] << 24);\n        }\n        offset = _writeCodepoint(dst, offset, codepoint, badChar);\n    }\n\n    // Keep overflowing bytes.\n    for (; i < src.length; i++) {\n        overflow.push(src[i]);\n    }\n\n    return dst.slice(0, offset).toString('ucs2');\n};\n\nfunction _writeCodepoint(dst, offset, codepoint, badChar) {\n    // NOTE: codepoint is signed int32 and can be negative. We keep it that way to help V8 with optimizations.\n    if (codepoint < 0 || codepoint > 0x10FFFF) {\n        // Not a valid Unicode codepoint\n        codepoint = badChar;\n    } \n\n    // Ephemeral Planes: Write high surrogate.\n    if (codepoint >= 0x10000) {\n        codepoint -= 0x10000;\n\n        var high = 0xD800 | (codepoint >> 10);\n        dst[offset++] = high & 0xff;\n        dst[offset++] = high >> 8;\n\n        // Low surrogate is written below.\n        var codepoint = 0xDC00 | (codepoint & 0x3FF);\n    }\n\n    // Write BMP char or low surrogate.\n    dst[offset++] = codepoint & 0xff;\n    dst[offset++] = codepoint >> 8;\n\n    return offset;\n};\n\nUtf32Decoder.prototype.end = function() {\n    this.overflow.length = 0;\n};\n\n// == UTF-32 Auto codec =============================================================\n// Decoder chooses automatically from UTF-32LE and UTF-32BE using BOM and space-based heuristic.\n// Defaults to UTF-32LE. http://en.wikipedia.org/wiki/UTF-32\n// Encoder/decoder default can be changed: iconv.decode(buf, 'utf32', {defaultEncoding: 'utf-32be'});\n\n// Encoder prepends BOM (which can be overridden with (addBOM: false}).\n\nexports.utf32 = Utf32AutoCodec;\nexports.ucs4 = 'utf32';\n\nfunction Utf32AutoCodec(options, iconv) {\n    this.iconv = iconv;\n}\n\nUtf32AutoCodec.prototype.encoder = Utf32AutoEncoder;\nUtf32AutoCodec.prototype.decoder = Utf32AutoDecoder;\n\n// -- Encoding\n\nfunction Utf32AutoEncoder(options, codec) {\n    options = options || {};\n\n    if (options.addBOM === undefined)\n        options.addBOM = true;\n\n    this.encoder = codec.iconv.getEncoder(options.defaultEncoding || 'utf-32le', options);\n}\n\nUtf32AutoEncoder.prototype.write = function(str) {\n    return this.encoder.write(str);\n};\n\nUtf32AutoEncoder.prototype.end = function() {\n    return this.encoder.end();\n};\n\n// -- Decoding\n\nfunction Utf32AutoDecoder(options, codec) {\n    this.decoder = null;\n    this.initialBufs = [];\n    this.initialBufsLen = 0;\n    this.options = options || {};\n    this.iconv = codec.iconv;\n}\n\nUtf32AutoDecoder.prototype.write = function(buf) {\n    if (!this.decoder) { \n        // Codec is not chosen yet. Accumulate initial bytes.\n        this.initialBufs.push(buf);\n        this.initialBufsLen += buf.length;\n\n        if (this.initialBufsLen < 32) // We need more bytes to use space heuristic (see below)\n            return '';\n\n        // We have enough bytes -> detect endianness.\n        var encoding = detectEncoding(this.initialBufs, this.options.defaultEncoding);\n        this.decoder = this.iconv.getDecoder(encoding, this.options);\n\n        var resStr = '';\n        for (var i = 0; i < this.initialBufs.length; i++)\n            resStr += this.decoder.write(this.initialBufs[i]);\n\n        this.initialBufs.length = this.initialBufsLen = 0;\n        return resStr;\n    }\n\n    return this.decoder.write(buf);\n};\n\nUtf32AutoDecoder.prototype.end = function() {\n    if (!this.decoder) {\n        var encoding = detectEncoding(this.initialBufs, this.options.defaultEncoding);\n        this.decoder = this.iconv.getDecoder(encoding, this.options);\n\n        var resStr = '';\n        for (var i = 0; i < this.initialBufs.length; i++)\n            resStr += this.decoder.write(this.initialBufs[i]);\n\n        var trail = this.decoder.end();\n        if (trail)\n            resStr += trail;\n\n        this.initialBufs.length = this.initialBufsLen = 0;\n        return resStr;\n    }\n\n    return this.decoder.end();\n};\n\nfunction detectEncoding(bufs, defaultEncoding) {\n    var b = [];\n    var charsProcessed = 0;\n    var invalidLE = 0, invalidBE = 0;   // Number of invalid chars when decoded as LE or BE.\n    var bmpCharsLE = 0, bmpCharsBE = 0; // Number of BMP chars when decoded as LE or BE.\n\n    outer_loop:\n    for (var i = 0; i < bufs.length; i++) {\n        var buf = bufs[i];\n        for (var j = 0; j < buf.length; j++) {\n            b.push(buf[j]);\n            if (b.length === 4) {\n                if (charsProcessed === 0) {\n                    // Check BOM first.\n                    if (b[0] === 0xFF && b[1] === 0xFE && b[2] === 0 && b[3] === 0) {\n                        return 'utf-32le';\n                    }\n                    if (b[0] === 0 && b[1] === 0 && b[2] === 0xFE && b[3] === 0xFF) {\n                        return 'utf-32be';\n                    }\n                }\n\n                if (b[0] !== 0 || b[1] > 0x10) invalidBE++;\n                if (b[3] !== 0 || b[2] > 0x10) invalidLE++;\n\n                if (b[0] === 0 && b[1] === 0 && (b[2] !== 0 || b[3] !== 0)) bmpCharsBE++;\n                if ((b[0] !== 0 || b[1] !== 0) && b[2] === 0 && b[3] === 0) bmpCharsLE++;\n\n                b.length = 0;\n                charsProcessed++;\n\n                if (charsProcessed >= 100) {\n                    break outer_loop;\n                }\n            }\n        }\n    }\n\n    // Make decisions.\n    if (bmpCharsBE - invalidBE > bmpCharsLE - invalidLE)  return 'utf-32be';\n    if (bmpCharsBE - invalidBE < bmpCharsLE - invalidLE)  return 'utf-32le';\n\n    // Couldn't decide (likely all zeros or not enough data).\n    return defaultEncoding || 'utf-32le';\n}\n"], "names": [], "mappings": "AAAA;AAEA,IAAI,SAAS,+FAAwB,MAAM;AAE3C,mFAAmF;AAEnF,QAAQ,MAAM,GAAG;AAEjB,SAAS,WAAW,YAAY,EAAE,KAAK;IACnC,IAAI,CAAC,KAAK,GAAG;IACb,IAAI,CAAC,QAAQ,GAAG;IAChB,IAAI,CAAC,IAAI,GAAG,aAAa,IAAI;AACjC;AAEA,QAAQ,OAAO,GAAG;IAAE,MAAM;IAAU,MAAM;AAAK;AAC/C,QAAQ,OAAO,GAAG;IAAE,MAAM;IAAU,MAAM;AAAM;AAEhD,UAAU;AACV,QAAQ,MAAM,GAAG;AACjB,QAAQ,MAAM,GAAG;AAEjB,WAAW,SAAS,CAAC,OAAO,GAAG;AAC/B,WAAW,SAAS,CAAC,OAAO,GAAG;AAE/B,cAAc;AAEd,SAAS,aAAa,OAAO,EAAE,KAAK;IAChC,IAAI,CAAC,IAAI,GAAG,MAAM,IAAI;IACtB,IAAI,CAAC,aAAa,GAAG;AACzB;AAEA,aAAa,SAAS,CAAC,KAAK,GAAG,SAAS,GAAG;IACvC,IAAI,MAAM,OAAO,IAAI,CAAC,KAAK;IAC3B,IAAI,MAAM,OAAO,KAAK,CAAC,IAAI,MAAM,GAAG;IACpC,IAAI,UAAU,IAAI,CAAC,IAAI,GAAG,IAAI,aAAa,GAAG,IAAI,aAAa;IAC/D,IAAI,SAAS;IAEb,IAAK,IAAI,IAAI,GAAG,IAAI,IAAI,MAAM,EAAE,KAAK,EAAG;QACpC,IAAI,OAAO,IAAI,YAAY,CAAC;QAC5B,IAAI,kBAAmB,UAAU,QAAQ,OAAO;QAChD,IAAI,iBAAkB,UAAU,QAAQ,OAAO;QAE/C,IAAI,IAAI,CAAC,aAAa,EAAE;YACpB,IAAI,mBAAmB,CAAC,gBAAgB;gBACpC,sGAAsG;gBACtG,sGAAsG;gBACtG,mFAAmF;gBACnF,QAAQ,IAAI,CAAC,KAAK,IAAI,CAAC,aAAa,EAAE;gBACtC,UAAU;YACd,OACK;gBACD,oDAAoD;gBACpD,IAAI,YAAY,CAAC,AAAE,IAAI,CAAC,aAAa,GAAG,UAAW,KAAO,OAAO,MAAO,IAAI;gBAE5E,QAAQ,IAAI,CAAC,KAAK,WAAW;gBAC7B,UAAU;gBACV,IAAI,CAAC,aAAa,GAAG;gBAErB;YACJ;QACJ;QAEA,IAAI,iBACA,IAAI,CAAC,aAAa,GAAG;aACpB;YACD,2FAA2F;YAC3F,6FAA6F;YAC7F,4BAA4B;YAC5B,QAAQ,IAAI,CAAC,KAAK,MAAM;YACxB,UAAU;YACV,IAAI,CAAC,aAAa,GAAG;QACzB;IACJ;IAEA,IAAI,SAAS,IAAI,MAAM,EACnB,MAAM,IAAI,KAAK,CAAC,GAAG;IAEvB,OAAO;AACX;AAEA,aAAa,SAAS,CAAC,GAAG,GAAG;IACzB,2EAA2E;IAC3E,IAAI,CAAC,IAAI,CAAC,aAAa,EACnB;IAEJ,IAAI,MAAM,OAAO,KAAK,CAAC;IAEvB,IAAI,IAAI,CAAC,IAAI,EACT,IAAI,aAAa,CAAC,IAAI,CAAC,aAAa,EAAE;SAEtC,IAAI,aAAa,CAAC,IAAI,CAAC,aAAa,EAAE;IAE1C,IAAI,CAAC,aAAa,GAAG;IAErB,OAAO;AACX;AAEA,cAAc;AAEd,SAAS,aAAa,OAAO,EAAE,KAAK;IAChC,IAAI,CAAC,IAAI,GAAG,MAAM,IAAI;IACtB,IAAI,CAAC,OAAO,GAAG,MAAM,KAAK,CAAC,kBAAkB,CAAC,UAAU,CAAC;IACzD,IAAI,CAAC,QAAQ,GAAG,EAAE;AACtB;AAEA,aAAa,SAAS,CAAC,KAAK,GAAG,SAAS,GAAG;IACvC,IAAI,IAAI,MAAM,KAAK,GACf,OAAO;IAEX,IAAI,IAAI;IACR,IAAI,YAAY;IAChB,IAAI,MAAM,OAAO,KAAK,CAAC,IAAI,MAAM,GAAG;IACpC,IAAI,SAAS;IACb,IAAI,OAAO,IAAI,CAAC,IAAI;IACpB,IAAI,WAAW,IAAI,CAAC,QAAQ;IAC5B,IAAI,UAAU,IAAI,CAAC,OAAO;IAE1B,IAAI,SAAS,MAAM,GAAG,GAAG;QACrB,MAAO,IAAI,IAAI,MAAM,IAAI,SAAS,MAAM,GAAG,GAAG,IAC1C,SAAS,IAAI,CAAC,GAAG,CAAC,EAAE;QAExB,IAAI,SAAS,MAAM,KAAK,GAAG;YACvB,yDAAyD;YACzD,kGAAkG;YAClG,IAAI,MAAM;gBACN,YAAY,QAAQ,CAAC,EAAE,GAAI,QAAQ,CAAC,IAAE,EAAE,IAAI,IAAM,QAAQ,CAAC,IAAE,EAAE,IAAI,KAAO,QAAQ,CAAC,IAAE,EAAE,IAAI;YAC/F,OAAO;gBACH,YAAY,QAAQ,CAAC,IAAE,EAAE,GAAI,QAAQ,CAAC,IAAE,EAAE,IAAI,IAAM,QAAQ,CAAC,IAAE,EAAE,IAAI,KAAO,QAAQ,CAAC,EAAE,IAAI;YAC/F;YACA,SAAS,MAAM,GAAG;YAElB,SAAS,gBAAgB,KAAK,QAAQ,WAAW;QACrD;IACJ;IAEA,iDAAiD;IACjD,MAAO,IAAI,IAAI,MAAM,GAAG,GAAG,KAAK,EAAG;QAC/B,yDAAyD;QACzD,IAAI,MAAM;YACN,YAAY,GAAG,CAAC,EAAE,GAAI,GAAG,CAAC,IAAE,EAAE,IAAI,IAAM,GAAG,CAAC,IAAE,EAAE,IAAI,KAAO,GAAG,CAAC,IAAE,EAAE,IAAI;QAC3E,OAAO;YACH,YAAY,GAAG,CAAC,IAAE,EAAE,GAAI,GAAG,CAAC,IAAE,EAAE,IAAI,IAAM,GAAG,CAAC,IAAE,EAAE,IAAI,KAAO,GAAG,CAAC,EAAE,IAAI;QAC3E;QACA,SAAS,gBAAgB,KAAK,QAAQ,WAAW;IACrD;IAEA,0BAA0B;IAC1B,MAAO,IAAI,IAAI,MAAM,EAAE,IAAK;QACxB,SAAS,IAAI,CAAC,GAAG,CAAC,EAAE;IACxB;IAEA,OAAO,IAAI,KAAK,CAAC,GAAG,QAAQ,QAAQ,CAAC;AACzC;AAEA,SAAS,gBAAgB,GAAG,EAAE,MAAM,EAAE,SAAS,EAAE,OAAO;IACpD,0GAA0G;IAC1G,IAAI,YAAY,KAAK,YAAY,UAAU;QACvC,gCAAgC;QAChC,YAAY;IAChB;IAEA,0CAA0C;IAC1C,IAAI,aAAa,SAAS;QACtB,aAAa;QAEb,IAAI,OAAO,SAAU,aAAa;QAClC,GAAG,CAAC,SAAS,GAAG,OAAO;QACvB,GAAG,CAAC,SAAS,GAAG,QAAQ;QAExB,kCAAkC;QAClC,IAAI,YAAY,SAAU,YAAY;IAC1C;IAEA,mCAAmC;IACnC,GAAG,CAAC,SAAS,GAAG,YAAY;IAC5B,GAAG,CAAC,SAAS,GAAG,aAAa;IAE7B,OAAO;AACX;;AAEA,aAAa,SAAS,CAAC,GAAG,GAAG;IACzB,IAAI,CAAC,QAAQ,CAAC,MAAM,GAAG;AAC3B;AAEA,qFAAqF;AACrF,gGAAgG;AAChG,4DAA4D;AAC5D,qGAAqG;AAErG,uEAAuE;AAEvE,QAAQ,KAAK,GAAG;AAChB,QAAQ,IAAI,GAAG;AAEf,SAAS,eAAe,OAAO,EAAE,KAAK;IAClC,IAAI,CAAC,KAAK,GAAG;AACjB;AAEA,eAAe,SAAS,CAAC,OAAO,GAAG;AACnC,eAAe,SAAS,CAAC,OAAO,GAAG;AAEnC,cAAc;AAEd,SAAS,iBAAiB,OAAO,EAAE,KAAK;IACpC,UAAU,WAAW,CAAC;IAEtB,IAAI,QAAQ,MAAM,KAAK,WACnB,QAAQ,MAAM,GAAG;IAErB,IAAI,CAAC,OAAO,GAAG,MAAM,KAAK,CAAC,UAAU,CAAC,QAAQ,eAAe,IAAI,YAAY;AACjF;AAEA,iBAAiB,SAAS,CAAC,KAAK,GAAG,SAAS,GAAG;IAC3C,OAAO,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC;AAC9B;AAEA,iBAAiB,SAAS,CAAC,GAAG,GAAG;IAC7B,OAAO,IAAI,CAAC,OAAO,CAAC,GAAG;AAC3B;AAEA,cAAc;AAEd,SAAS,iBAAiB,OAAO,EAAE,KAAK;IACpC,IAAI,CAAC,OAAO,GAAG;IACf,IAAI,CAAC,WAAW,GAAG,EAAE;IACrB,IAAI,CAAC,cAAc,GAAG;IACtB,IAAI,CAAC,OAAO,GAAG,WAAW,CAAC;IAC3B,IAAI,CAAC,KAAK,GAAG,MAAM,KAAK;AAC5B;AAEA,iBAAiB,SAAS,CAAC,KAAK,GAAG,SAAS,GAAG;IAC3C,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE;QACf,qDAAqD;QACrD,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC;QACtB,IAAI,CAAC,cAAc,IAAI,IAAI,MAAM;QAEjC,IAAI,IAAI,CAAC,cAAc,GAAG,IACtB,OAAO;QAEX,6CAA6C;QAC7C,IAAI,WAAW,eAAe,IAAI,CAAC,WAAW,EAAE,IAAI,CAAC,OAAO,CAAC,eAAe;QAC5E,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC,UAAU,IAAI,CAAC,OAAO;QAE3D,IAAI,SAAS;QACb,IAAK,IAAI,IAAI,GAAG,IAAI,IAAI,CAAC,WAAW,CAAC,MAAM,EAAE,IACzC,UAAU,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,IAAI,CAAC,WAAW,CAAC,EAAE;QAEpD,IAAI,CAAC,WAAW,CAAC,MAAM,GAAG,IAAI,CAAC,cAAc,GAAG;QAChD,OAAO;IACX;IAEA,OAAO,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC;AAC9B;AAEA,iBAAiB,SAAS,CAAC,GAAG,GAAG;IAC7B,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE;QACf,IAAI,WAAW,eAAe,IAAI,CAAC,WAAW,EAAE,IAAI,CAAC,OAAO,CAAC,eAAe;QAC5E,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC,UAAU,IAAI,CAAC,OAAO;QAE3D,IAAI,SAAS;QACb,IAAK,IAAI,IAAI,GAAG,IAAI,IAAI,CAAC,WAAW,CAAC,MAAM,EAAE,IACzC,UAAU,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,IAAI,CAAC,WAAW,CAAC,EAAE;QAEpD,IAAI,QAAQ,IAAI,CAAC,OAAO,CAAC,GAAG;QAC5B,IAAI,OACA,UAAU;QAEd,IAAI,CAAC,WAAW,CAAC,MAAM,GAAG,IAAI,CAAC,cAAc,GAAG;QAChD,OAAO;IACX;IAEA,OAAO,IAAI,CAAC,OAAO,CAAC,GAAG;AAC3B;AAEA,SAAS,eAAe,IAAI,EAAE,eAAe;IACzC,IAAI,IAAI,EAAE;IACV,IAAI,iBAAiB;IACrB,IAAI,YAAY,GAAG,YAAY,GAAK,oDAAoD;IACxF,IAAI,aAAa,GAAG,aAAa,GAAG,gDAAgD;IAEpF,YACA,IAAK,IAAI,IAAI,GAAG,IAAI,KAAK,MAAM,EAAE,IAAK;QAClC,IAAI,MAAM,IAAI,CAAC,EAAE;QACjB,IAAK,IAAI,IAAI,GAAG,IAAI,IAAI,MAAM,EAAE,IAAK;YACjC,EAAE,IAAI,CAAC,GAAG,CAAC,EAAE;YACb,IAAI,EAAE,MAAM,KAAK,GAAG;gBAChB,IAAI,mBAAmB,GAAG;oBACtB,mBAAmB;oBACnB,IAAI,CAAC,CAAC,EAAE,KAAK,QAAQ,CAAC,CAAC,EAAE,KAAK,QAAQ,CAAC,CAAC,EAAE,KAAK,KAAK,CAAC,CAAC,EAAE,KAAK,GAAG;wBAC5D,OAAO;oBACX;oBACA,IAAI,CAAC,CAAC,EAAE,KAAK,KAAK,CAAC,CAAC,EAAE,KAAK,KAAK,CAAC,CAAC,EAAE,KAAK,QAAQ,CAAC,CAAC,EAAE,KAAK,MAAM;wBAC5D,OAAO;oBACX;gBACJ;gBAEA,IAAI,CAAC,CAAC,EAAE,KAAK,KAAK,CAAC,CAAC,EAAE,GAAG,MAAM;gBAC/B,IAAI,CAAC,CAAC,EAAE,KAAK,KAAK,CAAC,CAAC,EAAE,GAAG,MAAM;gBAE/B,IAAI,CAAC,CAAC,EAAE,KAAK,KAAK,CAAC,CAAC,EAAE,KAAK,KAAK,CAAC,CAAC,CAAC,EAAE,KAAK,KAAK,CAAC,CAAC,EAAE,KAAK,CAAC,GAAG;gBAC5D,IAAI,CAAC,CAAC,CAAC,EAAE,KAAK,KAAK,CAAC,CAAC,EAAE,KAAK,CAAC,KAAK,CAAC,CAAC,EAAE,KAAK,KAAK,CAAC,CAAC,EAAE,KAAK,GAAG;gBAE5D,EAAE,MAAM,GAAG;gBACX;gBAEA,IAAI,kBAAkB,KAAK;oBACvB,MAAM;gBACV;YACJ;QACJ;IACJ;IAEA,kBAAkB;IAClB,IAAI,aAAa,YAAY,aAAa,WAAY,OAAO;IAC7D,IAAI,aAAa,YAAY,aAAa,WAAY,OAAO;IAE7D,yDAAyD;IACzD,OAAO,mBAAmB;AAC9B", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 466, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/New%20folder%20%2825%29/streamzen/node_modules/iconv-lite/encodings/utf16.js"], "sourcesContent": ["\"use strict\";\nvar Buffer = require(\"safer-buffer\").Buffer;\n\n// Note: UTF16-LE (or UCS2) codec is Node.js native. See encodings/internal.js\n\n// == UTF16-BE codec. ==========================================================\n\nexports.utf16be = Utf16BECodec;\nfunction Utf16BECodec() {\n}\n\nUtf16BECodec.prototype.encoder = Utf16BEEncoder;\nUtf16BECodec.prototype.decoder = Utf16BEDecoder;\nUtf16BECodec.prototype.bomAware = true;\n\n\n// -- Encoding\n\nfunction Utf16BEEncoder() {\n}\n\nUtf16BEEncoder.prototype.write = function(str) {\n    var buf = Buffer.from(str, 'ucs2');\n    for (var i = 0; i < buf.length; i += 2) {\n        var tmp = buf[i]; buf[i] = buf[i+1]; buf[i+1] = tmp;\n    }\n    return buf;\n}\n\nUtf16BEEncoder.prototype.end = function() {\n}\n\n\n// -- Decoding\n\nfunction Utf16BEDecoder() {\n    this.overflowByte = -1;\n}\n\nUtf16BEDecoder.prototype.write = function(buf) {\n    if (buf.length == 0)\n        return '';\n\n    var buf2 = Buffer.alloc(buf.length + 1),\n        i = 0, j = 0;\n\n    if (this.overflowByte !== -1) {\n        buf2[0] = buf[0];\n        buf2[1] = this.overflowByte;\n        i = 1; j = 2;\n    }\n\n    for (; i < buf.length-1; i += 2, j+= 2) {\n        buf2[j] = buf[i+1];\n        buf2[j+1] = buf[i];\n    }\n\n    this.overflowByte = (i == buf.length-1) ? buf[buf.length-1] : -1;\n\n    return buf2.slice(0, j).toString('ucs2');\n}\n\nUtf16BEDecoder.prototype.end = function() {\n    this.overflowByte = -1;\n}\n\n\n// == UTF-16 codec =============================================================\n// Decoder chooses automatically from UTF-16LE and UTF-16BE using BOM and space-based heuristic.\n// Defaults to UTF-16LE, as it's prevalent and default in Node.\n// http://en.wikipedia.org/wiki/UTF-16 and http://encoding.spec.whatwg.org/#utf-16le\n// Decoder default can be changed: iconv.decode(buf, 'utf16', {defaultEncoding: 'utf-16be'});\n\n// Encoder uses UTF-16LE and prepends BOM (which can be overridden with addBOM: false).\n\nexports.utf16 = Utf16Codec;\nfunction Utf16Codec(codecOptions, iconv) {\n    this.iconv = iconv;\n}\n\nUtf16Codec.prototype.encoder = Utf16Encoder;\nUtf16Codec.prototype.decoder = Utf16Decoder;\n\n\n// -- Encoding (pass-through)\n\nfunction Utf16Encoder(options, codec) {\n    options = options || {};\n    if (options.addBOM === undefined)\n        options.addBOM = true;\n    this.encoder = codec.iconv.getEncoder('utf-16le', options);\n}\n\nUtf16Encoder.prototype.write = function(str) {\n    return this.encoder.write(str);\n}\n\nUtf16Encoder.prototype.end = function() {\n    return this.encoder.end();\n}\n\n\n// -- Decoding\n\nfunction Utf16Decoder(options, codec) {\n    this.decoder = null;\n    this.initialBufs = [];\n    this.initialBufsLen = 0;\n\n    this.options = options || {};\n    this.iconv = codec.iconv;\n}\n\nUtf16Decoder.prototype.write = function(buf) {\n    if (!this.decoder) {\n        // Codec is not chosen yet. Accumulate initial bytes.\n        this.initialBufs.push(buf);\n        this.initialBufsLen += buf.length;\n        \n        if (this.initialBufsLen < 16) // We need more bytes to use space heuristic (see below)\n            return '';\n\n        // We have enough bytes -> detect endianness.\n        var encoding = detectEncoding(this.initialBufs, this.options.defaultEncoding);\n        this.decoder = this.iconv.getDecoder(encoding, this.options);\n\n        var resStr = '';\n        for (var i = 0; i < this.initialBufs.length; i++)\n            resStr += this.decoder.write(this.initialBufs[i]);\n\n        this.initialBufs.length = this.initialBufsLen = 0;\n        return resStr;\n    }\n\n    return this.decoder.write(buf);\n}\n\nUtf16Decoder.prototype.end = function() {\n    if (!this.decoder) {\n        var encoding = detectEncoding(this.initialBufs, this.options.defaultEncoding);\n        this.decoder = this.iconv.getDecoder(encoding, this.options);\n\n        var resStr = '';\n        for (var i = 0; i < this.initialBufs.length; i++)\n            resStr += this.decoder.write(this.initialBufs[i]);\n\n        var trail = this.decoder.end();\n        if (trail)\n            resStr += trail;\n\n        this.initialBufs.length = this.initialBufsLen = 0;\n        return resStr;\n    }\n    return this.decoder.end();\n}\n\nfunction detectEncoding(bufs, defaultEncoding) {\n    var b = [];\n    var charsProcessed = 0;\n    var asciiCharsLE = 0, asciiCharsBE = 0; // Number of ASCII chars when decoded as LE or BE.\n\n    outer_loop:\n    for (var i = 0; i < bufs.length; i++) {\n        var buf = bufs[i];\n        for (var j = 0; j < buf.length; j++) {\n            b.push(buf[j]);\n            if (b.length === 2) {\n                if (charsProcessed === 0) {\n                    // Check BOM first.\n                    if (b[0] === 0xFF && b[1] === 0xFE) return 'utf-16le';\n                    if (b[0] === 0xFE && b[1] === 0xFF) return 'utf-16be';\n                }\n\n                if (b[0] === 0 && b[1] !== 0) asciiCharsBE++;\n                if (b[0] !== 0 && b[1] === 0) asciiCharsLE++;\n\n                b.length = 0;\n                charsProcessed++;\n\n                if (charsProcessed >= 100) {\n                    break outer_loop;\n                }\n            }\n        }\n    }\n\n    // Make decisions.\n    // Most of the time, the content has ASCII chars (U+00**), but the opposite (U+**00) is uncommon.\n    // So, we count ASCII as if it was LE or BE, and decide from that.\n    if (asciiCharsBE > asciiCharsLE) return 'utf-16be';\n    if (asciiCharsBE < asciiCharsLE) return 'utf-16le';\n\n    // Couldn't decide (likely all zeros or not enough data).\n    return defaultEncoding || 'utf-16le';\n}\n\n\n"], "names": [], "mappings": "AAAA;AACA,IAAI,SAAS,+FAAwB,MAAM;AAE3C,8EAA8E;AAE9E,gFAAgF;AAEhF,QAAQ,OAAO,GAAG;AAClB,SAAS,gBACT;AAEA,aAAa,SAAS,CAAC,OAAO,GAAG;AACjC,aAAa,SAAS,CAAC,OAAO,GAAG;AACjC,aAAa,SAAS,CAAC,QAAQ,GAAG;AAGlC,cAAc;AAEd,SAAS,kBACT;AAEA,eAAe,SAAS,CAAC,KAAK,GAAG,SAAS,GAAG;IACzC,IAAI,MAAM,OAAO,IAAI,CAAC,KAAK;IAC3B,IAAK,IAAI,IAAI,GAAG,IAAI,IAAI,MAAM,EAAE,KAAK,EAAG;QACpC,IAAI,MAAM,GAAG,CAAC,EAAE;QAAE,GAAG,CAAC,EAAE,GAAG,GAAG,CAAC,IAAE,EAAE;QAAE,GAAG,CAAC,IAAE,EAAE,GAAG;IACpD;IACA,OAAO;AACX;AAEA,eAAe,SAAS,CAAC,GAAG,GAAG,YAC/B;AAGA,cAAc;AAEd,SAAS;IACL,IAAI,CAAC,YAAY,GAAG,CAAC;AACzB;AAEA,eAAe,SAAS,CAAC,KAAK,GAAG,SAAS,GAAG;IACzC,IAAI,IAAI,MAAM,IAAI,GACd,OAAO;IAEX,IAAI,OAAO,OAAO,KAAK,CAAC,IAAI,MAAM,GAAG,IACjC,IAAI,GAAG,IAAI;IAEf,IAAI,IAAI,CAAC,YAAY,KAAK,CAAC,GAAG;QAC1B,IAAI,CAAC,EAAE,GAAG,GAAG,CAAC,EAAE;QAChB,IAAI,CAAC,EAAE,GAAG,IAAI,CAAC,YAAY;QAC3B,IAAI;QAAG,IAAI;IACf;IAEA,MAAO,IAAI,IAAI,MAAM,GAAC,GAAG,KAAK,GAAG,KAAI,EAAG;QACpC,IAAI,CAAC,EAAE,GAAG,GAAG,CAAC,IAAE,EAAE;QAClB,IAAI,CAAC,IAAE,EAAE,GAAG,GAAG,CAAC,EAAE;IACtB;IAEA,IAAI,CAAC,YAAY,GAAG,AAAC,KAAK,IAAI,MAAM,GAAC,IAAK,GAAG,CAAC,IAAI,MAAM,GAAC,EAAE,GAAG,CAAC;IAE/D,OAAO,KAAK,KAAK,CAAC,GAAG,GAAG,QAAQ,CAAC;AACrC;AAEA,eAAe,SAAS,CAAC,GAAG,GAAG;IAC3B,IAAI,CAAC,YAAY,GAAG,CAAC;AACzB;AAGA,gFAAgF;AAChF,gGAAgG;AAChG,+DAA+D;AAC/D,oFAAoF;AACpF,6FAA6F;AAE7F,uFAAuF;AAEvF,QAAQ,KAAK,GAAG;AAChB,SAAS,WAAW,YAAY,EAAE,KAAK;IACnC,IAAI,CAAC,KAAK,GAAG;AACjB;AAEA,WAAW,SAAS,CAAC,OAAO,GAAG;AAC/B,WAAW,SAAS,CAAC,OAAO,GAAG;AAG/B,6BAA6B;AAE7B,SAAS,aAAa,OAAO,EAAE,KAAK;IAChC,UAAU,WAAW,CAAC;IACtB,IAAI,QAAQ,MAAM,KAAK,WACnB,QAAQ,MAAM,GAAG;IACrB,IAAI,CAAC,OAAO,GAAG,MAAM,KAAK,CAAC,UAAU,CAAC,YAAY;AACtD;AAEA,aAAa,SAAS,CAAC,KAAK,GAAG,SAAS,GAAG;IACvC,OAAO,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC;AAC9B;AAEA,aAAa,SAAS,CAAC,GAAG,GAAG;IACzB,OAAO,IAAI,CAAC,OAAO,CAAC,GAAG;AAC3B;AAGA,cAAc;AAEd,SAAS,aAAa,OAAO,EAAE,KAAK;IAChC,IAAI,CAAC,OAAO,GAAG;IACf,IAAI,CAAC,WAAW,GAAG,EAAE;IACrB,IAAI,CAAC,cAAc,GAAG;IAEtB,IAAI,CAAC,OAAO,GAAG,WAAW,CAAC;IAC3B,IAAI,CAAC,KAAK,GAAG,MAAM,KAAK;AAC5B;AAEA,aAAa,SAAS,CAAC,KAAK,GAAG,SAAS,GAAG;IACvC,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE;QACf,qDAAqD;QACrD,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC;QACtB,IAAI,CAAC,cAAc,IAAI,IAAI,MAAM;QAEjC,IAAI,IAAI,CAAC,cAAc,GAAG,IACtB,OAAO;QAEX,6CAA6C;QAC7C,IAAI,WAAW,eAAe,IAAI,CAAC,WAAW,EAAE,IAAI,CAAC,OAAO,CAAC,eAAe;QAC5E,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC,UAAU,IAAI,CAAC,OAAO;QAE3D,IAAI,SAAS;QACb,IAAK,IAAI,IAAI,GAAG,IAAI,IAAI,CAAC,WAAW,CAAC,MAAM,EAAE,IACzC,UAAU,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,IAAI,CAAC,WAAW,CAAC,EAAE;QAEpD,IAAI,CAAC,WAAW,CAAC,MAAM,GAAG,IAAI,CAAC,cAAc,GAAG;QAChD,OAAO;IACX;IAEA,OAAO,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC;AAC9B;AAEA,aAAa,SAAS,CAAC,GAAG,GAAG;IACzB,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE;QACf,IAAI,WAAW,eAAe,IAAI,CAAC,WAAW,EAAE,IAAI,CAAC,OAAO,CAAC,eAAe;QAC5E,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC,UAAU,IAAI,CAAC,OAAO;QAE3D,IAAI,SAAS;QACb,IAAK,IAAI,IAAI,GAAG,IAAI,IAAI,CAAC,WAAW,CAAC,MAAM,EAAE,IACzC,UAAU,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,IAAI,CAAC,WAAW,CAAC,EAAE;QAEpD,IAAI,QAAQ,IAAI,CAAC,OAAO,CAAC,GAAG;QAC5B,IAAI,OACA,UAAU;QAEd,IAAI,CAAC,WAAW,CAAC,MAAM,GAAG,IAAI,CAAC,cAAc,GAAG;QAChD,OAAO;IACX;IACA,OAAO,IAAI,CAAC,OAAO,CAAC,GAAG;AAC3B;AAEA,SAAS,eAAe,IAAI,EAAE,eAAe;IACzC,IAAI,IAAI,EAAE;IACV,IAAI,iBAAiB;IACrB,IAAI,eAAe,GAAG,eAAe,GAAG,kDAAkD;IAE1F,YACA,IAAK,IAAI,IAAI,GAAG,IAAI,KAAK,MAAM,EAAE,IAAK;QAClC,IAAI,MAAM,IAAI,CAAC,EAAE;QACjB,IAAK,IAAI,IAAI,GAAG,IAAI,IAAI,MAAM,EAAE,IAAK;YACjC,EAAE,IAAI,CAAC,GAAG,CAAC,EAAE;YACb,IAAI,EAAE,MAAM,KAAK,GAAG;gBAChB,IAAI,mBAAmB,GAAG;oBACtB,mBAAmB;oBACnB,IAAI,CAAC,CAAC,EAAE,KAAK,QAAQ,CAAC,CAAC,EAAE,KAAK,MAAM,OAAO;oBAC3C,IAAI,CAAC,CAAC,EAAE,KAAK,QAAQ,CAAC,CAAC,EAAE,KAAK,MAAM,OAAO;gBAC/C;gBAEA,IAAI,CAAC,CAAC,EAAE,KAAK,KAAK,CAAC,CAAC,EAAE,KAAK,GAAG;gBAC9B,IAAI,CAAC,CAAC,EAAE,KAAK,KAAK,CAAC,CAAC,EAAE,KAAK,GAAG;gBAE9B,EAAE,MAAM,GAAG;gBACX;gBAEA,IAAI,kBAAkB,KAAK;oBACvB,MAAM;gBACV;YACJ;QACJ;IACJ;IAEA,kBAAkB;IAClB,iGAAiG;IACjG,kEAAkE;IAClE,IAAI,eAAe,cAAc,OAAO;IACxC,IAAI,eAAe,cAAc,OAAO;IAExC,yDAAyD;IACzD,OAAO,mBAAmB;AAC9B", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 608, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/New%20folder%20%2825%29/streamzen/node_modules/iconv-lite/encodings/utf7.js"], "sourcesContent": ["\"use strict\";\nvar Buffer = require(\"safer-buffer\").Buffer;\n\n// UTF-7 codec, according to https://tools.ietf.org/html/rfc2152\n// See also below a UTF-7-IMAP codec, according to http://tools.ietf.org/html/rfc3501#section-5.1.3\n\nexports.utf7 = Utf7Codec;\nexports.unicode11utf7 = 'utf7'; // Alias UNICODE-1-1-UTF-7\nfunction Utf7Codec(codecOptions, iconv) {\n    this.iconv = iconv;\n};\n\nUtf7Codec.prototype.encoder = Utf7Encoder;\nUtf7Codec.prototype.decoder = Utf7Decoder;\nUtf7Codec.prototype.bomAware = true;\n\n\n// -- Encoding\n\nvar nonDirectChars = /[^A-Za-z0-9'\\(\\),-\\.\\/:\\? \\n\\r\\t]+/g;\n\nfunction Utf7Encoder(options, codec) {\n    this.iconv = codec.iconv;\n}\n\nUtf7Encoder.prototype.write = function(str) {\n    // Naive implementation.\n    // Non-direct chars are encoded as \"+<base64>-\"; single \"+\" char is encoded as \"+-\".\n    return Buffer.from(str.replace(nonDirectChars, function(chunk) {\n        return \"+\" + (chunk === '+' ? '' : \n            this.iconv.encode(chunk, 'utf16-be').toString('base64').replace(/=+$/, '')) \n            + \"-\";\n    }.bind(this)));\n}\n\nUtf7Encoder.prototype.end = function() {\n}\n\n\n// -- Decoding\n\nfunction Utf7Decoder(options, codec) {\n    this.iconv = codec.iconv;\n    this.inBase64 = false;\n    this.base64Accum = '';\n}\n\nvar base64Regex = /[A-Za-z0-9\\/+]/;\nvar base64Chars = [];\nfor (var i = 0; i < 256; i++)\n    base64Chars[i] = base64Regex.test(String.fromCharCode(i));\n\nvar plusChar = '+'.charCodeAt(0), \n    minusChar = '-'.charCodeAt(0),\n    andChar = '&'.charCodeAt(0);\n\nUtf7Decoder.prototype.write = function(buf) {\n    var res = \"\", lastI = 0,\n        inBase64 = this.inBase64,\n        base64Accum = this.base64Accum;\n\n    // The decoder is more involved as we must handle chunks in stream.\n\n    for (var i = 0; i < buf.length; i++) {\n        if (!inBase64) { // We're in direct mode.\n            // Write direct chars until '+'\n            if (buf[i] == plusChar) {\n                res += this.iconv.decode(buf.slice(lastI, i), \"ascii\"); // Write direct chars.\n                lastI = i+1;\n                inBase64 = true;\n            }\n        } else { // We decode base64.\n            if (!base64Chars[buf[i]]) { // Base64 ended.\n                if (i == lastI && buf[i] == minusChar) {// \"+-\" -> \"+\"\n                    res += \"+\";\n                } else {\n                    var b64str = base64Accum + this.iconv.decode(buf.slice(lastI, i), \"ascii\");\n                    res += this.iconv.decode(Buffer.from(b64str, 'base64'), \"utf16-be\");\n                }\n\n                if (buf[i] != minusChar) // Minus is absorbed after base64.\n                    i--;\n\n                lastI = i+1;\n                inBase64 = false;\n                base64Accum = '';\n            }\n        }\n    }\n\n    if (!inBase64) {\n        res += this.iconv.decode(buf.slice(lastI), \"ascii\"); // Write direct chars.\n    } else {\n        var b64str = base64Accum + this.iconv.decode(buf.slice(lastI), \"ascii\");\n\n        var canBeDecoded = b64str.length - (b64str.length % 8); // Minimal chunk: 2 quads -> 2x3 bytes -> 3 chars.\n        base64Accum = b64str.slice(canBeDecoded); // The rest will be decoded in future.\n        b64str = b64str.slice(0, canBeDecoded);\n\n        res += this.iconv.decode(Buffer.from(b64str, 'base64'), \"utf16-be\");\n    }\n\n    this.inBase64 = inBase64;\n    this.base64Accum = base64Accum;\n\n    return res;\n}\n\nUtf7Decoder.prototype.end = function() {\n    var res = \"\";\n    if (this.inBase64 && this.base64Accum.length > 0)\n        res = this.iconv.decode(Buffer.from(this.base64Accum, 'base64'), \"utf16-be\");\n\n    this.inBase64 = false;\n    this.base64Accum = '';\n    return res;\n}\n\n\n// UTF-7-IMAP codec.\n// RFC3501 Sec. 5.1.3 Modified UTF-7 (http://tools.ietf.org/html/rfc3501#section-5.1.3)\n// Differences:\n//  * Base64 part is started by \"&\" instead of \"+\"\n//  * Direct characters are 0x20-0x7E, except \"&\" (0x26)\n//  * In Base64, \",\" is used instead of \"/\"\n//  * Base64 must not be used to represent direct characters.\n//  * No implicit shift back from Base64 (should always end with '-')\n//  * String must end in non-shifted position.\n//  * \"-&\" while in base64 is not allowed.\n\n\nexports.utf7imap = Utf7IMAPCodec;\nfunction Utf7IMAPCodec(codecOptions, iconv) {\n    this.iconv = iconv;\n};\n\nUtf7IMAPCodec.prototype.encoder = Utf7IMAPEncoder;\nUtf7IMAPCodec.prototype.decoder = Utf7IMAPDecoder;\nUtf7IMAPCodec.prototype.bomAware = true;\n\n\n// -- Encoding\n\nfunction Utf7IMAPEncoder(options, codec) {\n    this.iconv = codec.iconv;\n    this.inBase64 = false;\n    this.base64Accum = Buffer.alloc(6);\n    this.base64AccumIdx = 0;\n}\n\nUtf7IMAPEncoder.prototype.write = function(str) {\n    var inBase64 = this.inBase64,\n        base64Accum = this.base64Accum,\n        base64AccumIdx = this.base64AccumIdx,\n        buf = Buffer.alloc(str.length*5 + 10), bufIdx = 0;\n\n    for (var i = 0; i < str.length; i++) {\n        var uChar = str.charCodeAt(i);\n        if (0x20 <= uChar && uChar <= 0x7E) { // Direct character or '&'.\n            if (inBase64) {\n                if (base64AccumIdx > 0) {\n                    bufIdx += buf.write(base64Accum.slice(0, base64AccumIdx).toString('base64').replace(/\\//g, ',').replace(/=+$/, ''), bufIdx);\n                    base64AccumIdx = 0;\n                }\n\n                buf[bufIdx++] = minusChar; // Write '-', then go to direct mode.\n                inBase64 = false;\n            }\n\n            if (!inBase64) {\n                buf[bufIdx++] = uChar; // Write direct character\n\n                if (uChar === andChar)  // Ampersand -> '&-'\n                    buf[bufIdx++] = minusChar;\n            }\n\n        } else { // Non-direct character\n            if (!inBase64) {\n                buf[bufIdx++] = andChar; // Write '&', then go to base64 mode.\n                inBase64 = true;\n            }\n            if (inBase64) {\n                base64Accum[base64AccumIdx++] = uChar >> 8;\n                base64Accum[base64AccumIdx++] = uChar & 0xFF;\n\n                if (base64AccumIdx == base64Accum.length) {\n                    bufIdx += buf.write(base64Accum.toString('base64').replace(/\\//g, ','), bufIdx);\n                    base64AccumIdx = 0;\n                }\n            }\n        }\n    }\n\n    this.inBase64 = inBase64;\n    this.base64AccumIdx = base64AccumIdx;\n\n    return buf.slice(0, bufIdx);\n}\n\nUtf7IMAPEncoder.prototype.end = function() {\n    var buf = Buffer.alloc(10), bufIdx = 0;\n    if (this.inBase64) {\n        if (this.base64AccumIdx > 0) {\n            bufIdx += buf.write(this.base64Accum.slice(0, this.base64AccumIdx).toString('base64').replace(/\\//g, ',').replace(/=+$/, ''), bufIdx);\n            this.base64AccumIdx = 0;\n        }\n\n        buf[bufIdx++] = minusChar; // Write '-', then go to direct mode.\n        this.inBase64 = false;\n    }\n\n    return buf.slice(0, bufIdx);\n}\n\n\n// -- Decoding\n\nfunction Utf7IMAPDecoder(options, codec) {\n    this.iconv = codec.iconv;\n    this.inBase64 = false;\n    this.base64Accum = '';\n}\n\nvar base64IMAPChars = base64Chars.slice();\nbase64IMAPChars[','.charCodeAt(0)] = true;\n\nUtf7IMAPDecoder.prototype.write = function(buf) {\n    var res = \"\", lastI = 0,\n        inBase64 = this.inBase64,\n        base64Accum = this.base64Accum;\n\n    // The decoder is more involved as we must handle chunks in stream.\n    // It is forgiving, closer to standard UTF-7 (for example, '-' is optional at the end).\n\n    for (var i = 0; i < buf.length; i++) {\n        if (!inBase64) { // We're in direct mode.\n            // Write direct chars until '&'\n            if (buf[i] == andChar) {\n                res += this.iconv.decode(buf.slice(lastI, i), \"ascii\"); // Write direct chars.\n                lastI = i+1;\n                inBase64 = true;\n            }\n        } else { // We decode base64.\n            if (!base64IMAPChars[buf[i]]) { // Base64 ended.\n                if (i == lastI && buf[i] == minusChar) { // \"&-\" -> \"&\"\n                    res += \"&\";\n                } else {\n                    var b64str = base64Accum + this.iconv.decode(buf.slice(lastI, i), \"ascii\").replace(/,/g, '/');\n                    res += this.iconv.decode(Buffer.from(b64str, 'base64'), \"utf16-be\");\n                }\n\n                if (buf[i] != minusChar) // Minus may be absorbed after base64.\n                    i--;\n\n                lastI = i+1;\n                inBase64 = false;\n                base64Accum = '';\n            }\n        }\n    }\n\n    if (!inBase64) {\n        res += this.iconv.decode(buf.slice(lastI), \"ascii\"); // Write direct chars.\n    } else {\n        var b64str = base64Accum + this.iconv.decode(buf.slice(lastI), \"ascii\").replace(/,/g, '/');\n\n        var canBeDecoded = b64str.length - (b64str.length % 8); // Minimal chunk: 2 quads -> 2x3 bytes -> 3 chars.\n        base64Accum = b64str.slice(canBeDecoded); // The rest will be decoded in future.\n        b64str = b64str.slice(0, canBeDecoded);\n\n        res += this.iconv.decode(Buffer.from(b64str, 'base64'), \"utf16-be\");\n    }\n\n    this.inBase64 = inBase64;\n    this.base64Accum = base64Accum;\n\n    return res;\n}\n\nUtf7IMAPDecoder.prototype.end = function() {\n    var res = \"\";\n    if (this.inBase64 && this.base64Accum.length > 0)\n        res = this.iconv.decode(Buffer.from(this.base64Accum, 'base64'), \"utf16-be\");\n\n    this.inBase64 = false;\n    this.base64Accum = '';\n    return res;\n}\n\n\n"], "names": [], "mappings": "AAAA;AACA,IAAI,SAAS,+FAAwB,MAAM;AAE3C,gEAAgE;AAChE,mGAAmG;AAEnG,QAAQ,IAAI,GAAG;AACf,QAAQ,aAAa,GAAG,QAAQ,0BAA0B;AAC1D,SAAS,UAAU,YAAY,EAAE,KAAK;IAClC,IAAI,CAAC,KAAK,GAAG;AACjB;;AAEA,UAAU,SAAS,CAAC,OAAO,GAAG;AAC9B,UAAU,SAAS,CAAC,OAAO,GAAG;AAC9B,UAAU,SAAS,CAAC,QAAQ,GAAG;AAG/B,cAAc;AAEd,IAAI,iBAAiB;AAErB,SAAS,YAAY,OAAO,EAAE,KAAK;IAC/B,IAAI,CAAC,KAAK,GAAG,MAAM,KAAK;AAC5B;AAEA,YAAY,SAAS,CAAC,KAAK,GAAG,SAAS,GAAG;IACtC,wBAAwB;IACxB,oFAAoF;IACpF,OAAO,OAAO,IAAI,CAAC,IAAI,OAAO,CAAC,gBAAgB,CAAA,SAAS,KAAK;QACzD,OAAO,MAAM,CAAC,UAAU,MAAM,KAC1B,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,OAAO,YAAY,QAAQ,CAAC,UAAU,OAAO,CAAC,OAAO,GAAG,IACxE;IACV,CAAA,EAAE,IAAI,CAAC,IAAI;AACf;AAEA,YAAY,SAAS,CAAC,GAAG,GAAG,YAC5B;AAGA,cAAc;AAEd,SAAS,YAAY,OAAO,EAAE,KAAK;IAC/B,IAAI,CAAC,KAAK,GAAG,MAAM,KAAK;IACxB,IAAI,CAAC,QAAQ,GAAG;IAChB,IAAI,CAAC,WAAW,GAAG;AACvB;AAEA,IAAI,cAAc;AAClB,IAAI,cAAc,EAAE;AACpB,IAAK,IAAI,IAAI,GAAG,IAAI,KAAK,IACrB,WAAW,CAAC,EAAE,GAAG,YAAY,IAAI,CAAC,OAAO,YAAY,CAAC;AAE1D,IAAI,WAAW,IAAI,UAAU,CAAC,IAC1B,YAAY,IAAI,UAAU,CAAC,IAC3B,UAAU,IAAI,UAAU,CAAC;AAE7B,YAAY,SAAS,CAAC,KAAK,GAAG,SAAS,GAAG;IACtC,IAAI,MAAM,IAAI,QAAQ,GAClB,WAAW,IAAI,CAAC,QAAQ,EACxB,cAAc,IAAI,CAAC,WAAW;IAElC,mEAAmE;IAEnE,IAAK,IAAI,IAAI,GAAG,IAAI,IAAI,MAAM,EAAE,IAAK;QACjC,IAAI,CAAC,UAAU;YACX,+BAA+B;YAC/B,IAAI,GAAG,CAAC,EAAE,IAAI,UAAU;gBACpB,OAAO,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,IAAI,KAAK,CAAC,OAAO,IAAI,UAAU,sBAAsB;gBAC9E,QAAQ,IAAE;gBACV,WAAW;YACf;QACJ,OAAO;YACH,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE;gBACtB,IAAI,KAAK,SAAS,GAAG,CAAC,EAAE,IAAI,WAAW;oBACnC,OAAO;gBACX,OAAO;oBACH,IAAI,SAAS,cAAc,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,IAAI,KAAK,CAAC,OAAO,IAAI;oBAClE,OAAO,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,OAAO,IAAI,CAAC,QAAQ,WAAW;gBAC5D;gBAEA,IAAI,GAAG,CAAC,EAAE,IAAI,WACV;gBAEJ,QAAQ,IAAE;gBACV,WAAW;gBACX,cAAc;YAClB;QACJ;IACJ;IAEA,IAAI,CAAC,UAAU;QACX,OAAO,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,IAAI,KAAK,CAAC,QAAQ,UAAU,sBAAsB;IAC/E,OAAO;QACH,IAAI,SAAS,cAAc,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,IAAI,KAAK,CAAC,QAAQ;QAE/D,IAAI,eAAe,OAAO,MAAM,GAAI,OAAO,MAAM,GAAG,GAAI,kDAAkD;QAC1G,cAAc,OAAO,KAAK,CAAC,eAAe,sCAAsC;QAChF,SAAS,OAAO,KAAK,CAAC,GAAG;QAEzB,OAAO,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,OAAO,IAAI,CAAC,QAAQ,WAAW;IAC5D;IAEA,IAAI,CAAC,QAAQ,GAAG;IAChB,IAAI,CAAC,WAAW,GAAG;IAEnB,OAAO;AACX;AAEA,YAAY,SAAS,CAAC,GAAG,GAAG;IACxB,IAAI,MAAM;IACV,IAAI,IAAI,CAAC,QAAQ,IAAI,IAAI,CAAC,WAAW,CAAC,MAAM,GAAG,GAC3C,MAAM,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,OAAO,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE,WAAW;IAErE,IAAI,CAAC,QAAQ,GAAG;IAChB,IAAI,CAAC,WAAW,GAAG;IACnB,OAAO;AACX;AAGA,oBAAoB;AACpB,uFAAuF;AACvF,eAAe;AACf,kDAAkD;AAClD,wDAAwD;AACxD,2CAA2C;AAC3C,6DAA6D;AAC7D,qEAAqE;AACrE,8CAA8C;AAC9C,0CAA0C;AAG1C,QAAQ,QAAQ,GAAG;AACnB,SAAS,cAAc,YAAY,EAAE,KAAK;IACtC,IAAI,CAAC,KAAK,GAAG;AACjB;;AAEA,cAAc,SAAS,CAAC,OAAO,GAAG;AAClC,cAAc,SAAS,CAAC,OAAO,GAAG;AAClC,cAAc,SAAS,CAAC,QAAQ,GAAG;AAGnC,cAAc;AAEd,SAAS,gBAAgB,OAAO,EAAE,KAAK;IACnC,IAAI,CAAC,KAAK,GAAG,MAAM,KAAK;IACxB,IAAI,CAAC,QAAQ,GAAG;IAChB,IAAI,CAAC,WAAW,GAAG,OAAO,KAAK,CAAC;IAChC,IAAI,CAAC,cAAc,GAAG;AAC1B;AAEA,gBAAgB,SAAS,CAAC,KAAK,GAAG,SAAS,GAAG;IAC1C,IAAI,WAAW,IAAI,CAAC,QAAQ,EACxB,cAAc,IAAI,CAAC,WAAW,EAC9B,iBAAiB,IAAI,CAAC,cAAc,EACpC,MAAM,OAAO,KAAK,CAAC,IAAI,MAAM,GAAC,IAAI,KAAK,SAAS;IAEpD,IAAK,IAAI,IAAI,GAAG,IAAI,IAAI,MAAM,EAAE,IAAK;QACjC,IAAI,QAAQ,IAAI,UAAU,CAAC;QAC3B,IAAI,QAAQ,SAAS,SAAS,MAAM;YAChC,IAAI,UAAU;gBACV,IAAI,iBAAiB,GAAG;oBACpB,UAAU,IAAI,KAAK,CAAC,YAAY,KAAK,CAAC,GAAG,gBAAgB,QAAQ,CAAC,UAAU,OAAO,CAAC,OAAO,KAAK,OAAO,CAAC,OAAO,KAAK;oBACpH,iBAAiB;gBACrB;gBAEA,GAAG,CAAC,SAAS,GAAG,WAAW,qCAAqC;gBAChE,WAAW;YACf;YAEA,IAAI,CAAC,UAAU;gBACX,GAAG,CAAC,SAAS,GAAG,OAAO,yBAAyB;gBAEhD,IAAI,UAAU,SACV,GAAG,CAAC,SAAS,GAAG;YACxB;QAEJ,OAAO;YACH,IAAI,CAAC,UAAU;gBACX,GAAG,CAAC,SAAS,GAAG,SAAS,qCAAqC;gBAC9D,WAAW;YACf;YACA,IAAI,UAAU;gBACV,WAAW,CAAC,iBAAiB,GAAG,SAAS;gBACzC,WAAW,CAAC,iBAAiB,GAAG,QAAQ;gBAExC,IAAI,kBAAkB,YAAY,MAAM,EAAE;oBACtC,UAAU,IAAI,KAAK,CAAC,YAAY,QAAQ,CAAC,UAAU,OAAO,CAAC,OAAO,MAAM;oBACxE,iBAAiB;gBACrB;YACJ;QACJ;IACJ;IAEA,IAAI,CAAC,QAAQ,GAAG;IAChB,IAAI,CAAC,cAAc,GAAG;IAEtB,OAAO,IAAI,KAAK,CAAC,GAAG;AACxB;AAEA,gBAAgB,SAAS,CAAC,GAAG,GAAG;IAC5B,IAAI,MAAM,OAAO,KAAK,CAAC,KAAK,SAAS;IACrC,IAAI,IAAI,CAAC,QAAQ,EAAE;QACf,IAAI,IAAI,CAAC,cAAc,GAAG,GAAG;YACzB,UAAU,IAAI,KAAK,CAAC,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC,GAAG,IAAI,CAAC,cAAc,EAAE,QAAQ,CAAC,UAAU,OAAO,CAAC,OAAO,KAAK,OAAO,CAAC,OAAO,KAAK;YAC9H,IAAI,CAAC,cAAc,GAAG;QAC1B;QAEA,GAAG,CAAC,SAAS,GAAG,WAAW,qCAAqC;QAChE,IAAI,CAAC,QAAQ,GAAG;IACpB;IAEA,OAAO,IAAI,KAAK,CAAC,GAAG;AACxB;AAGA,cAAc;AAEd,SAAS,gBAAgB,OAAO,EAAE,KAAK;IACnC,IAAI,CAAC,KAAK,GAAG,MAAM,KAAK;IACxB,IAAI,CAAC,QAAQ,GAAG;IAChB,IAAI,CAAC,WAAW,GAAG;AACvB;AAEA,IAAI,kBAAkB,YAAY,KAAK;AACvC,eAAe,CAAC,IAAI,UAAU,CAAC,GAAG,GAAG;AAErC,gBAAgB,SAAS,CAAC,KAAK,GAAG,SAAS,GAAG;IAC1C,IAAI,MAAM,IAAI,QAAQ,GAClB,WAAW,IAAI,CAAC,QAAQ,EACxB,cAAc,IAAI,CAAC,WAAW;IAElC,mEAAmE;IACnE,uFAAuF;IAEvF,IAAK,IAAI,IAAI,GAAG,IAAI,IAAI,MAAM,EAAE,IAAK;QACjC,IAAI,CAAC,UAAU;YACX,+BAA+B;YAC/B,IAAI,GAAG,CAAC,EAAE,IAAI,SAAS;gBACnB,OAAO,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,IAAI,KAAK,CAAC,OAAO,IAAI,UAAU,sBAAsB;gBAC9E,QAAQ,IAAE;gBACV,WAAW;YACf;QACJ,OAAO;YACH,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE;gBAC1B,IAAI,KAAK,SAAS,GAAG,CAAC,EAAE,IAAI,WAAW;oBACnC,OAAO;gBACX,OAAO;oBACH,IAAI,SAAS,cAAc,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,IAAI,KAAK,CAAC,OAAO,IAAI,SAAS,OAAO,CAAC,MAAM;oBACzF,OAAO,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,OAAO,IAAI,CAAC,QAAQ,WAAW;gBAC5D;gBAEA,IAAI,GAAG,CAAC,EAAE,IAAI,WACV;gBAEJ,QAAQ,IAAE;gBACV,WAAW;gBACX,cAAc;YAClB;QACJ;IACJ;IAEA,IAAI,CAAC,UAAU;QACX,OAAO,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,IAAI,KAAK,CAAC,QAAQ,UAAU,sBAAsB;IAC/E,OAAO;QACH,IAAI,SAAS,cAAc,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,IAAI,KAAK,CAAC,QAAQ,SAAS,OAAO,CAAC,MAAM;QAEtF,IAAI,eAAe,OAAO,MAAM,GAAI,OAAO,MAAM,GAAG,GAAI,kDAAkD;QAC1G,cAAc,OAAO,KAAK,CAAC,eAAe,sCAAsC;QAChF,SAAS,OAAO,KAAK,CAAC,GAAG;QAEzB,OAAO,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,OAAO,IAAI,CAAC,QAAQ,WAAW;IAC5D;IAEA,IAAI,CAAC,QAAQ,GAAG;IAChB,IAAI,CAAC,WAAW,GAAG;IAEnB,OAAO;AACX;AAEA,gBAAgB,SAAS,CAAC,GAAG,GAAG;IAC5B,IAAI,MAAM;IACV,IAAI,IAAI,CAAC,QAAQ,IAAI,IAAI,CAAC,WAAW,CAAC,MAAM,GAAG,GAC3C,MAAM,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,OAAO,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE,WAAW;IAErE,IAAI,CAAC,QAAQ,GAAG;IAChB,IAAI,CAAC,WAAW,GAAG;IACnB,OAAO;AACX", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 823, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/New%20folder%20%2825%29/streamzen/node_modules/iconv-lite/encodings/sbcs-codec.js"], "sourcesContent": ["\"use strict\";\nvar Buffer = require(\"safer-buffer\").Buffer;\n\n// Single-byte codec. Needs a 'chars' string parameter that contains 256 or 128 chars that\n// correspond to encoded bytes (if 128 - then lower half is ASCII). \n\nexports._sbcs = SBCSCodec;\nfunction SBCSCodec(codecOptions, iconv) {\n    if (!codecOptions)\n        throw new Error(\"SBCS codec is called without the data.\")\n    \n    // Prepare char buffer for decoding.\n    if (!codecOptions.chars || (codecOptions.chars.length !== 128 && codecOptions.chars.length !== 256))\n        throw new Error(\"Encoding '\"+codecOptions.type+\"' has incorrect 'chars' (must be of len 128 or 256)\");\n    \n    if (codecOptions.chars.length === 128) {\n        var asciiString = \"\";\n        for (var i = 0; i < 128; i++)\n            asciiString += String.fromCharCode(i);\n        codecOptions.chars = asciiString + codecOptions.chars;\n    }\n\n    this.decodeBuf = Buffer.from(codecOptions.chars, 'ucs2');\n    \n    // Encoding buffer.\n    var encodeBuf = Buffer.alloc(65536, iconv.defaultCharSingleByte.charCodeAt(0));\n\n    for (var i = 0; i < codecOptions.chars.length; i++)\n        encodeBuf[codecOptions.chars.charCodeAt(i)] = i;\n\n    this.encodeBuf = encodeBuf;\n}\n\nSBCSCodec.prototype.encoder = SBCSEncoder;\nSBCSCodec.prototype.decoder = SBCSDecoder;\n\n\nfunction SBCSEncoder(options, codec) {\n    this.encodeBuf = codec.encodeBuf;\n}\n\nSBCSEncoder.prototype.write = function(str) {\n    var buf = Buffer.alloc(str.length);\n    for (var i = 0; i < str.length; i++)\n        buf[i] = this.encodeBuf[str.charCodeAt(i)];\n    \n    return buf;\n}\n\nSBCSEncoder.prototype.end = function() {\n}\n\n\nfunction SBCSDecoder(options, codec) {\n    this.decodeBuf = codec.decodeBuf;\n}\n\nSBCSDecoder.prototype.write = function(buf) {\n    // Strings are immutable in JS -> we use ucs2 buffer to speed up computations.\n    var decodeBuf = this.decodeBuf;\n    var newBuf = Buffer.alloc(buf.length*2);\n    var idx1 = 0, idx2 = 0;\n    for (var i = 0; i < buf.length; i++) {\n        idx1 = buf[i]*2; idx2 = i*2;\n        newBuf[idx2] = decodeBuf[idx1];\n        newBuf[idx2+1] = decodeBuf[idx1+1];\n    }\n    return newBuf.toString('ucs2');\n}\n\nSBCSDecoder.prototype.end = function() {\n}\n"], "names": [], "mappings": "AAAA;AACA,IAAI,SAAS,+FAAwB,MAAM;AAE3C,0FAA0F;AAC1F,oEAAoE;AAEpE,QAAQ,KAAK,GAAG;AAChB,SAAS,UAAU,YAAY,EAAE,KAAK;IAClC,IAAI,CAAC,cACD,MAAM,IAAI,MAAM;IAEpB,oCAAoC;IACpC,IAAI,CAAC,aAAa,KAAK,IAAK,aAAa,KAAK,CAAC,MAAM,KAAK,OAAO,aAAa,KAAK,CAAC,MAAM,KAAK,KAC3F,MAAM,IAAI,MAAM,eAAa,aAAa,IAAI,GAAC;IAEnD,IAAI,aAAa,KAAK,CAAC,MAAM,KAAK,KAAK;QACnC,IAAI,cAAc;QAClB,IAAK,IAAI,IAAI,GAAG,IAAI,KAAK,IACrB,eAAe,OAAO,YAAY,CAAC;QACvC,aAAa,KAAK,GAAG,cAAc,aAAa,KAAK;IACzD;IAEA,IAAI,CAAC,SAAS,GAAG,OAAO,IAAI,CAAC,aAAa,KAAK,EAAE;IAEjD,mBAAmB;IACnB,IAAI,YAAY,OAAO,KAAK,CAAC,OAAO,MAAM,qBAAqB,CAAC,UAAU,CAAC;IAE3E,IAAK,IAAI,IAAI,GAAG,IAAI,aAAa,KAAK,CAAC,MAAM,EAAE,IAC3C,SAAS,CAAC,aAAa,KAAK,CAAC,UAAU,CAAC,GAAG,GAAG;IAElD,IAAI,CAAC,SAAS,GAAG;AACrB;AAEA,UAAU,SAAS,CAAC,OAAO,GAAG;AAC9B,UAAU,SAAS,CAAC,OAAO,GAAG;AAG9B,SAAS,YAAY,OAAO,EAAE,KAAK;IAC/B,IAAI,CAAC,SAAS,GAAG,MAAM,SAAS;AACpC;AAEA,YAAY,SAAS,CAAC,KAAK,GAAG,SAAS,GAAG;IACtC,IAAI,MAAM,OAAO,KAAK,CAAC,IAAI,MAAM;IACjC,IAAK,IAAI,IAAI,GAAG,IAAI,IAAI,MAAM,EAAE,IAC5B,GAAG,CAAC,EAAE,GAAG,IAAI,CAAC,SAAS,CAAC,IAAI,UAAU,CAAC,GAAG;IAE9C,OAAO;AACX;AAEA,YAAY,SAAS,CAAC,GAAG,GAAG,YAC5B;AAGA,SAAS,YAAY,OAAO,EAAE,KAAK;IAC/B,IAAI,CAAC,SAAS,GAAG,MAAM,SAAS;AACpC;AAEA,YAAY,SAAS,CAAC,KAAK,GAAG,SAAS,GAAG;IACtC,8EAA8E;IAC9E,IAAI,YAAY,IAAI,CAAC,SAAS;IAC9B,IAAI,SAAS,OAAO,KAAK,CAAC,IAAI,MAAM,GAAC;IACrC,IAAI,OAAO,GAAG,OAAO;IACrB,IAAK,IAAI,IAAI,GAAG,IAAI,IAAI,MAAM,EAAE,IAAK;QACjC,OAAO,GAAG,CAAC,EAAE,GAAC;QAAG,OAAO,IAAE;QAC1B,MAAM,CAAC,KAAK,GAAG,SAAS,CAAC,KAAK;QAC9B,MAAM,CAAC,OAAK,EAAE,GAAG,SAAS,CAAC,OAAK,EAAE;IACtC;IACA,OAAO,OAAO,QAAQ,CAAC;AAC3B;AAEA,YAAY,SAAS,CAAC,GAAG,GAAG,YAC5B", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 876, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/New%20folder%20%2825%29/streamzen/node_modules/iconv-lite/encodings/sbcs-data.js"], "sourcesContent": ["\"use strict\";\n\n// Manually added data to be used by sbcs codec in addition to generated one.\n\nmodule.exports = {\n    // Not supported by iconv, not sure why.\n    \"10029\": \"maccenteuro\",\n    \"maccenteuro\": {\n        \"type\": \"_sbcs\",\n        \"chars\": \"ÄĀāÉĄÖÜáąČäčĆćéŹźĎíďĒēĖóėôöõúĚěü†°Ę£§•¶ß®©™ę¨≠ģĮįĪ≤≥īĶ∂∑łĻļĽľĹĺŅņŃ¬√ńŇ∆«»… ňŐÕőŌ–—“”‘’÷◊ōŔŕŘ‹›řŖŗŠ‚„šŚśÁŤťÍŽžŪÓÔūŮÚůŰűŲųÝýķŻŁżĢˇ\"\n    },\n\n    \"808\": \"cp808\",\n    \"ibm808\": \"cp808\",\n    \"cp808\": {\n        \"type\": \"_sbcs\",\n        \"chars\": \"АБВГДЕЖЗИЙКЛМНОПРСТУФХЦЧШЩЪЫЬЭЮЯабвгдежзийклмноп░▒▓│┤╡╢╖╕╣║╗╝╜╛┐└┴┬├─┼╞╟╚╔╩╦╠═╬╧╨╤╥╙╘╒╓╫╪┘┌█▄▌▐▀рстуфхцчшщъыьэюяЁёЄєЇїЎў°∙·√№€■ \"\n    },\n\n    \"mik\": {\n        \"type\": \"_sbcs\",\n        \"chars\": \"АБВГДЕЖЗИЙКЛМНОПРСТУФХЦЧШЩЪЫЬЭЮЯабвгдежзийклмнопрстуфхцчшщъыьэюя└┴┬├─┼╣║╚╔╩╦╠═╬┐░▒▓│┤№§╗╝┘┌█▄▌▐▀αßΓπΣσµτΦΘΩδ∞φε∩≡±≥≤⌠⌡÷≈°∙·√ⁿ²■ \"\n    },\n\n    \"cp720\": {\n        \"type\": \"_sbcs\",\n        \"chars\": \"\\x80\\x81éâ\\x84à\\x86çêëèïî\\x8d\\x8e\\x8f\\x90\\u0651\\u0652ô¤ـûùءآأؤ£إئابةتثجحخدذرزسشص«»░▒▓│┤╡╢╖╕╣║╗╝╜╛┐└┴┬├─┼╞╟╚╔╩╦╠═╬╧╨╤╥╙╘╒╓╫╪┘┌█▄▌▐▀ضطظعغفµقكلمنهوىي≡\\u064b\\u064c\\u064d\\u064e\\u064f\\u0650≈°∙·√ⁿ²■\\u00a0\"\n    },\n\n    // Aliases of generated encodings.\n    \"ascii8bit\": \"ascii\",\n    \"usascii\": \"ascii\",\n    \"ansix34\": \"ascii\",\n    \"ansix341968\": \"ascii\",\n    \"ansix341986\": \"ascii\",\n    \"csascii\": \"ascii\",\n    \"cp367\": \"ascii\",\n    \"ibm367\": \"ascii\",\n    \"isoir6\": \"ascii\",\n    \"iso646us\": \"ascii\",\n    \"iso646irv\": \"ascii\",\n    \"us\": \"ascii\",\n\n    \"latin1\": \"iso88591\",\n    \"latin2\": \"iso88592\",\n    \"latin3\": \"iso88593\",\n    \"latin4\": \"iso88594\",\n    \"latin5\": \"iso88599\",\n    \"latin6\": \"iso885910\",\n    \"latin7\": \"iso885913\",\n    \"latin8\": \"iso885914\",\n    \"latin9\": \"iso885915\",\n    \"latin10\": \"iso885916\",\n\n    \"csisolatin1\": \"iso88591\",\n    \"csisolatin2\": \"iso88592\",\n    \"csisolatin3\": \"iso88593\",\n    \"csisolatin4\": \"iso88594\",\n    \"csisolatincyrillic\": \"iso88595\",\n    \"csisolatinarabic\": \"iso88596\",\n    \"csisolatingreek\" : \"iso88597\",\n    \"csisolatinhebrew\": \"iso88598\",\n    \"csisolatin5\": \"iso88599\",\n    \"csisolatin6\": \"iso885910\",\n\n    \"l1\": \"iso88591\",\n    \"l2\": \"iso88592\",\n    \"l3\": \"iso88593\",\n    \"l4\": \"iso88594\",\n    \"l5\": \"iso88599\",\n    \"l6\": \"iso885910\",\n    \"l7\": \"iso885913\",\n    \"l8\": \"iso885914\",\n    \"l9\": \"iso885915\",\n    \"l10\": \"iso885916\",\n\n    \"isoir14\": \"iso646jp\",\n    \"isoir57\": \"iso646cn\",\n    \"isoir100\": \"iso88591\",\n    \"isoir101\": \"iso88592\",\n    \"isoir109\": \"iso88593\",\n    \"isoir110\": \"iso88594\",\n    \"isoir144\": \"iso88595\",\n    \"isoir127\": \"iso88596\",\n    \"isoir126\": \"iso88597\",\n    \"isoir138\": \"iso88598\",\n    \"isoir148\": \"iso88599\",\n    \"isoir157\": \"iso885910\",\n    \"isoir166\": \"tis620\",\n    \"isoir179\": \"iso885913\",\n    \"isoir199\": \"iso885914\",\n    \"isoir203\": \"iso885915\",\n    \"isoir226\": \"iso885916\",\n\n    \"cp819\": \"iso88591\",\n    \"ibm819\": \"iso88591\",\n\n    \"cyrillic\": \"iso88595\",\n\n    \"arabic\": \"iso88596\",\n    \"arabic8\": \"iso88596\",\n    \"ecma114\": \"iso88596\",\n    \"asmo708\": \"iso88596\",\n\n    \"greek\" : \"iso88597\",\n    \"greek8\" : \"iso88597\",\n    \"ecma118\" : \"iso88597\",\n    \"elot928\" : \"iso88597\",\n\n    \"hebrew\": \"iso88598\",\n    \"hebrew8\": \"iso88598\",\n\n    \"turkish\": \"iso88599\",\n    \"turkish8\": \"iso88599\",\n\n    \"thai\": \"iso885911\",\n    \"thai8\": \"iso885911\",\n\n    \"celtic\": \"iso885914\",\n    \"celtic8\": \"iso885914\",\n    \"isoceltic\": \"iso885914\",\n\n    \"tis6200\": \"tis620\",\n    \"tis62025291\": \"tis620\",\n    \"tis62025330\": \"tis620\",\n\n    \"10000\": \"macroman\",\n    \"10006\": \"macgreek\",\n    \"10007\": \"maccyrillic\",\n    \"10079\": \"maciceland\",\n    \"10081\": \"macturkish\",\n\n    \"cspc8codepage437\": \"cp437\",\n    \"cspc775baltic\": \"cp775\",\n    \"cspc850multilingual\": \"cp850\",\n    \"cspcp852\": \"cp852\",\n    \"cspc862latinhebrew\": \"cp862\",\n    \"cpgr\": \"cp869\",\n\n    \"msee\": \"cp1250\",\n    \"mscyrl\": \"cp1251\",\n    \"msansi\": \"cp1252\",\n    \"msgreek\": \"cp1253\",\n    \"msturk\": \"cp1254\",\n    \"mshebr\": \"cp1255\",\n    \"msarab\": \"cp1256\",\n    \"winbaltrim\": \"cp1257\",\n\n    \"cp20866\": \"koi8r\",\n    \"20866\": \"koi8r\",\n    \"ibm878\": \"koi8r\",\n    \"cskoi8r\": \"koi8r\",\n\n    \"cp21866\": \"koi8u\",\n    \"21866\": \"koi8u\",\n    \"ibm1168\": \"koi8u\",\n\n    \"strk10482002\": \"rk1048\",\n\n    \"tcvn5712\": \"tcvn\",\n    \"tcvn57121\": \"tcvn\",\n\n    \"gb198880\": \"iso646cn\",\n    \"cn\": \"iso646cn\",\n\n    \"csiso14jisc6220ro\": \"iso646jp\",\n    \"jisc62201969ro\": \"iso646jp\",\n    \"jp\": \"iso646jp\",\n\n    \"cshproman8\": \"hproman8\",\n    \"r8\": \"hproman8\",\n    \"roman8\": \"hproman8\",\n    \"xroman8\": \"hproman8\",\n    \"ibm1051\": \"hproman8\",\n\n    \"mac\": \"macintosh\",\n    \"csmacintosh\": \"macintosh\",\n};\n\n"], "names": [], "mappings": "AAAA;AAEA,6EAA6E;AAE7E,OAAO,OAAO,GAAG;IACb,wCAAwC;IACxC,SAAS;IACT,eAAe;QACX,QAAQ;QACR,SAAS;IACb;IAEA,OAAO;IACP,UAAU;IACV,SAAS;QACL,QAAQ;QACR,SAAS;IACb;IAEA,OAAO;QACH,QAAQ;QACR,SAAS;IACb;IAEA,SAAS;QACL,QAAQ;QACR,SAAS;IACb;IAEA,kCAAkC;IAClC,aAAa;IACb,WAAW;IACX,WAAW;IACX,eAAe;IACf,eAAe;IACf,WAAW;IACX,SAAS;IACT,UAAU;IACV,UAAU;IACV,YAAY;IACZ,aAAa;IACb,MAAM;IAEN,UAAU;IACV,UAAU;IACV,UAAU;IACV,UAAU;IACV,UAAU;IACV,UAAU;IACV,UAAU;IACV,UAAU;IACV,UAAU;IACV,WAAW;IAEX,eAAe;IACf,eAAe;IACf,eAAe;IACf,eAAe;IACf,sBAAsB;IACtB,oBAAoB;IACpB,mBAAoB;IACpB,oBAAoB;IACpB,eAAe;IACf,eAAe;IAEf,MAAM;IACN,MAAM;IACN,MAAM;IACN,MAAM;IACN,MAAM;IACN,MAAM;IACN,MAAM;IACN,MAAM;IACN,MAAM;IACN,OAAO;IAEP,WAAW;IACX,WAAW;IACX,YAAY;IACZ,YAAY;IACZ,YAAY;IACZ,YAAY;IACZ,YAAY;IACZ,YAAY;IACZ,YAAY;IACZ,YAAY;IACZ,YAAY;IACZ,YAAY;IACZ,YAAY;IACZ,YAAY;IACZ,YAAY;IACZ,YAAY;IACZ,YAAY;IAEZ,SAAS;IACT,UAAU;IAEV,YAAY;IAEZ,UAAU;IACV,WAAW;IACX,WAAW;IACX,WAAW;IAEX,SAAU;IACV,UAAW;IACX,WAAY;IACZ,WAAY;IAEZ,UAAU;IACV,WAAW;IAEX,WAAW;IACX,YAAY;IAEZ,QAAQ;IACR,SAAS;IAET,UAAU;IACV,WAAW;IACX,aAAa;IAEb,WAAW;IACX,eAAe;IACf,eAAe;IAEf,SAAS;IACT,SAAS;IACT,SAAS;IACT,SAAS;IACT,SAAS;IAET,oBAAoB;IACpB,iBAAiB;IACjB,uBAAuB;IACvB,YAAY;IACZ,sBAAsB;IACtB,QAAQ;IAER,QAAQ;IACR,UAAU;IACV,UAAU;IACV,WAAW;IACX,UAAU;IACV,UAAU;IACV,UAAU;IACV,cAAc;IAEd,WAAW;IACX,SAAS;IACT,UAAU;IACV,WAAW;IAEX,WAAW;IACX,SAAS;IACT,WAAW;IAEX,gBAAgB;IAEhB,YAAY;IACZ,aAAa;IAEb,YAAY;IACZ,MAAM;IAEN,qBAAqB;IACrB,kBAAkB;IAClB,MAAM;IAEN,cAAc;IACd,MAAM;IACN,UAAU;IACV,WAAW;IACX,WAAW;IAEX,OAAO;IACP,eAAe;AACnB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1029, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/New%20folder%20%2825%29/streamzen/node_modules/iconv-lite/encodings/sbcs-data-generated.js"], "sourcesContent": ["\"use strict\";\n\n// Generated data for sbcs codec. Don't edit manually. Regenerate using generation/gen-sbcs.js script.\nmodule.exports = {\n  \"437\": \"cp437\",\n  \"737\": \"cp737\",\n  \"775\": \"cp775\",\n  \"850\": \"cp850\",\n  \"852\": \"cp852\",\n  \"855\": \"cp855\",\n  \"856\": \"cp856\",\n  \"857\": \"cp857\",\n  \"858\": \"cp858\",\n  \"860\": \"cp860\",\n  \"861\": \"cp861\",\n  \"862\": \"cp862\",\n  \"863\": \"cp863\",\n  \"864\": \"cp864\",\n  \"865\": \"cp865\",\n  \"866\": \"cp866\",\n  \"869\": \"cp869\",\n  \"874\": \"windows874\",\n  \"922\": \"cp922\",\n  \"1046\": \"cp1046\",\n  \"1124\": \"cp1124\",\n  \"1125\": \"cp1125\",\n  \"1129\": \"cp1129\",\n  \"1133\": \"cp1133\",\n  \"1161\": \"cp1161\",\n  \"1162\": \"cp1162\",\n  \"1163\": \"cp1163\",\n  \"1250\": \"windows1250\",\n  \"1251\": \"windows1251\",\n  \"1252\": \"windows1252\",\n  \"1253\": \"windows1253\",\n  \"1254\": \"windows1254\",\n  \"1255\": \"windows1255\",\n  \"1256\": \"windows1256\",\n  \"1257\": \"windows1257\",\n  \"1258\": \"windows1258\",\n  \"28591\": \"iso88591\",\n  \"28592\": \"iso88592\",\n  \"28593\": \"iso88593\",\n  \"28594\": \"iso88594\",\n  \"28595\": \"iso88595\",\n  \"28596\": \"iso88596\",\n  \"28597\": \"iso88597\",\n  \"28598\": \"iso88598\",\n  \"28599\": \"iso88599\",\n  \"28600\": \"iso885910\",\n  \"28601\": \"iso885911\",\n  \"28603\": \"iso885913\",\n  \"28604\": \"iso885914\",\n  \"28605\": \"iso885915\",\n  \"28606\": \"iso885916\",\n  \"windows874\": {\n    \"type\": \"_sbcs\",\n    \"chars\": \"€����…�����������‘’“”•–—�������� กขฃคฅฆงจฉชซฌญฎฏฐฑฒณดตถทธนบปผฝพฟภมยรฤลฦวศษสหฬอฮฯะัาำิีึืฺุู����฿เแโใไๅๆ็่้๊๋์ํ๎๏๐๑๒๓๔๕๖๗๘๙๚๛����\"\n  },\n  \"win874\": \"windows874\",\n  \"cp874\": \"windows874\",\n  \"windows1250\": {\n    \"type\": \"_sbcs\",\n    \"chars\": \"€�‚�„…†‡�‰Š‹ŚŤŽŹ�‘’“”•–—�™š›śťžź ˇ˘Ł¤Ą¦§¨©Ş«¬­®Ż°±˛ł´µ¶·¸ąş»Ľ˝ľżŔÁÂĂÄĹĆÇČÉĘËĚÍÎĎĐŃŇÓÔŐÖ×ŘŮÚŰÜÝŢßŕáâăäĺćçčéęëěíîďđńňóôőö÷řůúűüýţ˙\"\n  },\n  \"win1250\": \"windows1250\",\n  \"cp1250\": \"windows1250\",\n  \"windows1251\": {\n    \"type\": \"_sbcs\",\n    \"chars\": \"ЂЃ‚ѓ„…†‡€‰Љ‹ЊЌЋЏђ‘’“”•–—�™љ›њќћџ ЎўЈ¤Ґ¦§Ё©Є«¬­®Ї°±Ііґµ¶·ё№є»јЅѕїАБВГДЕЖЗИЙКЛМНОПРСТУФХЦЧШЩЪЫЬЭЮЯабвгдежзийклмнопрстуфхцчшщъыьэюя\"\n  },\n  \"win1251\": \"windows1251\",\n  \"cp1251\": \"windows1251\",\n  \"windows1252\": {\n    \"type\": \"_sbcs\",\n    \"chars\": \"€�‚ƒ„…†‡ˆ‰Š‹Œ�Ž��‘’“”•–—˜™š›œ�žŸ ¡¢£¤¥¦§¨©ª«¬­®¯°±²³´µ¶·¸¹º»¼½¾¿ÀÁÂÃÄÅÆÇÈÉÊËÌÍÎÏÐÑÒÓÔÕÖ×ØÙÚÛÜÝÞßàáâãäåæçèéêëìíîïðñòóôõö÷øùúûüýþÿ\"\n  },\n  \"win1252\": \"windows1252\",\n  \"cp1252\": \"windows1252\",\n  \"windows1253\": {\n    \"type\": \"_sbcs\",\n    \"chars\": \"€�‚ƒ„…†‡�‰�‹�����‘’“”•–—�™�›���� ΅Ά£¤¥¦§¨©�«¬­®―°±²³΄µ¶·ΈΉΊ»Ό½ΎΏΐΑΒΓΔΕΖΗΘΙΚΛΜΝΞΟΠΡ�ΣΤΥΦΧΨΩΪΫάέήίΰαβγδεζηθικλμνξοπρςστυφχψωϊϋόύώ�\"\n  },\n  \"win1253\": \"windows1253\",\n  \"cp1253\": \"windows1253\",\n  \"windows1254\": {\n    \"type\": \"_sbcs\",\n    \"chars\": \"€�‚ƒ„…†‡ˆ‰Š‹Œ����‘’“”•–—˜™š›œ��Ÿ ¡¢£¤¥¦§¨©ª«¬­®¯°±²³´µ¶·¸¹º»¼½¾¿ÀÁÂÃÄÅÆÇÈÉÊËÌÍÎÏĞÑÒÓÔÕÖ×ØÙÚÛÜİŞßàáâãäåæçèéêëìíîïğñòóôõö÷øùúûüışÿ\"\n  },\n  \"win1254\": \"windows1254\",\n  \"cp1254\": \"windows1254\",\n  \"windows1255\": {\n    \"type\": \"_sbcs\",\n    \"chars\": \"€�‚ƒ„…†‡ˆ‰�‹�����‘’“”•–—˜™�›���� ¡¢£₪¥¦§¨©×«¬­®¯°±²³´µ¶·¸¹÷»¼½¾¿ְֱֲֳִֵֶַָֹֺֻּֽ־ֿ׀ׁׂ׃װױײ׳״�������אבגדהוזחטיךכלםמןנסעףפץצקרשת��‎‏�\"\n  },\n  \"win1255\": \"windows1255\",\n  \"cp1255\": \"windows1255\",\n  \"windows1256\": {\n    \"type\": \"_sbcs\",\n    \"chars\": \"€پ‚ƒ„…†‡ˆ‰ٹ‹Œچژڈگ‘’“”•–—ک™ڑ›œ‌‍ں ،¢£¤¥¦§¨©ھ«¬­®¯°±²³´µ¶·¸¹؛»¼½¾؟ہءآأؤإئابةتثجحخدذرزسشصض×طظعغـفقكàلâمنهوçèéêëىيîïًٌٍَôُِ÷ّùْûü‎‏ے\"\n  },\n  \"win1256\": \"windows1256\",\n  \"cp1256\": \"windows1256\",\n  \"windows1257\": {\n    \"type\": \"_sbcs\",\n    \"chars\": \"€�‚�„…†‡�‰�‹�¨ˇ¸�‘’“”•–—�™�›�¯˛� �¢£¤�¦§Ø©Ŗ«¬­®Æ°±²³´µ¶·ø¹ŗ»¼½¾æĄĮĀĆÄÅĘĒČÉŹĖĢĶĪĻŠŃŅÓŌÕÖ×ŲŁŚŪÜŻŽßąįāćäåęēčéźėģķīļšńņóōõö÷ųłśūüżž˙\"\n  },\n  \"win1257\": \"windows1257\",\n  \"cp1257\": \"windows1257\",\n  \"windows1258\": {\n    \"type\": \"_sbcs\",\n    \"chars\": \"€�‚ƒ„…†‡ˆ‰�‹Œ����‘’“”•–—˜™�›œ��Ÿ ¡¢£¤¥¦§¨©ª«¬­®¯°±²³´µ¶·¸¹º»¼½¾¿ÀÁÂĂÄÅÆÇÈÉÊË̀ÍÎÏĐÑ̉ÓÔƠÖ×ØÙÚÛÜỮßàáâăäåæçèéêë́íîïđṇ̃óôơö÷øùúûüư₫ÿ\"\n  },\n  \"win1258\": \"windows1258\",\n  \"cp1258\": \"windows1258\",\n  \"iso88591\": {\n    \"type\": \"_sbcs\",\n    \"chars\": \" ¡¢£¤¥¦§¨©ª«¬­®¯°±²³´µ¶·¸¹º»¼½¾¿ÀÁÂÃÄÅÆÇÈÉÊËÌÍÎÏÐÑÒÓÔÕÖ×ØÙÚÛÜÝÞßàáâãäåæçèéêëìíîïðñòóôõö÷øùúûüýþÿ\"\n  },\n  \"cp28591\": \"iso88591\",\n  \"iso88592\": {\n    \"type\": \"_sbcs\",\n    \"chars\": \" Ą˘Ł¤ĽŚ§¨ŠŞŤŹ­ŽŻ°ą˛ł´ľśˇ¸šşťź˝žżŔÁÂĂÄĹĆÇČÉĘËĚÍÎĎĐŃŇÓÔŐÖ×ŘŮÚŰÜÝŢßŕáâăäĺćçčéęëěíîďđńňóôőö÷řůúűüýţ˙\"\n  },\n  \"cp28592\": \"iso88592\",\n  \"iso88593\": {\n    \"type\": \"_sbcs\",\n    \"chars\": \" Ħ˘£¤�Ĥ§¨İŞĞĴ­�Ż°ħ²³´µĥ·¸ışğĵ½�żÀÁÂ�ÄĊĈÇÈÉÊËÌÍÎÏ�ÑÒÓÔĠÖ×ĜÙÚÛÜŬŜßàáâ�äċĉçèéêëìíîï�ñòóôġö÷ĝùúûüŭŝ˙\"\n  },\n  \"cp28593\": \"iso88593\",\n  \"iso88594\": {\n    \"type\": \"_sbcs\",\n    \"chars\": \" ĄĸŖ¤ĨĻ§¨ŠĒĢŦ­Ž¯°ą˛ŗ´ĩļˇ¸šēģŧŊžŋĀÁÂÃÄÅÆĮČÉĘËĖÍÎĪĐŅŌĶÔÕÖ×ØŲÚÛÜŨŪßāáâãäåæįčéęëėíîīđņōķôõö÷øųúûüũū˙\"\n  },\n  \"cp28594\": \"iso88594\",\n  \"iso88595\": {\n    \"type\": \"_sbcs\",\n    \"chars\": \" ЁЂЃЄЅІЇЈЉЊЋЌ­ЎЏАБВГДЕЖЗИЙКЛМНОПРСТУФХЦЧШЩЪЫЬЭЮЯабвгдежзийклмнопрстуфхцчшщъыьэюя№ёђѓєѕіїјљњћќ§ўџ\"\n  },\n  \"cp28595\": \"iso88595\",\n  \"iso88596\": {\n    \"type\": \"_sbcs\",\n    \"chars\": \" ���¤�������،­�������������؛���؟�ءآأؤإئابةتثجحخدذرزسشصضطظعغ�����ـفقكلمنهوىيًٌٍَُِّْ�������������\"\n  },\n  \"cp28596\": \"iso88596\",\n  \"iso88597\": {\n    \"type\": \"_sbcs\",\n    \"chars\": \" ‘’£€₯¦§¨©ͺ«¬­�―°±²³΄΅Ά·ΈΉΊ»Ό½ΎΏΐΑΒΓΔΕΖΗΘΙΚΛΜΝΞΟΠΡ�ΣΤΥΦΧΨΩΪΫάέήίΰαβγδεζηθικλμνξοπρςστυφχψωϊϋόύώ�\"\n  },\n  \"cp28597\": \"iso88597\",\n  \"iso88598\": {\n    \"type\": \"_sbcs\",\n    \"chars\": \" �¢£¤¥¦§¨©×«¬­®¯°±²³´µ¶·¸¹÷»¼½¾��������������������������������‗אבגדהוזחטיךכלםמןנסעףפץצקרשת��‎‏�\"\n  },\n  \"cp28598\": \"iso88598\",\n  \"iso88599\": {\n    \"type\": \"_sbcs\",\n    \"chars\": \" ¡¢£¤¥¦§¨©ª«¬­®¯°±²³´µ¶·¸¹º»¼½¾¿ÀÁÂÃÄÅÆÇÈÉÊËÌÍÎÏĞÑÒÓÔÕÖ×ØÙÚÛÜİŞßàáâãäåæçèéêëìíîïğñòóôõö÷øùúûüışÿ\"\n  },\n  \"cp28599\": \"iso88599\",\n  \"iso885910\": {\n    \"type\": \"_sbcs\",\n    \"chars\": \" ĄĒĢĪĨĶ§ĻĐŠŦŽ­ŪŊ°ąēģīĩķ·ļđšŧž―ūŋĀÁÂÃÄÅÆĮČÉĘËĖÍÎÏÐŅŌÓÔÕÖŨØŲÚÛÜÝÞßāáâãäåæįčéęëėíîïðņōóôõöũøųúûüýþĸ\"\n  },\n  \"cp28600\": \"iso885910\",\n  \"iso885911\": {\n    \"type\": \"_sbcs\",\n    \"chars\": \" กขฃคฅฆงจฉชซฌญฎฏฐฑฒณดตถทธนบปผฝพฟภมยรฤลฦวศษสหฬอฮฯะัาำิีึืฺุู����฿เแโใไๅๆ็่้๊๋์ํ๎๏๐๑๒๓๔๕๖๗๘๙๚๛����\"\n  },\n  \"cp28601\": \"iso885911\",\n  \"iso885913\": {\n    \"type\": \"_sbcs\",\n    \"chars\": \" ”¢£¤„¦§Ø©Ŗ«¬­®Æ°±²³“µ¶·ø¹ŗ»¼½¾æĄĮĀĆÄÅĘĒČÉŹĖĢĶĪĻŠŃŅÓŌÕÖ×ŲŁŚŪÜŻŽßąįāćäåęēčéźėģķīļšńņóōõö÷ųłśūüżž’\"\n  },\n  \"cp28603\": \"iso885913\",\n  \"iso885914\": {\n    \"type\": \"_sbcs\",\n    \"chars\": \" Ḃḃ£ĊċḊ§Ẁ©ẂḋỲ­®ŸḞḟĠġṀṁ¶ṖẁṗẃṠỳẄẅṡÀÁÂÃÄÅÆÇÈÉÊËÌÍÎÏŴÑÒÓÔÕÖṪØÙÚÛÜÝŶßàáâãäåæçèéêëìíîïŵñòóôõöṫøùúûüýŷÿ\"\n  },\n  \"cp28604\": \"iso885914\",\n  \"iso885915\": {\n    \"type\": \"_sbcs\",\n    \"chars\": \" ¡¢£€¥Š§š©ª«¬­®¯°±²³Žµ¶·ž¹º»ŒœŸ¿ÀÁÂÃÄÅÆÇÈÉÊËÌÍÎÏÐÑÒÓÔÕÖ×ØÙÚÛÜÝÞßàáâãäåæçèéêëìíîïðñòóôõö÷øùúûüýþÿ\"\n  },\n  \"cp28605\": \"iso885915\",\n  \"iso885916\": {\n    \"type\": \"_sbcs\",\n    \"chars\": \" ĄąŁ€„Š§š©Ș«Ź­źŻ°±ČłŽ”¶·žčș»ŒœŸżÀÁÂĂÄĆÆÇÈÉÊËÌÍÎÏĐŃÒÓÔŐÖŚŰÙÚÛÜĘȚßàáâăäćæçèéêëìíîïđńòóôőöśűùúûüęțÿ\"\n  },\n  \"cp28606\": \"iso885916\",\n  \"cp437\": {\n    \"type\": \"_sbcs\",\n    \"chars\": \"ÇüéâäàåçêëèïîìÄÅÉæÆôöòûùÿÖÜ¢£¥₧ƒáíóúñÑªº¿⌐¬½¼¡«»░▒▓│┤╡╢╖╕╣║╗╝╜╛┐└┴┬├─┼╞╟╚╔╩╦╠═╬╧╨╤╥╙╘╒╓╫╪┘┌█▄▌▐▀αßΓπΣσµτΦΘΩδ∞φε∩≡±≥≤⌠⌡÷≈°∙·√ⁿ²■ \"\n  },\n  \"ibm437\": \"cp437\",\n  \"csibm437\": \"cp437\",\n  \"cp737\": {\n    \"type\": \"_sbcs\",\n    \"chars\": \"ΑΒΓΔΕΖΗΘΙΚΛΜΝΞΟΠΡΣΤΥΦΧΨΩαβγδεζηθικλμνξοπρσςτυφχψ░▒▓│┤╡╢╖╕╣║╗╝╜╛┐└┴┬├─┼╞╟╚╔╩╦╠═╬╧╨╤╥╙╘╒╓╫╪┘┌█▄▌▐▀ωάέήϊίόύϋώΆΈΉΊΌΎΏ±≥≤ΪΫ÷≈°∙·√ⁿ²■ \"\n  },\n  \"ibm737\": \"cp737\",\n  \"csibm737\": \"cp737\",\n  \"cp775\": {\n    \"type\": \"_sbcs\",\n    \"chars\": \"ĆüéāäģåćłēŖŗīŹÄÅÉæÆōöĢ¢ŚśÖÜø£Ø×¤ĀĪóŻżź”¦©®¬½¼Ł«»░▒▓│┤ĄČĘĖ╣║╗╝ĮŠ┐└┴┬├─┼ŲŪ╚╔╩╦╠═╬Žąčęėįšųūž┘┌█▄▌▐▀ÓßŌŃõÕµńĶķĻļņĒŅ’­±“¾¶§÷„°∙·¹³²■ \"\n  },\n  \"ibm775\": \"cp775\",\n  \"csibm775\": \"cp775\",\n  \"cp850\": {\n    \"type\": \"_sbcs\",\n    \"chars\": \"ÇüéâäàåçêëèïîìÄÅÉæÆôöòûùÿÖÜø£Ø×ƒáíóúñÑªº¿®¬½¼¡«»░▒▓│┤ÁÂÀ©╣║╗╝¢¥┐└┴┬├─┼ãÃ╚╔╩╦╠═╬¤ðÐÊËÈıÍÎÏ┘┌█▄¦Ì▀ÓßÔÒõÕµþÞÚÛÙýÝ¯´­±‗¾¶§÷¸°¨·¹³²■ \"\n  },\n  \"ibm850\": \"cp850\",\n  \"csibm850\": \"cp850\",\n  \"cp852\": {\n    \"type\": \"_sbcs\",\n    \"chars\": \"ÇüéâäůćçłëŐőîŹÄĆÉĹĺôöĽľŚśÖÜŤťŁ×čáíóúĄąŽžĘę¬źČş«»░▒▓│┤ÁÂĚŞ╣║╗╝Żż┐└┴┬├─┼Ăă╚╔╩╦╠═╬¤đĐĎËďŇÍÎě┘┌█▄ŢŮ▀ÓßÔŃńňŠšŔÚŕŰýÝţ´­˝˛ˇ˘§÷¸°¨˙űŘř■ \"\n  },\n  \"ibm852\": \"cp852\",\n  \"csibm852\": \"cp852\",\n  \"cp855\": {\n    \"type\": \"_sbcs\",\n    \"chars\": \"ђЂѓЃёЁєЄѕЅіІїЇјЈљЉњЊћЋќЌўЎџЏюЮъЪаАбБцЦдДеЕфФгГ«»░▒▓│┤хХиИ╣║╗╝йЙ┐└┴┬├─┼кК╚╔╩╦╠═╬¤лЛмМнНоОп┘┌█▄Пя▀ЯрРсСтТуУжЖвВьЬ№­ыЫзЗшШэЭщЩчЧ§■ \"\n  },\n  \"ibm855\": \"cp855\",\n  \"csibm855\": \"cp855\",\n  \"cp856\": {\n    \"type\": \"_sbcs\",\n    \"chars\": \"אבגדהוזחטיךכלםמןנסעףפץצקרשת�£�×����������®¬½¼�«»░▒▓│┤���©╣║╗╝¢¥┐└┴┬├─┼��╚╔╩╦╠═╬¤���������┘┌█▄¦�▀������µ�������¯´­±‗¾¶§÷¸°¨·¹³²■ \"\n  },\n  \"ibm856\": \"cp856\",\n  \"csibm856\": \"cp856\",\n  \"cp857\": {\n    \"type\": \"_sbcs\",\n    \"chars\": \"ÇüéâäàåçêëèïîıÄÅÉæÆôöòûùİÖÜø£ØŞşáíóúñÑĞğ¿®¬½¼¡«»░▒▓│┤ÁÂÀ©╣║╗╝¢¥┐└┴┬├─┼ãÃ╚╔╩╦╠═╬¤ºªÊËÈ�ÍÎÏ┘┌█▄¦Ì▀ÓßÔÒõÕµ�×ÚÛÙìÿ¯´­±�¾¶§÷¸°¨·¹³²■ \"\n  },\n  \"ibm857\": \"cp857\",\n  \"csibm857\": \"cp857\",\n  \"cp858\": {\n    \"type\": \"_sbcs\",\n    \"chars\": \"ÇüéâäàåçêëèïîìÄÅÉæÆôöòûùÿÖÜø£Ø×ƒáíóúñÑªº¿®¬½¼¡«»░▒▓│┤ÁÂÀ©╣║╗╝¢¥┐└┴┬├─┼ãÃ╚╔╩╦╠═╬¤ðÐÊËÈ€ÍÎÏ┘┌█▄¦Ì▀ÓßÔÒõÕµþÞÚÛÙýÝ¯´­±‗¾¶§÷¸°¨·¹³²■ \"\n  },\n  \"ibm858\": \"cp858\",\n  \"csibm858\": \"cp858\",\n  \"cp860\": {\n    \"type\": \"_sbcs\",\n    \"chars\": \"ÇüéâãàÁçêÊèÍÔìÃÂÉÀÈôõòÚùÌÕÜ¢£Ù₧ÓáíóúñÑªº¿Ò¬½¼¡«»░▒▓│┤╡╢╖╕╣║╗╝╜╛┐└┴┬├─┼╞╟╚╔╩╦╠═╬╧╨╤╥╙╘╒╓╫╪┘┌█▄▌▐▀αßΓπΣσµτΦΘΩδ∞φε∩≡±≥≤⌠⌡÷≈°∙·√ⁿ²■ \"\n  },\n  \"ibm860\": \"cp860\",\n  \"csibm860\": \"cp860\",\n  \"cp861\": {\n    \"type\": \"_sbcs\",\n    \"chars\": \"ÇüéâäàåçêëèÐðÞÄÅÉæÆôöþûÝýÖÜø£Ø₧ƒáíóúÁÍÓÚ¿⌐¬½¼¡«»░▒▓│┤╡╢╖╕╣║╗╝╜╛┐└┴┬├─┼╞╟╚╔╩╦╠═╬╧╨╤╥╙╘╒╓╫╪┘┌█▄▌▐▀αßΓπΣσµτΦΘΩδ∞φε∩≡±≥≤⌠⌡÷≈°∙·√ⁿ²■ \"\n  },\n  \"ibm861\": \"cp861\",\n  \"csibm861\": \"cp861\",\n  \"cp862\": {\n    \"type\": \"_sbcs\",\n    \"chars\": \"אבגדהוזחטיךכלםמןנסעףפץצקרשת¢£¥₧ƒáíóúñÑªº¿⌐¬½¼¡«»░▒▓│┤╡╢╖╕╣║╗╝╜╛┐└┴┬├─┼╞╟╚╔╩╦╠═╬╧╨╤╥╙╘╒╓╫╪┘┌█▄▌▐▀αßΓπΣσµτΦΘΩδ∞φε∩≡±≥≤⌠⌡÷≈°∙·√ⁿ²■ \"\n  },\n  \"ibm862\": \"cp862\",\n  \"csibm862\": \"cp862\",\n  \"cp863\": {\n    \"type\": \"_sbcs\",\n    \"chars\": \"ÇüéâÂà¶çêëèïî‗À§ÉÈÊôËÏûù¤ÔÜ¢£ÙÛƒ¦´óú¨¸³¯Î⌐¬½¼¾«»░▒▓│┤╡╢╖╕╣║╗╝╜╛┐└┴┬├─┼╞╟╚╔╩╦╠═╬╧╨╤╥╙╘╒╓╫╪┘┌█▄▌▐▀αßΓπΣσµτΦΘΩδ∞φε∩≡±≥≤⌠⌡÷≈°∙·√ⁿ²■ \"\n  },\n  \"ibm863\": \"cp863\",\n  \"csibm863\": \"cp863\",\n  \"cp864\": {\n    \"type\": \"_sbcs\",\n    \"chars\": \"\\u0000\\u0001\\u0002\\u0003\\u0004\\u0005\\u0006\\u0007\\b\\t\\n\\u000b\\f\\r\\u000e\\u000f\\u0010\\u0011\\u0012\\u0013\\u0014\\u0015\\u0016\\u0017\\u0018\\u0019\\u001a\\u001b\\u001c\\u001d\\u001e\\u001f !\\\"#$٪&'()*+,-./0123456789:;<=>?@ABCDEFGHIJKLMNOPQRSTUVWXYZ[\\\\]^_`abcdefghijklmnopqrstuvwxyz{|}~°·∙√▒─│┼┤┬├┴┐┌└┘β∞φ±½¼≈«»ﻷﻸ��ﻻﻼ� ­ﺂ£¤ﺄ��ﺎﺏﺕﺙ،ﺝﺡﺥ٠١٢٣٤٥٦٧٨٩ﻑ؛ﺱﺵﺹ؟¢ﺀﺁﺃﺅﻊﺋﺍﺑﺓﺗﺛﺟﺣﺧﺩﺫﺭﺯﺳﺷﺻﺿﻁﻅﻋﻏ¦¬÷×ﻉـﻓﻗﻛﻟﻣﻧﻫﻭﻯﻳﺽﻌﻎﻍﻡﹽّﻥﻩﻬﻰﻲﻐﻕﻵﻶﻝﻙﻱ■�\"\n  },\n  \"ibm864\": \"cp864\",\n  \"csibm864\": \"cp864\",\n  \"cp865\": {\n    \"type\": \"_sbcs\",\n    \"chars\": \"ÇüéâäàåçêëèïîìÄÅÉæÆôöòûùÿÖÜø£Ø₧ƒáíóúñÑªº¿⌐¬½¼¡«¤░▒▓│┤╡╢╖╕╣║╗╝╜╛┐└┴┬├─┼╞╟╚╔╩╦╠═╬╧╨╤╥╙╘╒╓╫╪┘┌█▄▌▐▀αßΓπΣσµτΦΘΩδ∞φε∩≡±≥≤⌠⌡÷≈°∙·√ⁿ²■ \"\n  },\n  \"ibm865\": \"cp865\",\n  \"csibm865\": \"cp865\",\n  \"cp866\": {\n    \"type\": \"_sbcs\",\n    \"chars\": \"АБВГДЕЖЗИЙКЛМНОПРСТУФХЦЧШЩЪЫЬЭЮЯабвгдежзийклмноп░▒▓│┤╡╢╖╕╣║╗╝╜╛┐└┴┬├─┼╞╟╚╔╩╦╠═╬╧╨╤╥╙╘╒╓╫╪┘┌█▄▌▐▀рстуфхцчшщъыьэюяЁёЄєЇїЎў°∙·√№¤■ \"\n  },\n  \"ibm866\": \"cp866\",\n  \"csibm866\": \"cp866\",\n  \"cp869\": {\n    \"type\": \"_sbcs\",\n    \"chars\": \"������Ά�·¬¦‘’Έ―ΉΊΪΌ��ΎΫ©Ώ²³ά£έήίϊΐόύΑΒΓΔΕΖΗ½ΘΙ«»░▒▓│┤ΚΛΜΝ╣║╗╝ΞΟ┐└┴┬├─┼ΠΡ╚╔╩╦╠═╬ΣΤΥΦΧΨΩαβγ┘┌█▄δε▀ζηθικλμνξοπρσςτ΄­±υφχ§ψ΅°¨ωϋΰώ■ \"\n  },\n  \"ibm869\": \"cp869\",\n  \"csibm869\": \"cp869\",\n  \"cp922\": {\n    \"type\": \"_sbcs\",\n    \"chars\": \" ¡¢£¤¥¦§¨©ª«¬­®‾°±²³´µ¶·¸¹º»¼½¾¿ÀÁÂÃÄÅÆÇÈÉÊËÌÍÎÏŠÑÒÓÔÕÖ×ØÙÚÛÜÝŽßàáâãäåæçèéêëìíîïšñòóôõö÷øùúûüýžÿ\"\n  },\n  \"ibm922\": \"cp922\",\n  \"csibm922\": \"cp922\",\n  \"cp1046\": {\n    \"type\": \"_sbcs\",\n    \"chars\": \"ﺈ×÷ﹱ■│─┐┌└┘ﹹﹻﹽﹿﹷﺊﻰﻳﻲﻎﻏﻐﻶﻸﻺﻼ ¤ﺋﺑﺗﺛﺟﺣ،­ﺧﺳ٠١٢٣٤٥٦٧٨٩ﺷ؛ﺻﺿﻊ؟ﻋءآأؤإئابةتثجحخدذرزسشصضطﻇعغﻌﺂﺄﺎﻓـفقكلمنهوىيًٌٍَُِّْﻗﻛﻟﻵﻷﻹﻻﻣﻧﻬﻩ�\"\n  },\n  \"ibm1046\": \"cp1046\",\n  \"csibm1046\": \"cp1046\",\n  \"cp1124\": {\n    \"type\": \"_sbcs\",\n    \"chars\": \" ЁЂҐЄЅІЇЈЉЊЋЌ­ЎЏАБВГДЕЖЗИЙКЛМНОПРСТУФХЦЧШЩЪЫЬЭЮЯабвгдежзийклмнопрстуфхцчшщъыьэюя№ёђґєѕіїјљњћќ§ўџ\"\n  },\n  \"ibm1124\": \"cp1124\",\n  \"csibm1124\": \"cp1124\",\n  \"cp1125\": {\n    \"type\": \"_sbcs\",\n    \"chars\": \"АБВГДЕЖЗИЙКЛМНОПРСТУФХЦЧШЩЪЫЬЭЮЯабвгдежзийклмноп░▒▓│┤╡╢╖╕╣║╗╝╜╛┐└┴┬├─┼╞╟╚╔╩╦╠═╬╧╨╤╥╙╘╒╓╫╪┘┌█▄▌▐▀рстуфхцчшщъыьэюяЁёҐґЄєІіЇї·√№¤■ \"\n  },\n  \"ibm1125\": \"cp1125\",\n  \"csibm1125\": \"cp1125\",\n  \"cp1129\": {\n    \"type\": \"_sbcs\",\n    \"chars\": \" ¡¢£¤¥¦§œ©ª«¬­®¯°±²³Ÿµ¶·Œ¹º»¼½¾¿ÀÁÂĂÄÅÆÇÈÉÊË̀ÍÎÏĐÑ̉ÓÔƠÖ×ØÙÚÛÜỮßàáâăäåæçèéêë́íîïđṇ̃óôơö÷øùúûüư₫ÿ\"\n  },\n  \"ibm1129\": \"cp1129\",\n  \"csibm1129\": \"cp1129\",\n  \"cp1133\": {\n    \"type\": \"_sbcs\",\n    \"chars\": \" ກຂຄງຈສຊຍດຕຖທນບປຜຝພຟມຢຣລວຫອຮ���ຯະາຳິີຶືຸູຼັົຽ���ເແໂໃໄ່້໊໋໌ໍໆ�ໜໝ₭����������������໐໑໒໓໔໕໖໗໘໙��¢¬¦�\"\n  },\n  \"ibm1133\": \"cp1133\",\n  \"csibm1133\": \"cp1133\",\n  \"cp1161\": {\n    \"type\": \"_sbcs\",\n    \"chars\": \"��������������������������������่กขฃคฅฆงจฉชซฌญฎฏฐฑฒณดตถทธนบปผฝพฟภมยรฤลฦวศษสหฬอฮฯะัาำิีึืฺุู้๊๋€฿เแโใไๅๆ็่้๊๋์ํ๎๏๐๑๒๓๔๕๖๗๘๙๚๛¢¬¦ \"\n  },\n  \"ibm1161\": \"cp1161\",\n  \"csibm1161\": \"cp1161\",\n  \"cp1162\": {\n    \"type\": \"_sbcs\",\n    \"chars\": \"€…‘’“”•–— กขฃคฅฆงจฉชซฌญฎฏฐฑฒณดตถทธนบปผฝพฟภมยรฤลฦวศษสหฬอฮฯะัาำิีึืฺุู����฿เแโใไๅๆ็่้๊๋์ํ๎๏๐๑๒๓๔๕๖๗๘๙๚๛����\"\n  },\n  \"ibm1162\": \"cp1162\",\n  \"csibm1162\": \"cp1162\",\n  \"cp1163\": {\n    \"type\": \"_sbcs\",\n    \"chars\": \" ¡¢£€¥¦§œ©ª«¬­®¯°±²³Ÿµ¶·Œ¹º»¼½¾¿ÀÁÂĂÄÅÆÇÈÉÊË̀ÍÎÏĐÑ̉ÓÔƠÖ×ØÙÚÛÜỮßàáâăäåæçèéêë́íîïđṇ̃óôơö÷øùúûüư₫ÿ\"\n  },\n  \"ibm1163\": \"cp1163\",\n  \"csibm1163\": \"cp1163\",\n  \"maccroatian\": {\n    \"type\": \"_sbcs\",\n    \"chars\": \"ÄÅÇÉÑÖÜáàâäãåçéèêëíìîïñóòôöõúùûü†°¢£§•¶ß®Š™´¨≠ŽØ∞±≤≥∆µ∂∑∏š∫ªºΩžø¿¡¬√ƒ≈Ć«Č… ÀÃÕŒœĐ—“”‘’÷◊�©⁄¤‹›Æ»–·‚„‰ÂćÁčÈÍÎÏÌÓÔđÒÚÛÙıˆ˜¯πË˚¸Êæˇ\"\n  },\n  \"maccyrillic\": {\n    \"type\": \"_sbcs\",\n    \"chars\": \"АБВГДЕЖЗИЙКЛМНОПРСТУФХЦЧШЩЪЫЬЭЮЯ†°¢£§•¶І®©™Ђђ≠Ѓѓ∞±≤≥іµ∂ЈЄєЇїЉљЊњјЅ¬√ƒ≈∆«»… ЋћЌќѕ–—“”‘’÷„ЎўЏџ№Ёёяабвгдежзийклмнопрстуфхцчшщъыьэю¤\"\n  },\n  \"macgreek\": {\n    \"type\": \"_sbcs\",\n    \"chars\": \"Ä¹²É³ÖÜ΅àâä΄¨çéèêë£™îï•½‰ôö¦­ùûü†ΓΔΘΛΞΠß®©ΣΪ§≠°·Α±≤≥¥ΒΕΖΗΙΚΜΦΫΨΩάΝ¬ΟΡ≈Τ«»… ΥΧΆΈœ–―“”‘’÷ΉΊΌΎέήίόΏύαβψδεφγηιξκλμνοπώρστθωςχυζϊϋΐΰ�\"\n  },\n  \"maciceland\": {\n    \"type\": \"_sbcs\",\n    \"chars\": \"ÄÅÇÉÑÖÜáàâäãåçéèêëíìîïñóòôöõúùûüÝ°¢£§•¶ß®©™´¨≠ÆØ∞±≤≥¥µ∂∑∏π∫ªºΩæø¿¡¬√ƒ≈∆«»… ÀÃÕŒœ–—“”‘’÷◊ÿŸ⁄¤ÐðÞþý·‚„‰ÂÊÁËÈÍÎÏÌÓÔ�ÒÚÛÙıˆ˜¯˘˙˚¸˝˛ˇ\"\n  },\n  \"macroman\": {\n    \"type\": \"_sbcs\",\n    \"chars\": \"ÄÅÇÉÑÖÜáàâäãåçéèêëíìîïñóòôöõúùûü†°¢£§•¶ß®©™´¨≠ÆØ∞±≤≥¥µ∂∑∏π∫ªºΩæø¿¡¬√ƒ≈∆«»… ÀÃÕŒœ–—“”‘’÷◊ÿŸ⁄¤‹›ﬁﬂ‡·‚„‰ÂÊÁËÈÍÎÏÌÓÔ�ÒÚÛÙıˆ˜¯˘˙˚¸˝˛ˇ\"\n  },\n  \"macromania\": {\n    \"type\": \"_sbcs\",\n    \"chars\": \"ÄÅÇÉÑÖÜáàâäãåçéèêëíìîïñóòôöõúùûü†°¢£§•¶ß®©™´¨≠ĂŞ∞±≤≥¥µ∂∑∏π∫ªºΩăş¿¡¬√ƒ≈∆«»… ÀÃÕŒœ–—“”‘’÷◊ÿŸ⁄¤‹›Ţţ‡·‚„‰ÂÊÁËÈÍÎÏÌÓÔ�ÒÚÛÙıˆ˜¯˘˙˚¸˝˛ˇ\"\n  },\n  \"macthai\": {\n    \"type\": \"_sbcs\",\n    \"chars\": \"«»…“”�•‘’� กขฃคฅฆงจฉชซฌญฎฏฐฑฒณดตถทธนบปผฝพฟภมยรฤลฦวศษสหฬอฮฯะัาำิีึืฺุู﻿​–—฿เแโใไๅๆ็่้๊๋์ํ™๏๐๑๒๓๔๕๖๗๘๙®©����\"\n  },\n  \"macturkish\": {\n    \"type\": \"_sbcs\",\n    \"chars\": \"ÄÅÇÉÑÖÜáàâäãåçéèêëíìîïñóòôöõúùûü†°¢£§•¶ß®©™´¨≠ÆØ∞±≤≥¥µ∂∑∏π∫ªºΩæø¿¡¬√ƒ≈∆«»… ÀÃÕŒœ–—“”‘’÷◊ÿŸĞğİıŞş‡·‚„‰ÂÊÁËÈÍÎÏÌÓÔ�ÒÚÛÙ�ˆ˜¯˘˙˚¸˝˛ˇ\"\n  },\n  \"macukraine\": {\n    \"type\": \"_sbcs\",\n    \"chars\": \"АБВГДЕЖЗИЙКЛМНОПРСТУФХЦЧШЩЪЫЬЭЮЯ†°Ґ£§•¶І®©™Ђђ≠Ѓѓ∞±≤≥іµґЈЄєЇїЉљЊњјЅ¬√ƒ≈∆«»… ЋћЌќѕ–—“”‘’÷„ЎўЏџ№Ёёяабвгдежзийклмнопрстуфхцчшщъыьэю¤\"\n  },\n  \"koi8r\": {\n    \"type\": \"_sbcs\",\n    \"chars\": \"─│┌┐└┘├┤┬┴┼▀▄█▌▐░▒▓⌠■∙√≈≤≥ ⌡°²·÷═║╒ё╓╔╕╖╗╘╙╚╛╜╝╞╟╠╡Ё╢╣╤╥╦╧╨╩╪╫╬©юабцдефгхийклмнопярстужвьызшэщчъЮАБЦДЕФГХИЙКЛМНОПЯРСТУЖВЬЫЗШЭЩЧЪ\"\n  },\n  \"koi8u\": {\n    \"type\": \"_sbcs\",\n    \"chars\": \"─│┌┐└┘├┤┬┴┼▀▄█▌▐░▒▓⌠■∙√≈≤≥ ⌡°²·÷═║╒ёє╔ії╗╘╙╚╛ґ╝╞╟╠╡ЁЄ╣ІЇ╦╧╨╩╪Ґ╬©юабцдефгхийклмнопярстужвьызшэщчъЮАБЦДЕФГХИЙКЛМНОПЯРСТУЖВЬЫЗШЭЩЧЪ\"\n  },\n  \"koi8ru\": {\n    \"type\": \"_sbcs\",\n    \"chars\": \"─│┌┐└┘├┤┬┴┼▀▄█▌▐░▒▓⌠■∙√≈≤≥ ⌡°²·÷═║╒ёє╔ії╗╘╙╚╛ґў╞╟╠╡ЁЄ╣ІЇ╦╧╨╩╪ҐЎ©юабцдефгхийклмнопярстужвьызшэщчъЮАБЦДЕФГХИЙКЛМНОПЯРСТУЖВЬЫЗШЭЩЧЪ\"\n  },\n  \"koi8t\": {\n    \"type\": \"_sbcs\",\n    \"chars\": \"қғ‚Ғ„…†‡�‰ҳ‹ҲҷҶ�Қ‘’“”•–—�™�›�����ӯӮё¤ӣ¦§���«¬­®�°±²Ё�Ӣ¶·�№�»���©юабцдефгхийклмнопярстужвьызшэщчъЮАБЦДЕФГХИЙКЛМНОПЯРСТУЖВЬЫЗШЭЩЧЪ\"\n  },\n  \"armscii8\": {\n    \"type\": \"_sbcs\",\n    \"chars\": \" �և։)(»«—.՝,-֊…՜՛՞ԱաԲբԳգԴդԵեԶզԷէԸըԹթԺժԻիԼլԽխԾծԿկՀհՁձՂղՃճՄմՅյՆնՇշՈոՉչՊպՋջՌռՍսՎվՏտՐրՑցՒւՓփՔքՕօՖֆ՚�\"\n  },\n  \"rk1048\": {\n    \"type\": \"_sbcs\",\n    \"chars\": \"ЂЃ‚ѓ„…†‡€‰Љ‹ЊҚҺЏђ‘’“”•–—�™љ›њқһџ ҰұӘ¤Ө¦§Ё©Ғ«¬­®Ү°±Ііөµ¶·ё№ғ»әҢңүАБВГДЕЖЗИЙКЛМНОПРСТУФХЦЧШЩЪЫЬЭЮЯабвгдежзийклмнопрстуфхцчшщъыьэюя\"\n  },\n  \"tcvn\": {\n    \"type\": \"_sbcs\",\n    \"chars\": \"\\u0000ÚỤ\\u0003ỪỬỮ\\u0007\\b\\t\\n\\u000b\\f\\r\\u000e\\u000f\\u0010ỨỰỲỶỸÝỴ\\u0018\\u0019\\u001a\\u001b\\u001c\\u001d\\u001e\\u001f !\\\"#$%&'()*+,-./0123456789:;<=>?@ABCDEFGHIJKLMNOPQRSTUVWXYZ[\\\\]^_`abcdefghijklmnopqrstuvwxyz{|}~ÀẢÃÁẠẶẬÈẺẼÉẸỆÌỈĨÍỊÒỎÕÓỌỘỜỞỠỚỢÙỦŨ ĂÂÊÔƠƯĐăâêôơưđẶ̀̀̉̃́àảãáạẲằẳẵắẴẮẦẨẪẤỀặầẩẫấậèỂẻẽéẹềểễếệìỉỄẾỒĩíịòỔỏõóọồổỗốộờởỡớợùỖủũúụừửữứựỳỷỹýỵỐ\"\n  },\n  \"georgianacademy\": {\n    \"type\": \"_sbcs\",\n    \"chars\": \"‚ƒ„…†‡ˆ‰Š‹Œ‘’“”•–—˜™š›œŸ ¡¢£¤¥¦§¨©ª«¬­®¯°±²³´µ¶·¸¹º»¼½¾¿აბგდევზთიკლმნოპჟრსტუფქღყშჩცძწჭხჯჰჱჲჳჴჵჶçèéêëìíîïðñòóôõö÷øùúûüýþÿ\"\n  },\n  \"georgianps\": {\n    \"type\": \"_sbcs\",\n    \"chars\": \"‚ƒ„…†‡ˆ‰Š‹Œ‘’“”•–—˜™š›œŸ ¡¢£¤¥¦§¨©ª«¬­®¯°±²³´µ¶·¸¹º»¼½¾¿აბგდევზჱთიკლმნჲოპჟრსტჳუფქღყშჩცძწჭხჴჯჰჵæçèéêëìíîïðñòóôõö÷øùúûüýþÿ\"\n  },\n  \"pt154\": {\n    \"type\": \"_sbcs\",\n    \"chars\": \"ҖҒӮғ„…ҶҮҲүҠӢҢҚҺҸҗ‘’“”•–—ҳҷҡӣңқһҹ ЎўЈӨҘҰ§Ё©Ә«¬ӯ®Ҝ°ұІіҙө¶·ё№ә»јҪҫҝАБВГДЕЖЗИЙКЛМНОПРСТУФХЦЧШЩЪЫЬЭЮЯабвгдежзийклмнопрстуфхцчшщъыьэюя\"\n  },\n  \"viscii\": {\n    \"type\": \"_sbcs\",\n    \"chars\": \"\\u0000\\u0001Ẳ\\u0003\\u0004ẴẪ\\u0007\\b\\t\\n\\u000b\\f\\r\\u000e\\u000f\\u0010\\u0011\\u0012\\u0013Ỷ\\u0015\\u0016\\u0017\\u0018Ỹ\\u001a\\u001b\\u001c\\u001dỴ\\u001f !\\\"#$%&'()*+,-./0123456789:;<=>?@ABCDEFGHIJKLMNOPQRSTUVWXYZ[\\\\]^_`abcdefghijklmnopqrstuvwxyz{|}~ẠẮẰẶẤẦẨẬẼẸẾỀỂỄỆỐỒỔỖỘỢỚỜỞỊỎỌỈỦŨỤỲÕắằặấầẩậẽẹếềểễệốồổỗỠƠộờởịỰỨỪỬơớƯÀÁÂÃẢĂẳẵÈÉÊẺÌÍĨỳĐứÒÓÔạỷừửÙÚỹỵÝỡưàáâãảăữẫèéêẻìíĩỉđựòóôõỏọụùúũủýợỮ\"\n  },\n  \"iso646cn\": {\n    \"type\": \"_sbcs\",\n    \"chars\": \"\\u0000\\u0001\\u0002\\u0003\\u0004\\u0005\\u0006\\u0007\\b\\t\\n\\u000b\\f\\r\\u000e\\u000f\\u0010\\u0011\\u0012\\u0013\\u0014\\u0015\\u0016\\u0017\\u0018\\u0019\\u001a\\u001b\\u001c\\u001d\\u001e\\u001f !\\\"#¥%&'()*+,-./0123456789:;<=>?@ABCDEFGHIJKLMNOPQRSTUVWXYZ[\\\\]^_`abcdefghijklmnopqrstuvwxyz{|}‾��������������������������������������������������������������������������������������������������������������������������������\"\n  },\n  \"iso646jp\": {\n    \"type\": \"_sbcs\",\n    \"chars\": \"\\u0000\\u0001\\u0002\\u0003\\u0004\\u0005\\u0006\\u0007\\b\\t\\n\\u000b\\f\\r\\u000e\\u000f\\u0010\\u0011\\u0012\\u0013\\u0014\\u0015\\u0016\\u0017\\u0018\\u0019\\u001a\\u001b\\u001c\\u001d\\u001e\\u001f !\\\"#$%&'()*+,-./0123456789:;<=>?@ABCDEFGHIJKLMNOPQRSTUVWXYZ[¥]^_`abcdefghijklmnopqrstuvwxyz{|}‾��������������������������������������������������������������������������������������������������������������������������������\"\n  },\n  \"hproman8\": {\n    \"type\": \"_sbcs\",\n    \"chars\": \" ÀÂÈÊËÎÏ´ˋˆ¨˜ÙÛ₤¯Ýý°ÇçÑñ¡¿¤£¥§ƒ¢âêôûáéóúàèòùäëöüÅîØÆåíøæÄìÖÜÉïßÔÁÃãÐðÍÌÓÒÕõŠšÚŸÿÞþ·µ¶¾—¼½ªº«■»±�\"\n  },\n  \"macintosh\": {\n    \"type\": \"_sbcs\",\n    \"chars\": \"ÄÅÇÉÑÖÜáàâäãåçéèêëíìîïñóòôöõúùûü†°¢£§•¶ß®©™´¨≠ÆØ∞±≤≥¥µ∂∑∏π∫ªºΩæø¿¡¬√ƒ≈∆«»… ÀÃÕŒœ–—“”‘’÷◊ÿŸ⁄¤‹›ﬁﬂ‡·‚„‰ÂÊÁËÈÍÎÏÌÓÔ�ÒÚÛÙıˆ˜¯˘˙˚¸˝˛ˇ\"\n  },\n  \"ascii\": {\n    \"type\": \"_sbcs\",\n    \"chars\": \"��������������������������������������������������������������������������������������������������������������������������������\"\n  },\n  \"tis620\": {\n    \"type\": \"_sbcs\",\n    \"chars\": \"���������������������������������กขฃคฅฆงจฉชซฌญฎฏฐฑฒณดตถทธนบปผฝพฟภมยรฤลฦวศษสหฬอฮฯะัาำิีึืฺุู����฿เแโใไๅๆ็่้๊๋์ํ๎๏๐๑๒๓๔๕๖๗๘๙๚๛����\"\n  }\n}"], "names": [], "mappings": "AAAA;AAEA,sGAAsG;AACtG,OAAO,OAAO,GAAG;IACf,OAAO;IACP,OAAO;IACP,OAAO;IACP,OAAO;IACP,OAAO;IACP,OAAO;IACP,OAAO;IACP,OAAO;IACP,OAAO;IACP,OAAO;IACP,OAAO;IACP,OAAO;IACP,OAAO;IACP,OAAO;IACP,OAAO;IACP,OAAO;IACP,OAAO;IACP,OAAO;IACP,OAAO;IACP,QAAQ;IACR,QAAQ;IACR,QAAQ;IACR,QAAQ;IACR,QAAQ;IACR,QAAQ;IACR,QAAQ;IACR,QAAQ;IACR,QAAQ;IACR,QAAQ;IACR,QAAQ;IACR,QAAQ;IACR,QAAQ;IACR,QAAQ;IACR,QAAQ;IACR,QAAQ;IACR,QAAQ;IACR,SAAS;IACT,SAAS;IACT,SAAS;IACT,SAAS;IACT,SAAS;IACT,SAAS;IACT,SAAS;IACT,SAAS;IACT,SAAS;IACT,SAAS;IACT,SAAS;IACT,SAAS;IACT,SAAS;IACT,SAAS;IACT,SAAS;IACT,cAAc;QACZ,QAAQ;QACR,SAAS;IACX;IACA,UAAU;IACV,SAAS;IACT,eAAe;QACb,QAAQ;QACR,SAAS;IACX;IACA,WAAW;IACX,UAAU;IACV,eAAe;QACb,QAAQ;QACR,SAAS;IACX;IACA,WAAW;IACX,UAAU;IACV,eAAe;QACb,QAAQ;QACR,SAAS;IACX;IACA,WAAW;IACX,UAAU;IACV,eAAe;QACb,QAAQ;QACR,SAAS;IACX;IACA,WAAW;IACX,UAAU;IACV,eAAe;QACb,QAAQ;QACR,SAAS;IACX;IACA,WAAW;IACX,UAAU;IACV,eAAe;QACb,QAAQ;QACR,SAAS;IACX;IACA,WAAW;IACX,UAAU;IACV,eAAe;QACb,QAAQ;QACR,SAAS;IACX;IACA,WAAW;IACX,UAAU;IACV,eAAe;QACb,QAAQ;QACR,SAAS;IACX;IACA,WAAW;IACX,UAAU;IACV,eAAe;QACb,QAAQ;QACR,SAAS;IACX;IACA,WAAW;IACX,UAAU;IACV,YAAY;QACV,QAAQ;QACR,SAAS;IACX;IACA,WAAW;IACX,YAAY;QACV,QAAQ;QACR,SAAS;IACX;IACA,WAAW;IACX,YAAY;QACV,QAAQ;QACR,SAAS;IACX;IACA,WAAW;IACX,YAAY;QACV,QAAQ;QACR,SAAS;IACX;IACA,WAAW;IACX,YAAY;QACV,QAAQ;QACR,SAAS;IACX;IACA,WAAW;IACX,YAAY;QACV,QAAQ;QACR,SAAS;IACX;IACA,WAAW;IACX,YAAY;QACV,QAAQ;QACR,SAAS;IACX;IACA,WAAW;IACX,YAAY;QACV,QAAQ;QACR,SAAS;IACX;IACA,WAAW;IACX,YAAY;QACV,QAAQ;QACR,SAAS;IACX;IACA,WAAW;IACX,aAAa;QACX,QAAQ;QACR,SAAS;IACX;IACA,WAAW;IACX,aAAa;QACX,QAAQ;QACR,SAAS;IACX;IACA,WAAW;IACX,aAAa;QACX,QAAQ;QACR,SAAS;IACX;IACA,WAAW;IACX,aAAa;QACX,QAAQ;QACR,SAAS;IACX;IACA,WAAW;IACX,aAAa;QACX,QAAQ;QACR,SAAS;IACX;IACA,WAAW;IACX,aAAa;QACX,QAAQ;QACR,SAAS;IACX;IACA,WAAW;IACX,SAAS;QACP,QAAQ;QACR,SAAS;IACX;IACA,UAAU;IACV,YAAY;IACZ,SAAS;QACP,QAAQ;QACR,SAAS;IACX;IACA,UAAU;IACV,YAAY;IACZ,SAAS;QACP,QAAQ;QACR,SAAS;IACX;IACA,UAAU;IACV,YAAY;IACZ,SAAS;QACP,QAAQ;QACR,SAAS;IACX;IACA,UAAU;IACV,YAAY;IACZ,SAAS;QACP,QAAQ;QACR,SAAS;IACX;IACA,UAAU;IACV,YAAY;IACZ,SAAS;QACP,QAAQ;QACR,SAAS;IACX;IACA,UAAU;IACV,YAAY;IACZ,SAAS;QACP,QAAQ;QACR,SAAS;IACX;IACA,UAAU;IACV,YAAY;IACZ,SAAS;QACP,QAAQ;QACR,SAAS;IACX;IACA,UAAU;IACV,YAAY;IACZ,SAAS;QACP,QAAQ;QACR,SAAS;IACX;IACA,UAAU;IACV,YAAY;IACZ,SAAS;QACP,QAAQ;QACR,SAAS;IACX;IACA,UAAU;IACV,YAAY;IACZ,SAAS;QACP,QAAQ;QACR,SAAS;IACX;IACA,UAAU;IACV,YAAY;IACZ,SAAS;QACP,QAAQ;QACR,SAAS;IACX;IACA,UAAU;IACV,YAAY;IACZ,SAAS;QACP,QAAQ;QACR,SAAS;IACX;IACA,UAAU;IACV,YAAY;IACZ,SAAS;QACP,QAAQ;QACR,SAAS;IACX;IACA,UAAU;IACV,YAAY;IACZ,SAAS;QACP,QAAQ;QACR,SAAS;IACX;IACA,UAAU;IACV,YAAY;IACZ,SAAS;QACP,QAAQ;QACR,SAAS;IACX;IACA,UAAU;IACV,YAAY;IACZ,SAAS;QACP,QAAQ;QACR,SAAS;IACX;IACA,UAAU;IACV,YAAY;IACZ,SAAS;QACP,QAAQ;QACR,SAAS;IACX;IACA,UAAU;IACV,YAAY;IACZ,UAAU;QACR,QAAQ;QACR,SAAS;IACX;IACA,WAAW;IACX,aAAa;IACb,UAAU;QACR,QAAQ;QACR,SAAS;IACX;IACA,WAAW;IACX,aAAa;IACb,UAAU;QACR,QAAQ;QACR,SAAS;IACX;IACA,WAAW;IACX,aAAa;IACb,UAAU;QACR,QAAQ;QACR,SAAS;IACX;IACA,WAAW;IACX,aAAa;IACb,UAAU;QACR,QAAQ;QACR,SAAS;IACX;IACA,WAAW;IACX,aAAa;IACb,UAAU;QACR,QAAQ;QACR,SAAS;IACX;IACA,WAAW;IACX,aAAa;IACb,UAAU;QACR,QAAQ;QACR,SAAS;IACX;IACA,WAAW;IACX,aAAa;IACb,UAAU;QACR,QAAQ;QACR,SAAS;IACX;IACA,WAAW;IACX,aAAa;IACb,eAAe;QACb,QAAQ;QACR,SAAS;IACX;IACA,eAAe;QACb,QAAQ;QACR,SAAS;IACX;IACA,YAAY;QACV,QAAQ;QACR,SAAS;IACX;IACA,cAAc;QACZ,QAAQ;QACR,SAAS;IACX;IACA,YAAY;QACV,QAAQ;QACR,SAAS;IACX;IACA,cAAc;QACZ,QAAQ;QACR,SAAS;IACX;IACA,WAAW;QACT,QAAQ;QACR,SAAS;IACX;IACA,cAAc;QACZ,QAAQ;QACR,SAAS;IACX;IACA,cAAc;QACZ,QAAQ;QACR,SAAS;IACX;IACA,SAAS;QACP,QAAQ;QACR,SAAS;IACX;IACA,SAAS;QACP,QAAQ;QACR,SAAS;IACX;IACA,UAAU;QACR,QAAQ;QACR,SAAS;IACX;IACA,SAAS;QACP,QAAQ;QACR,SAAS;IACX;IACA,YAAY;QACV,QAAQ;QACR,SAAS;IACX;IACA,UAAU;QACR,QAAQ;QACR,SAAS;IACX;IACA,QAAQ;QACN,QAAQ;QACR,SAAS;IACX;IACA,mBAAmB;QACjB,QAAQ;QACR,SAAS;IACX;IACA,cAAc;QACZ,QAAQ;QACR,SAAS;IACX;IACA,SAAS;QACP,QAAQ;QACR,SAAS;IACX;IACA,UAAU;QACR,QAAQ;QACR,SAAS;IACX;IACA,YAAY;QACV,QAAQ;QACR,SAAS;IACX;IACA,YAAY;QACV,QAAQ;QACR,SAAS;IACX;IACA,YAAY;QACV,QAAQ;QACR,SAAS;IACX;IACA,aAAa;QACX,QAAQ;QACR,SAAS;IACX;IACA,SAAS;QACP,QAAQ;QACR,SAAS;IACX;IACA,UAAU;QACR,QAAQ;QACR,SAAS;IACX;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1484, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/New%20folder%20%2825%29/streamzen/node_modules/iconv-lite/encodings/dbcs-codec.js"], "sourcesContent": ["\"use strict\";\nvar Buffer = require(\"safer-buffer\").Buffer;\n\n// Multibyte codec. In this scheme, a character is represented by 1 or more bytes.\n// Our codec supports UTF-16 surrogates, extensions for GB18030 and unicode sequences.\n// To save memory and loading time, we read table files only when requested.\n\nexports._dbcs = DBCSCodec;\n\nvar UNASSIGNED = -1,\n    GB18030_CODE = -2,\n    SEQ_START  = -10,\n    NODE_START = -1000,\n    UNASSIGNED_NODE = new Array(0x100),\n    DEF_CHAR = -1;\n\nfor (var i = 0; i < 0x100; i++)\n    UNASSIGNED_NODE[i] = UNASSIGNED;\n\n\n// Class DBCSCodec reads and initializes mapping tables.\nfunction DBCSCodec(codecOptions, iconv) {\n    this.encodingName = codecOptions.encodingName;\n    if (!codecOptions)\n        throw new Error(\"DBCS codec is called without the data.\")\n    if (!codecOptions.table)\n        throw new Error(\"Encoding '\" + this.encodingName + \"' has no data.\");\n\n    // Load tables.\n    var mappingTable = codecOptions.table();\n\n\n    // Decode tables: MBCS -> Unicode.\n\n    // decodeTables is a trie, encoded as an array of arrays of integers. Internal arrays are trie nodes and all have len = 256.\n    // Trie root is decodeTables[0].\n    // Values: >=  0 -> unicode character code. can be > 0xFFFF\n    //         == UNASSIGNED -> unknown/unassigned sequence.\n    //         == GB18030_CODE -> this is the end of a GB18030 4-byte sequence.\n    //         <= NODE_START -> index of the next node in our trie to process next byte.\n    //         <= SEQ_START  -> index of the start of a character code sequence, in decodeTableSeq.\n    this.decodeTables = [];\n    this.decodeTables[0] = UNASSIGNED_NODE.slice(0); // Create root node.\n\n    // Sometimes a MBCS char corresponds to a sequence of unicode chars. We store them as arrays of integers here. \n    this.decodeTableSeq = [];\n\n    // Actual mapping tables consist of chunks. Use them to fill up decode tables.\n    for (var i = 0; i < mappingTable.length; i++)\n        this._addDecodeChunk(mappingTable[i]);\n\n    // Load & create GB18030 tables when needed.\n    if (typeof codecOptions.gb18030 === 'function') {\n        this.gb18030 = codecOptions.gb18030(); // Load GB18030 ranges.\n\n        // Add GB18030 common decode nodes.\n        var commonThirdByteNodeIdx = this.decodeTables.length;\n        this.decodeTables.push(UNASSIGNED_NODE.slice(0));\n\n        var commonFourthByteNodeIdx = this.decodeTables.length;\n        this.decodeTables.push(UNASSIGNED_NODE.slice(0));\n\n        // Fill out the tree\n        var firstByteNode = this.decodeTables[0];\n        for (var i = 0x81; i <= 0xFE; i++) {\n            var secondByteNode = this.decodeTables[NODE_START - firstByteNode[i]];\n            for (var j = 0x30; j <= 0x39; j++) {\n                if (secondByteNode[j] === UNASSIGNED) {\n                    secondByteNode[j] = NODE_START - commonThirdByteNodeIdx;\n                } else if (secondByteNode[j] > NODE_START) {\n                    throw new Error(\"gb18030 decode tables conflict at byte 2\");\n                }\n\n                var thirdByteNode = this.decodeTables[NODE_START - secondByteNode[j]];\n                for (var k = 0x81; k <= 0xFE; k++) {\n                    if (thirdByteNode[k] === UNASSIGNED) {\n                        thirdByteNode[k] = NODE_START - commonFourthByteNodeIdx;\n                    } else if (thirdByteNode[k] === NODE_START - commonFourthByteNodeIdx) {\n                        continue;\n                    } else if (thirdByteNode[k] > NODE_START) {\n                        throw new Error(\"gb18030 decode tables conflict at byte 3\");\n                    }\n\n                    var fourthByteNode = this.decodeTables[NODE_START - thirdByteNode[k]];\n                    for (var l = 0x30; l <= 0x39; l++) {\n                        if (fourthByteNode[l] === UNASSIGNED)\n                            fourthByteNode[l] = GB18030_CODE;\n                    }\n                }\n            }\n        }\n    }\n\n    this.defaultCharUnicode = iconv.defaultCharUnicode;\n\n    \n    // Encode tables: Unicode -> DBCS.\n\n    // `encodeTable` is array mapping from unicode char to encoded char. All its values are integers for performance.\n    // Because it can be sparse, it is represented as array of buckets by 256 chars each. Bucket can be null.\n    // Values: >=  0 -> it is a normal char. Write the value (if <=256 then 1 byte, if <=65536 then 2 bytes, etc.).\n    //         == UNASSIGNED -> no conversion found. Output a default char.\n    //         <= SEQ_START  -> it's an index in encodeTableSeq, see below. The character starts a sequence.\n    this.encodeTable = [];\n    \n    // `encodeTableSeq` is used when a sequence of unicode characters is encoded as a single code. We use a tree of\n    // objects where keys correspond to characters in sequence and leafs are the encoded dbcs values. A special DEF_CHAR key\n    // means end of sequence (needed when one sequence is a strict subsequence of another).\n    // Objects are kept separately from encodeTable to increase performance.\n    this.encodeTableSeq = [];\n\n    // Some chars can be decoded, but need not be encoded.\n    var skipEncodeChars = {};\n    if (codecOptions.encodeSkipVals)\n        for (var i = 0; i < codecOptions.encodeSkipVals.length; i++) {\n            var val = codecOptions.encodeSkipVals[i];\n            if (typeof val === 'number')\n                skipEncodeChars[val] = true;\n            else\n                for (var j = val.from; j <= val.to; j++)\n                    skipEncodeChars[j] = true;\n        }\n        \n    // Use decode trie to recursively fill out encode tables.\n    this._fillEncodeTable(0, 0, skipEncodeChars);\n\n    // Add more encoding pairs when needed.\n    if (codecOptions.encodeAdd) {\n        for (var uChar in codecOptions.encodeAdd)\n            if (Object.prototype.hasOwnProperty.call(codecOptions.encodeAdd, uChar))\n                this._setEncodeChar(uChar.charCodeAt(0), codecOptions.encodeAdd[uChar]);\n    }\n\n    this.defCharSB  = this.encodeTable[0][iconv.defaultCharSingleByte.charCodeAt(0)];\n    if (this.defCharSB === UNASSIGNED) this.defCharSB = this.encodeTable[0]['?'];\n    if (this.defCharSB === UNASSIGNED) this.defCharSB = \"?\".charCodeAt(0);\n}\n\nDBCSCodec.prototype.encoder = DBCSEncoder;\nDBCSCodec.prototype.decoder = DBCSDecoder;\n\n// Decoder helpers\nDBCSCodec.prototype._getDecodeTrieNode = function(addr) {\n    var bytes = [];\n    for (; addr > 0; addr >>>= 8)\n        bytes.push(addr & 0xFF);\n    if (bytes.length == 0)\n        bytes.push(0);\n\n    var node = this.decodeTables[0];\n    for (var i = bytes.length-1; i > 0; i--) { // Traverse nodes deeper into the trie.\n        var val = node[bytes[i]];\n\n        if (val == UNASSIGNED) { // Create new node.\n            node[bytes[i]] = NODE_START - this.decodeTables.length;\n            this.decodeTables.push(node = UNASSIGNED_NODE.slice(0));\n        }\n        else if (val <= NODE_START) { // Existing node.\n            node = this.decodeTables[NODE_START - val];\n        }\n        else\n            throw new Error(\"Overwrite byte in \" + this.encodingName + \", addr: \" + addr.toString(16));\n    }\n    return node;\n}\n\n\nDBCSCodec.prototype._addDecodeChunk = function(chunk) {\n    // First element of chunk is the hex mbcs code where we start.\n    var curAddr = parseInt(chunk[0], 16);\n\n    // Choose the decoding node where we'll write our chars.\n    var writeTable = this._getDecodeTrieNode(curAddr);\n    curAddr = curAddr & 0xFF;\n\n    // Write all other elements of the chunk to the table.\n    for (var k = 1; k < chunk.length; k++) {\n        var part = chunk[k];\n        if (typeof part === \"string\") { // String, write as-is.\n            for (var l = 0; l < part.length;) {\n                var code = part.charCodeAt(l++);\n                if (0xD800 <= code && code < 0xDC00) { // Decode surrogate\n                    var codeTrail = part.charCodeAt(l++);\n                    if (0xDC00 <= codeTrail && codeTrail < 0xE000)\n                        writeTable[curAddr++] = 0x10000 + (code - 0xD800) * 0x400 + (codeTrail - 0xDC00);\n                    else\n                        throw new Error(\"Incorrect surrogate pair in \"  + this.encodingName + \" at chunk \" + chunk[0]);\n                }\n                else if (0x0FF0 < code && code <= 0x0FFF) { // Character sequence (our own encoding used)\n                    var len = 0xFFF - code + 2;\n                    var seq = [];\n                    for (var m = 0; m < len; m++)\n                        seq.push(part.charCodeAt(l++)); // Simple variation: don't support surrogates or subsequences in seq.\n\n                    writeTable[curAddr++] = SEQ_START - this.decodeTableSeq.length;\n                    this.decodeTableSeq.push(seq);\n                }\n                else\n                    writeTable[curAddr++] = code; // Basic char\n            }\n        } \n        else if (typeof part === \"number\") { // Integer, meaning increasing sequence starting with prev character.\n            var charCode = writeTable[curAddr - 1] + 1;\n            for (var l = 0; l < part; l++)\n                writeTable[curAddr++] = charCode++;\n        }\n        else\n            throw new Error(\"Incorrect type '\" + typeof part + \"' given in \"  + this.encodingName + \" at chunk \" + chunk[0]);\n    }\n    if (curAddr > 0xFF)\n        throw new Error(\"Incorrect chunk in \"  + this.encodingName + \" at addr \" + chunk[0] + \": too long\" + curAddr);\n}\n\n// Encoder helpers\nDBCSCodec.prototype._getEncodeBucket = function(uCode) {\n    var high = uCode >> 8; // This could be > 0xFF because of astral characters.\n    if (this.encodeTable[high] === undefined)\n        this.encodeTable[high] = UNASSIGNED_NODE.slice(0); // Create bucket on demand.\n    return this.encodeTable[high];\n}\n\nDBCSCodec.prototype._setEncodeChar = function(uCode, dbcsCode) {\n    var bucket = this._getEncodeBucket(uCode);\n    var low = uCode & 0xFF;\n    if (bucket[low] <= SEQ_START)\n        this.encodeTableSeq[SEQ_START-bucket[low]][DEF_CHAR] = dbcsCode; // There's already a sequence, set a single-char subsequence of it.\n    else if (bucket[low] == UNASSIGNED)\n        bucket[low] = dbcsCode;\n}\n\nDBCSCodec.prototype._setEncodeSequence = function(seq, dbcsCode) {\n    \n    // Get the root of character tree according to first character of the sequence.\n    var uCode = seq[0];\n    var bucket = this._getEncodeBucket(uCode);\n    var low = uCode & 0xFF;\n\n    var node;\n    if (bucket[low] <= SEQ_START) {\n        // There's already a sequence with  - use it.\n        node = this.encodeTableSeq[SEQ_START-bucket[low]];\n    }\n    else {\n        // There was no sequence object - allocate a new one.\n        node = {};\n        if (bucket[low] !== UNASSIGNED) node[DEF_CHAR] = bucket[low]; // If a char was set before - make it a single-char subsequence.\n        bucket[low] = SEQ_START - this.encodeTableSeq.length;\n        this.encodeTableSeq.push(node);\n    }\n\n    // Traverse the character tree, allocating new nodes as needed.\n    for (var j = 1; j < seq.length-1; j++) {\n        var oldVal = node[uCode];\n        if (typeof oldVal === 'object')\n            node = oldVal;\n        else {\n            node = node[uCode] = {}\n            if (oldVal !== undefined)\n                node[DEF_CHAR] = oldVal\n        }\n    }\n\n    // Set the leaf to given dbcsCode.\n    uCode = seq[seq.length-1];\n    node[uCode] = dbcsCode;\n}\n\nDBCSCodec.prototype._fillEncodeTable = function(nodeIdx, prefix, skipEncodeChars) {\n    var node = this.decodeTables[nodeIdx];\n    var hasValues = false;\n    var subNodeEmpty = {};\n    for (var i = 0; i < 0x100; i++) {\n        var uCode = node[i];\n        var mbCode = prefix + i;\n        if (skipEncodeChars[mbCode])\n            continue;\n\n        if (uCode >= 0) {\n            this._setEncodeChar(uCode, mbCode);\n            hasValues = true;\n        } else if (uCode <= NODE_START) {\n            var subNodeIdx = NODE_START - uCode;\n            if (!subNodeEmpty[subNodeIdx]) {  // Skip empty subtrees (they are too large in gb18030).\n                var newPrefix = (mbCode << 8) >>> 0;  // NOTE: '>>> 0' keeps 32-bit num positive.\n                if (this._fillEncodeTable(subNodeIdx, newPrefix, skipEncodeChars))\n                    hasValues = true;\n                else\n                    subNodeEmpty[subNodeIdx] = true;\n            }\n        } else if (uCode <= SEQ_START) {\n            this._setEncodeSequence(this.decodeTableSeq[SEQ_START - uCode], mbCode);\n            hasValues = true;\n        }\n    }\n    return hasValues;\n}\n\n\n\n// == Encoder ==================================================================\n\nfunction DBCSEncoder(options, codec) {\n    // Encoder state\n    this.leadSurrogate = -1;\n    this.seqObj = undefined;\n    \n    // Static data\n    this.encodeTable = codec.encodeTable;\n    this.encodeTableSeq = codec.encodeTableSeq;\n    this.defaultCharSingleByte = codec.defCharSB;\n    this.gb18030 = codec.gb18030;\n}\n\nDBCSEncoder.prototype.write = function(str) {\n    var newBuf = Buffer.alloc(str.length * (this.gb18030 ? 4 : 3)),\n        leadSurrogate = this.leadSurrogate,\n        seqObj = this.seqObj, nextChar = -1,\n        i = 0, j = 0;\n\n    while (true) {\n        // 0. Get next character.\n        if (nextChar === -1) {\n            if (i == str.length) break;\n            var uCode = str.charCodeAt(i++);\n        }\n        else {\n            var uCode = nextChar;\n            nextChar = -1;    \n        }\n\n        // 1. Handle surrogates.\n        if (0xD800 <= uCode && uCode < 0xE000) { // Char is one of surrogates.\n            if (uCode < 0xDC00) { // We've got lead surrogate.\n                if (leadSurrogate === -1) {\n                    leadSurrogate = uCode;\n                    continue;\n                } else {\n                    leadSurrogate = uCode;\n                    // Double lead surrogate found.\n                    uCode = UNASSIGNED;\n                }\n            } else { // We've got trail surrogate.\n                if (leadSurrogate !== -1) {\n                    uCode = 0x10000 + (leadSurrogate - 0xD800) * 0x400 + (uCode - 0xDC00);\n                    leadSurrogate = -1;\n                } else {\n                    // Incomplete surrogate pair - only trail surrogate found.\n                    uCode = UNASSIGNED;\n                }\n                \n            }\n        }\n        else if (leadSurrogate !== -1) {\n            // Incomplete surrogate pair - only lead surrogate found.\n            nextChar = uCode; uCode = UNASSIGNED; // Write an error, then current char.\n            leadSurrogate = -1;\n        }\n\n        // 2. Convert uCode character.\n        var dbcsCode = UNASSIGNED;\n        if (seqObj !== undefined && uCode != UNASSIGNED) { // We are in the middle of the sequence\n            var resCode = seqObj[uCode];\n            if (typeof resCode === 'object') { // Sequence continues.\n                seqObj = resCode;\n                continue;\n\n            } else if (typeof resCode == 'number') { // Sequence finished. Write it.\n                dbcsCode = resCode;\n\n            } else if (resCode == undefined) { // Current character is not part of the sequence.\n\n                // Try default character for this sequence\n                resCode = seqObj[DEF_CHAR];\n                if (resCode !== undefined) {\n                    dbcsCode = resCode; // Found. Write it.\n                    nextChar = uCode; // Current character will be written too in the next iteration.\n\n                } else {\n                    // TODO: What if we have no default? (resCode == undefined)\n                    // Then, we should write first char of the sequence as-is and try the rest recursively.\n                    // Didn't do it for now because no encoding has this situation yet.\n                    // Currently, just skip the sequence and write current char.\n                }\n            }\n            seqObj = undefined;\n        }\n        else if (uCode >= 0) {  // Regular character\n            var subtable = this.encodeTable[uCode >> 8];\n            if (subtable !== undefined)\n                dbcsCode = subtable[uCode & 0xFF];\n            \n            if (dbcsCode <= SEQ_START) { // Sequence start\n                seqObj = this.encodeTableSeq[SEQ_START-dbcsCode];\n                continue;\n            }\n\n            if (dbcsCode == UNASSIGNED && this.gb18030) {\n                // Use GB18030 algorithm to find character(s) to write.\n                var idx = findIdx(this.gb18030.uChars, uCode);\n                if (idx != -1) {\n                    var dbcsCode = this.gb18030.gbChars[idx] + (uCode - this.gb18030.uChars[idx]);\n                    newBuf[j++] = 0x81 + Math.floor(dbcsCode / 12600); dbcsCode = dbcsCode % 12600;\n                    newBuf[j++] = 0x30 + Math.floor(dbcsCode / 1260); dbcsCode = dbcsCode % 1260;\n                    newBuf[j++] = 0x81 + Math.floor(dbcsCode / 10); dbcsCode = dbcsCode % 10;\n                    newBuf[j++] = 0x30 + dbcsCode;\n                    continue;\n                }\n            }\n        }\n\n        // 3. Write dbcsCode character.\n        if (dbcsCode === UNASSIGNED)\n            dbcsCode = this.defaultCharSingleByte;\n        \n        if (dbcsCode < 0x100) {\n            newBuf[j++] = dbcsCode;\n        }\n        else if (dbcsCode < 0x10000) {\n            newBuf[j++] = dbcsCode >> 8;   // high byte\n            newBuf[j++] = dbcsCode & 0xFF; // low byte\n        }\n        else if (dbcsCode < 0x1000000) {\n            newBuf[j++] = dbcsCode >> 16;\n            newBuf[j++] = (dbcsCode >> 8) & 0xFF;\n            newBuf[j++] = dbcsCode & 0xFF;\n        } else {\n            newBuf[j++] = dbcsCode >>> 24;\n            newBuf[j++] = (dbcsCode >>> 16) & 0xFF;\n            newBuf[j++] = (dbcsCode >>> 8) & 0xFF;\n            newBuf[j++] = dbcsCode & 0xFF;\n        }\n    }\n\n    this.seqObj = seqObj;\n    this.leadSurrogate = leadSurrogate;\n    return newBuf.slice(0, j);\n}\n\nDBCSEncoder.prototype.end = function() {\n    if (this.leadSurrogate === -1 && this.seqObj === undefined)\n        return; // All clean. Most often case.\n\n    var newBuf = Buffer.alloc(10), j = 0;\n\n    if (this.seqObj) { // We're in the sequence.\n        var dbcsCode = this.seqObj[DEF_CHAR];\n        if (dbcsCode !== undefined) { // Write beginning of the sequence.\n            if (dbcsCode < 0x100) {\n                newBuf[j++] = dbcsCode;\n            }\n            else {\n                newBuf[j++] = dbcsCode >> 8;   // high byte\n                newBuf[j++] = dbcsCode & 0xFF; // low byte\n            }\n        } else {\n            // See todo above.\n        }\n        this.seqObj = undefined;\n    }\n\n    if (this.leadSurrogate !== -1) {\n        // Incomplete surrogate pair - only lead surrogate found.\n        newBuf[j++] = this.defaultCharSingleByte;\n        this.leadSurrogate = -1;\n    }\n    \n    return newBuf.slice(0, j);\n}\n\n// Export for testing\nDBCSEncoder.prototype.findIdx = findIdx;\n\n\n// == Decoder ==================================================================\n\nfunction DBCSDecoder(options, codec) {\n    // Decoder state\n    this.nodeIdx = 0;\n    this.prevBytes = [];\n\n    // Static data\n    this.decodeTables = codec.decodeTables;\n    this.decodeTableSeq = codec.decodeTableSeq;\n    this.defaultCharUnicode = codec.defaultCharUnicode;\n    this.gb18030 = codec.gb18030;\n}\n\nDBCSDecoder.prototype.write = function(buf) {\n    var newBuf = Buffer.alloc(buf.length*2),\n        nodeIdx = this.nodeIdx, \n        prevBytes = this.prevBytes, prevOffset = this.prevBytes.length,\n        seqStart = -this.prevBytes.length, // idx of the start of current parsed sequence.\n        uCode;\n\n    for (var i = 0, j = 0; i < buf.length; i++) {\n        var curByte = (i >= 0) ? buf[i] : prevBytes[i + prevOffset];\n\n        // Lookup in current trie node.\n        var uCode = this.decodeTables[nodeIdx][curByte];\n\n        if (uCode >= 0) { \n            // Normal character, just use it.\n        }\n        else if (uCode === UNASSIGNED) { // Unknown char.\n            // TODO: Callback with seq.\n            uCode = this.defaultCharUnicode.charCodeAt(0);\n            i = seqStart; // Skip one byte ('i' will be incremented by the for loop) and try to parse again.\n        }\n        else if (uCode === GB18030_CODE) {\n            if (i >= 3) {\n                var ptr = (buf[i-3]-0x81)*12600 + (buf[i-2]-0x30)*1260 + (buf[i-1]-0x81)*10 + (curByte-0x30);\n            } else {\n                var ptr = (prevBytes[i-3+prevOffset]-0x81)*12600 + \n                          (((i-2 >= 0) ? buf[i-2] : prevBytes[i-2+prevOffset])-0x30)*1260 + \n                          (((i-1 >= 0) ? buf[i-1] : prevBytes[i-1+prevOffset])-0x81)*10 + \n                          (curByte-0x30);\n            }\n            var idx = findIdx(this.gb18030.gbChars, ptr);\n            uCode = this.gb18030.uChars[idx] + ptr - this.gb18030.gbChars[idx];\n        }\n        else if (uCode <= NODE_START) { // Go to next trie node.\n            nodeIdx = NODE_START - uCode;\n            continue;\n        }\n        else if (uCode <= SEQ_START) { // Output a sequence of chars.\n            var seq = this.decodeTableSeq[SEQ_START - uCode];\n            for (var k = 0; k < seq.length - 1; k++) {\n                uCode = seq[k];\n                newBuf[j++] = uCode & 0xFF;\n                newBuf[j++] = uCode >> 8;\n            }\n            uCode = seq[seq.length-1];\n        }\n        else\n            throw new Error(\"iconv-lite internal error: invalid decoding table value \" + uCode + \" at \" + nodeIdx + \"/\" + curByte);\n\n        // Write the character to buffer, handling higher planes using surrogate pair.\n        if (uCode >= 0x10000) { \n            uCode -= 0x10000;\n            var uCodeLead = 0xD800 | (uCode >> 10);\n            newBuf[j++] = uCodeLead & 0xFF;\n            newBuf[j++] = uCodeLead >> 8;\n\n            uCode = 0xDC00 | (uCode & 0x3FF);\n        }\n        newBuf[j++] = uCode & 0xFF;\n        newBuf[j++] = uCode >> 8;\n\n        // Reset trie node.\n        nodeIdx = 0; seqStart = i+1;\n    }\n\n    this.nodeIdx = nodeIdx;\n    this.prevBytes = (seqStart >= 0)\n        ? Array.prototype.slice.call(buf, seqStart)\n        : prevBytes.slice(seqStart + prevOffset).concat(Array.prototype.slice.call(buf));\n\n    return newBuf.slice(0, j).toString('ucs2');\n}\n\nDBCSDecoder.prototype.end = function() {\n    var ret = '';\n\n    // Try to parse all remaining chars.\n    while (this.prevBytes.length > 0) {\n        // Skip 1 character in the buffer.\n        ret += this.defaultCharUnicode;\n        var bytesArr = this.prevBytes.slice(1);\n\n        // Parse remaining as usual.\n        this.prevBytes = [];\n        this.nodeIdx = 0;\n        if (bytesArr.length > 0)\n            ret += this.write(bytesArr);\n    }\n\n    this.prevBytes = [];\n    this.nodeIdx = 0;\n    return ret;\n}\n\n// Binary search for GB18030. Returns largest i such that table[i] <= val.\nfunction findIdx(table, val) {\n    if (table[0] > val)\n        return -1;\n\n    var l = 0, r = table.length;\n    while (l < r-1) { // always table[l] <= val < table[r]\n        var mid = l + ((r-l+1) >> 1);\n        if (table[mid] <= val)\n            l = mid;\n        else\n            r = mid;\n    }\n    return l;\n}\n\n"], "names": [], "mappings": "AAAA;AACA,IAAI,SAAS,+FAAwB,MAAM;AAE3C,kFAAkF;AAClF,sFAAsF;AACtF,4EAA4E;AAE5E,QAAQ,KAAK,GAAG;AAEhB,IAAI,aAAa,CAAC,GACd,eAAe,CAAC,GAChB,YAAa,CAAC,IACd,aAAa,CAAC,MACd,kBAAkB,IAAI,MAAM,QAC5B,WAAW,CAAC;AAEhB,IAAK,IAAI,IAAI,GAAG,IAAI,OAAO,IACvB,eAAe,CAAC,EAAE,GAAG;AAGzB,wDAAwD;AACxD,SAAS,UAAU,YAAY,EAAE,KAAK;IAClC,IAAI,CAAC,YAAY,GAAG,aAAa,YAAY;IAC7C,IAAI,CAAC,cACD,MAAM,IAAI,MAAM;IACpB,IAAI,CAAC,aAAa,KAAK,EACnB,MAAM,IAAI,MAAM,eAAe,IAAI,CAAC,YAAY,GAAG;IAEvD,eAAe;IACf,IAAI,eAAe,aAAa,KAAK;IAGrC,kCAAkC;IAElC,4HAA4H;IAC5H,gCAAgC;IAChC,2DAA2D;IAC3D,wDAAwD;IACxD,2EAA2E;IAC3E,oFAAoF;IACpF,+FAA+F;IAC/F,IAAI,CAAC,YAAY,GAAG,EAAE;IACtB,IAAI,CAAC,YAAY,CAAC,EAAE,GAAG,gBAAgB,KAAK,CAAC,IAAI,oBAAoB;IAErE,+GAA+G;IAC/G,IAAI,CAAC,cAAc,GAAG,EAAE;IAExB,8EAA8E;IAC9E,IAAK,IAAI,IAAI,GAAG,IAAI,aAAa,MAAM,EAAE,IACrC,IAAI,CAAC,eAAe,CAAC,YAAY,CAAC,EAAE;IAExC,4CAA4C;IAC5C,IAAI,OAAO,aAAa,OAAO,KAAK,YAAY;QAC5C,IAAI,CAAC,OAAO,GAAG,aAAa,OAAO,IAAI,uBAAuB;QAE9D,mCAAmC;QACnC,IAAI,yBAAyB,IAAI,CAAC,YAAY,CAAC,MAAM;QACrD,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,gBAAgB,KAAK,CAAC;QAE7C,IAAI,0BAA0B,IAAI,CAAC,YAAY,CAAC,MAAM;QACtD,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,gBAAgB,KAAK,CAAC;QAE7C,oBAAoB;QACpB,IAAI,gBAAgB,IAAI,CAAC,YAAY,CAAC,EAAE;QACxC,IAAK,IAAI,IAAI,MAAM,KAAK,MAAM,IAAK;YAC/B,IAAI,iBAAiB,IAAI,CAAC,YAAY,CAAC,aAAa,aAAa,CAAC,EAAE,CAAC;YACrE,IAAK,IAAI,IAAI,MAAM,KAAK,MAAM,IAAK;gBAC/B,IAAI,cAAc,CAAC,EAAE,KAAK,YAAY;oBAClC,cAAc,CAAC,EAAE,GAAG,aAAa;gBACrC,OAAO,IAAI,cAAc,CAAC,EAAE,GAAG,YAAY;oBACvC,MAAM,IAAI,MAAM;gBACpB;gBAEA,IAAI,gBAAgB,IAAI,CAAC,YAAY,CAAC,aAAa,cAAc,CAAC,EAAE,CAAC;gBACrE,IAAK,IAAI,IAAI,MAAM,KAAK,MAAM,IAAK;oBAC/B,IAAI,aAAa,CAAC,EAAE,KAAK,YAAY;wBACjC,aAAa,CAAC,EAAE,GAAG,aAAa;oBACpC,OAAO,IAAI,aAAa,CAAC,EAAE,KAAK,aAAa,yBAAyB;wBAClE;oBACJ,OAAO,IAAI,aAAa,CAAC,EAAE,GAAG,YAAY;wBACtC,MAAM,IAAI,MAAM;oBACpB;oBAEA,IAAI,iBAAiB,IAAI,CAAC,YAAY,CAAC,aAAa,aAAa,CAAC,EAAE,CAAC;oBACrE,IAAK,IAAI,IAAI,MAAM,KAAK,MAAM,IAAK;wBAC/B,IAAI,cAAc,CAAC,EAAE,KAAK,YACtB,cAAc,CAAC,EAAE,GAAG;oBAC5B;gBACJ;YACJ;QACJ;IACJ;IAEA,IAAI,CAAC,kBAAkB,GAAG,MAAM,kBAAkB;IAGlD,kCAAkC;IAElC,iHAAiH;IACjH,yGAAyG;IACzG,+GAA+G;IAC/G,uEAAuE;IACvE,wGAAwG;IACxG,IAAI,CAAC,WAAW,GAAG,EAAE;IAErB,+GAA+G;IAC/G,wHAAwH;IACxH,uFAAuF;IACvF,wEAAwE;IACxE,IAAI,CAAC,cAAc,GAAG,EAAE;IAExB,sDAAsD;IACtD,IAAI,kBAAkB,CAAC;IACvB,IAAI,aAAa,cAAc,EAC3B,IAAK,IAAI,IAAI,GAAG,IAAI,aAAa,cAAc,CAAC,MAAM,EAAE,IAAK;QACzD,IAAI,MAAM,aAAa,cAAc,CAAC,EAAE;QACxC,IAAI,OAAO,QAAQ,UACf,eAAe,CAAC,IAAI,GAAG;aAEvB,IAAK,IAAI,IAAI,IAAI,IAAI,EAAE,KAAK,IAAI,EAAE,EAAE,IAChC,eAAe,CAAC,EAAE,GAAG;IACjC;IAEJ,yDAAyD;IACzD,IAAI,CAAC,gBAAgB,CAAC,GAAG,GAAG;IAE5B,uCAAuC;IACvC,IAAI,aAAa,SAAS,EAAE;QACxB,IAAK,IAAI,SAAS,aAAa,SAAS,CACpC,IAAI,OAAO,SAAS,CAAC,cAAc,CAAC,IAAI,CAAC,aAAa,SAAS,EAAE,QAC7D,IAAI,CAAC,cAAc,CAAC,MAAM,UAAU,CAAC,IAAI,aAAa,SAAS,CAAC,MAAM;IAClF;IAEA,IAAI,CAAC,SAAS,GAAI,IAAI,CAAC,WAAW,CAAC,EAAE,CAAC,MAAM,qBAAqB,CAAC,UAAU,CAAC,GAAG;IAChF,IAAI,IAAI,CAAC,SAAS,KAAK,YAAY,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,WAAW,CAAC,EAAE,CAAC,IAAI;IAC5E,IAAI,IAAI,CAAC,SAAS,KAAK,YAAY,IAAI,CAAC,SAAS,GAAG,IAAI,UAAU,CAAC;AACvE;AAEA,UAAU,SAAS,CAAC,OAAO,GAAG;AAC9B,UAAU,SAAS,CAAC,OAAO,GAAG;AAE9B,kBAAkB;AAClB,UAAU,SAAS,CAAC,kBAAkB,GAAG,SAAS,IAAI;IAClD,IAAI,QAAQ,EAAE;IACd,MAAO,OAAO,GAAG,UAAU,EACvB,MAAM,IAAI,CAAC,OAAO;IACtB,IAAI,MAAM,MAAM,IAAI,GAChB,MAAM,IAAI,CAAC;IAEf,IAAI,OAAO,IAAI,CAAC,YAAY,CAAC,EAAE;IAC/B,IAAK,IAAI,IAAI,MAAM,MAAM,GAAC,GAAG,IAAI,GAAG,IAAK;QACrC,IAAI,MAAM,IAAI,CAAC,KAAK,CAAC,EAAE,CAAC;QAExB,IAAI,OAAO,YAAY;YACnB,IAAI,CAAC,KAAK,CAAC,EAAE,CAAC,GAAG,aAAa,IAAI,CAAC,YAAY,CAAC,MAAM;YACtD,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,OAAO,gBAAgB,KAAK,CAAC;QACxD,OACK,IAAI,OAAO,YAAY;YACxB,OAAO,IAAI,CAAC,YAAY,CAAC,aAAa,IAAI;QAC9C,OAEI,MAAM,IAAI,MAAM,uBAAuB,IAAI,CAAC,YAAY,GAAG,aAAa,KAAK,QAAQ,CAAC;IAC9F;IACA,OAAO;AACX;AAGA,UAAU,SAAS,CAAC,eAAe,GAAG,SAAS,KAAK;IAChD,8DAA8D;IAC9D,IAAI,UAAU,SAAS,KAAK,CAAC,EAAE,EAAE;IAEjC,wDAAwD;IACxD,IAAI,aAAa,IAAI,CAAC,kBAAkB,CAAC;IACzC,UAAU,UAAU;IAEpB,sDAAsD;IACtD,IAAK,IAAI,IAAI,GAAG,IAAI,MAAM,MAAM,EAAE,IAAK;QACnC,IAAI,OAAO,KAAK,CAAC,EAAE;QACnB,IAAI,OAAO,SAAS,UAAU;YAC1B,IAAK,IAAI,IAAI,GAAG,IAAI,KAAK,MAAM,EAAG;gBAC9B,IAAI,OAAO,KAAK,UAAU,CAAC;gBAC3B,IAAI,UAAU,QAAQ,OAAO,QAAQ;oBACjC,IAAI,YAAY,KAAK,UAAU,CAAC;oBAChC,IAAI,UAAU,aAAa,YAAY,QACnC,UAAU,CAAC,UAAU,GAAG,UAAU,CAAC,OAAO,MAAM,IAAI,QAAQ,CAAC,YAAY,MAAM;yBAE/E,MAAM,IAAI,MAAM,iCAAkC,IAAI,CAAC,YAAY,GAAG,eAAe,KAAK,CAAC,EAAE;gBACrG,OACK,IAAI,SAAS,QAAQ,QAAQ,QAAQ;oBACtC,IAAI,MAAM,QAAQ,OAAO;oBACzB,IAAI,MAAM,EAAE;oBACZ,IAAK,IAAI,IAAI,GAAG,IAAI,KAAK,IACrB,IAAI,IAAI,CAAC,KAAK,UAAU,CAAC,OAAO,qEAAqE;oBAEzG,UAAU,CAAC,UAAU,GAAG,YAAY,IAAI,CAAC,cAAc,CAAC,MAAM;oBAC9D,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC;gBAC7B,OAEI,UAAU,CAAC,UAAU,GAAG,MAAM,aAAa;YACnD;QACJ,OACK,IAAI,OAAO,SAAS,UAAU;YAC/B,IAAI,WAAW,UAAU,CAAC,UAAU,EAAE,GAAG;YACzC,IAAK,IAAI,IAAI,GAAG,IAAI,MAAM,IACtB,UAAU,CAAC,UAAU,GAAG;QAChC,OAEI,MAAM,IAAI,MAAM,qBAAqB,OAAO,OAAO,gBAAiB,IAAI,CAAC,YAAY,GAAG,eAAe,KAAK,CAAC,EAAE;IACvH;IACA,IAAI,UAAU,MACV,MAAM,IAAI,MAAM,wBAAyB,IAAI,CAAC,YAAY,GAAG,cAAc,KAAK,CAAC,EAAE,GAAG,eAAe;AAC7G;AAEA,kBAAkB;AAClB,UAAU,SAAS,CAAC,gBAAgB,GAAG,SAAS,KAAK;IACjD,IAAI,OAAO,SAAS,GAAG,qDAAqD;IAC5E,IAAI,IAAI,CAAC,WAAW,CAAC,KAAK,KAAK,WAC3B,IAAI,CAAC,WAAW,CAAC,KAAK,GAAG,gBAAgB,KAAK,CAAC,IAAI,2BAA2B;IAClF,OAAO,IAAI,CAAC,WAAW,CAAC,KAAK;AACjC;AAEA,UAAU,SAAS,CAAC,cAAc,GAAG,SAAS,KAAK,EAAE,QAAQ;IACzD,IAAI,SAAS,IAAI,CAAC,gBAAgB,CAAC;IACnC,IAAI,MAAM,QAAQ;IAClB,IAAI,MAAM,CAAC,IAAI,IAAI,WACf,IAAI,CAAC,cAAc,CAAC,YAAU,MAAM,CAAC,IAAI,CAAC,CAAC,SAAS,GAAG,UAAU,mEAAmE;SACnI,IAAI,MAAM,CAAC,IAAI,IAAI,YACpB,MAAM,CAAC,IAAI,GAAG;AACtB;AAEA,UAAU,SAAS,CAAC,kBAAkB,GAAG,SAAS,GAAG,EAAE,QAAQ;IAE3D,+EAA+E;IAC/E,IAAI,QAAQ,GAAG,CAAC,EAAE;IAClB,IAAI,SAAS,IAAI,CAAC,gBAAgB,CAAC;IACnC,IAAI,MAAM,QAAQ;IAElB,IAAI;IACJ,IAAI,MAAM,CAAC,IAAI,IAAI,WAAW;QAC1B,6CAA6C;QAC7C,OAAO,IAAI,CAAC,cAAc,CAAC,YAAU,MAAM,CAAC,IAAI,CAAC;IACrD,OACK;QACD,qDAAqD;QACrD,OAAO,CAAC;QACR,IAAI,MAAM,CAAC,IAAI,KAAK,YAAY,IAAI,CAAC,SAAS,GAAG,MAAM,CAAC,IAAI,EAAE,gEAAgE;QAC9H,MAAM,CAAC,IAAI,GAAG,YAAY,IAAI,CAAC,cAAc,CAAC,MAAM;QACpD,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC;IAC7B;IAEA,+DAA+D;IAC/D,IAAK,IAAI,IAAI,GAAG,IAAI,IAAI,MAAM,GAAC,GAAG,IAAK;QACnC,IAAI,SAAS,IAAI,CAAC,MAAM;QACxB,IAAI,OAAO,WAAW,UAClB,OAAO;aACN;YACD,OAAO,IAAI,CAAC,MAAM,GAAG,CAAC;YACtB,IAAI,WAAW,WACX,IAAI,CAAC,SAAS,GAAG;QACzB;IACJ;IAEA,kCAAkC;IAClC,QAAQ,GAAG,CAAC,IAAI,MAAM,GAAC,EAAE;IACzB,IAAI,CAAC,MAAM,GAAG;AAClB;AAEA,UAAU,SAAS,CAAC,gBAAgB,GAAG,SAAS,OAAO,EAAE,MAAM,EAAE,eAAe;IAC5E,IAAI,OAAO,IAAI,CAAC,YAAY,CAAC,QAAQ;IACrC,IAAI,YAAY;IAChB,IAAI,eAAe,CAAC;IACpB,IAAK,IAAI,IAAI,GAAG,IAAI,OAAO,IAAK;QAC5B,IAAI,QAAQ,IAAI,CAAC,EAAE;QACnB,IAAI,SAAS,SAAS;QACtB,IAAI,eAAe,CAAC,OAAO,EACvB;QAEJ,IAAI,SAAS,GAAG;YACZ,IAAI,CAAC,cAAc,CAAC,OAAO;YAC3B,YAAY;QAChB,OAAO,IAAI,SAAS,YAAY;YAC5B,IAAI,aAAa,aAAa;YAC9B,IAAI,CAAC,YAAY,CAAC,WAAW,EAAE;gBAC3B,IAAI,YAAY,AAAC,UAAU,MAAO,GAAI,2CAA2C;gBACjF,IAAI,IAAI,CAAC,gBAAgB,CAAC,YAAY,WAAW,kBAC7C,YAAY;qBAEZ,YAAY,CAAC,WAAW,GAAG;YACnC;QACJ,OAAO,IAAI,SAAS,WAAW;YAC3B,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,cAAc,CAAC,YAAY,MAAM,EAAE;YAChE,YAAY;QAChB;IACJ;IACA,OAAO;AACX;AAIA,gFAAgF;AAEhF,SAAS,YAAY,OAAO,EAAE,KAAK;IAC/B,gBAAgB;IAChB,IAAI,CAAC,aAAa,GAAG,CAAC;IACtB,IAAI,CAAC,MAAM,GAAG;IAEd,cAAc;IACd,IAAI,CAAC,WAAW,GAAG,MAAM,WAAW;IACpC,IAAI,CAAC,cAAc,GAAG,MAAM,cAAc;IAC1C,IAAI,CAAC,qBAAqB,GAAG,MAAM,SAAS;IAC5C,IAAI,CAAC,OAAO,GAAG,MAAM,OAAO;AAChC;AAEA,YAAY,SAAS,CAAC,KAAK,GAAG,SAAS,GAAG;IACtC,IAAI,SAAS,OAAO,KAAK,CAAC,IAAI,MAAM,GAAG,CAAC,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,IACxD,gBAAgB,IAAI,CAAC,aAAa,EAClC,SAAS,IAAI,CAAC,MAAM,EAAE,WAAW,CAAC,GAClC,IAAI,GAAG,IAAI;IAEf,MAAO,KAAM;QACT,yBAAyB;QACzB,IAAI,aAAa,CAAC,GAAG;YACjB,IAAI,KAAK,IAAI,MAAM,EAAE;YACrB,IAAI,QAAQ,IAAI,UAAU,CAAC;QAC/B,OACK;YACD,IAAI,QAAQ;YACZ,WAAW,CAAC;QAChB;QAEA,wBAAwB;QACxB,IAAI,UAAU,SAAS,QAAQ,QAAQ;YACnC,IAAI,QAAQ,QAAQ;gBAChB,IAAI,kBAAkB,CAAC,GAAG;oBACtB,gBAAgB;oBAChB;gBACJ,OAAO;oBACH,gBAAgB;oBAChB,+BAA+B;oBAC/B,QAAQ;gBACZ;YACJ,OAAO;gBACH,IAAI,kBAAkB,CAAC,GAAG;oBACtB,QAAQ,UAAU,CAAC,gBAAgB,MAAM,IAAI,QAAQ,CAAC,QAAQ,MAAM;oBACpE,gBAAgB,CAAC;gBACrB,OAAO;oBACH,0DAA0D;oBAC1D,QAAQ;gBACZ;YAEJ;QACJ,OACK,IAAI,kBAAkB,CAAC,GAAG;YAC3B,yDAAyD;YACzD,WAAW;YAAO,QAAQ,YAAY,qCAAqC;YAC3E,gBAAgB,CAAC;QACrB;QAEA,8BAA8B;QAC9B,IAAI,WAAW;QACf,IAAI,WAAW,aAAa,SAAS,YAAY;YAC7C,IAAI,UAAU,MAAM,CAAC,MAAM;YAC3B,IAAI,OAAO,YAAY,UAAU;gBAC7B,SAAS;gBACT;YAEJ,OAAO,IAAI,OAAO,WAAW,UAAU;gBACnC,WAAW;YAEf,OAAO,IAAI,WAAW,WAAW;gBAE7B,0CAA0C;gBAC1C,UAAU,MAAM,CAAC,SAAS;gBAC1B,IAAI,YAAY,WAAW;oBACvB,WAAW,SAAS,mBAAmB;oBACvC,WAAW,OAAO,+DAA+D;gBAErF,OAAO;gBACH,2DAA2D;gBAC3D,uFAAuF;gBACvF,mEAAmE;gBACnE,4DAA4D;gBAChE;YACJ;YACA,SAAS;QACb,OACK,IAAI,SAAS,GAAG;YACjB,IAAI,WAAW,IAAI,CAAC,WAAW,CAAC,SAAS,EAAE;YAC3C,IAAI,aAAa,WACb,WAAW,QAAQ,CAAC,QAAQ,KAAK;YAErC,IAAI,YAAY,WAAW;gBACvB,SAAS,IAAI,CAAC,cAAc,CAAC,YAAU,SAAS;gBAChD;YACJ;YAEA,IAAI,YAAY,cAAc,IAAI,CAAC,OAAO,EAAE;gBACxC,uDAAuD;gBACvD,IAAI,MAAM,QAAQ,IAAI,CAAC,OAAO,CAAC,MAAM,EAAE;gBACvC,IAAI,OAAO,CAAC,GAAG;oBACX,IAAI,WAAW,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,IAAI,GAAG,CAAC,QAAQ,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,IAAI;oBAC5E,MAAM,CAAC,IAAI,GAAG,OAAO,KAAK,KAAK,CAAC,WAAW;oBAAQ,WAAW,WAAW;oBACzE,MAAM,CAAC,IAAI,GAAG,OAAO,KAAK,KAAK,CAAC,WAAW;oBAAO,WAAW,WAAW;oBACxE,MAAM,CAAC,IAAI,GAAG,OAAO,KAAK,KAAK,CAAC,WAAW;oBAAK,WAAW,WAAW;oBACtE,MAAM,CAAC,IAAI,GAAG,OAAO;oBACrB;gBACJ;YACJ;QACJ;QAEA,+BAA+B;QAC/B,IAAI,aAAa,YACb,WAAW,IAAI,CAAC,qBAAqB;QAEzC,IAAI,WAAW,OAAO;YAClB,MAAM,CAAC,IAAI,GAAG;QAClB,OACK,IAAI,WAAW,SAAS;YACzB,MAAM,CAAC,IAAI,GAAG,YAAY,GAAK,YAAY;YAC3C,MAAM,CAAC,IAAI,GAAG,WAAW,MAAM,WAAW;QAC9C,OACK,IAAI,WAAW,WAAW;YAC3B,MAAM,CAAC,IAAI,GAAG,YAAY;YAC1B,MAAM,CAAC,IAAI,GAAG,AAAC,YAAY,IAAK;YAChC,MAAM,CAAC,IAAI,GAAG,WAAW;QAC7B,OAAO;YACH,MAAM,CAAC,IAAI,GAAG,aAAa;YAC3B,MAAM,CAAC,IAAI,GAAG,AAAC,aAAa,KAAM;YAClC,MAAM,CAAC,IAAI,GAAG,AAAC,aAAa,IAAK;YACjC,MAAM,CAAC,IAAI,GAAG,WAAW;QAC7B;IACJ;IAEA,IAAI,CAAC,MAAM,GAAG;IACd,IAAI,CAAC,aAAa,GAAG;IACrB,OAAO,OAAO,KAAK,CAAC,GAAG;AAC3B;AAEA,YAAY,SAAS,CAAC,GAAG,GAAG;IACxB,IAAI,IAAI,CAAC,aAAa,KAAK,CAAC,KAAK,IAAI,CAAC,MAAM,KAAK,WAC7C,QAAQ,8BAA8B;IAE1C,IAAI,SAAS,OAAO,KAAK,CAAC,KAAK,IAAI;IAEnC,IAAI,IAAI,CAAC,MAAM,EAAE;QACb,IAAI,WAAW,IAAI,CAAC,MAAM,CAAC,SAAS;QACpC,IAAI,aAAa,WAAW;YACxB,IAAI,WAAW,OAAO;gBAClB,MAAM,CAAC,IAAI,GAAG;YAClB,OACK;gBACD,MAAM,CAAC,IAAI,GAAG,YAAY,GAAK,YAAY;gBAC3C,MAAM,CAAC,IAAI,GAAG,WAAW,MAAM,WAAW;YAC9C;QACJ,OAAO;QACH,kBAAkB;QACtB;QACA,IAAI,CAAC,MAAM,GAAG;IAClB;IAEA,IAAI,IAAI,CAAC,aAAa,KAAK,CAAC,GAAG;QAC3B,yDAAyD;QACzD,MAAM,CAAC,IAAI,GAAG,IAAI,CAAC,qBAAqB;QACxC,IAAI,CAAC,aAAa,GAAG,CAAC;IAC1B;IAEA,OAAO,OAAO,KAAK,CAAC,GAAG;AAC3B;AAEA,qBAAqB;AACrB,YAAY,SAAS,CAAC,OAAO,GAAG;AAGhC,gFAAgF;AAEhF,SAAS,YAAY,OAAO,EAAE,KAAK;IAC/B,gBAAgB;IAChB,IAAI,CAAC,OAAO,GAAG;IACf,IAAI,CAAC,SAAS,GAAG,EAAE;IAEnB,cAAc;IACd,IAAI,CAAC,YAAY,GAAG,MAAM,YAAY;IACtC,IAAI,CAAC,cAAc,GAAG,MAAM,cAAc;IAC1C,IAAI,CAAC,kBAAkB,GAAG,MAAM,kBAAkB;IAClD,IAAI,CAAC,OAAO,GAAG,MAAM,OAAO;AAChC;AAEA,YAAY,SAAS,CAAC,KAAK,GAAG,SAAS,GAAG;IACtC,IAAI,SAAS,OAAO,KAAK,CAAC,IAAI,MAAM,GAAC,IACjC,UAAU,IAAI,CAAC,OAAO,EACtB,YAAY,IAAI,CAAC,SAAS,EAAE,aAAa,IAAI,CAAC,SAAS,CAAC,MAAM,EAC9D,WAAW,CAAC,IAAI,CAAC,SAAS,CAAC,MAAM,EACjC;IAEJ,IAAK,IAAI,IAAI,GAAG,IAAI,GAAG,IAAI,IAAI,MAAM,EAAE,IAAK;QACxC,IAAI,UAAU,AAAC,KAAK,IAAK,GAAG,CAAC,EAAE,GAAG,SAAS,CAAC,IAAI,WAAW;QAE3D,+BAA+B;QAC/B,IAAI,QAAQ,IAAI,CAAC,YAAY,CAAC,QAAQ,CAAC,QAAQ;QAE/C,IAAI,SAAS,GAAG;QACZ,iCAAiC;QACrC,OACK,IAAI,UAAU,YAAY;YAC3B,2BAA2B;YAC3B,QAAQ,IAAI,CAAC,kBAAkB,CAAC,UAAU,CAAC;YAC3C,IAAI,UAAU,kFAAkF;QACpG,OACK,IAAI,UAAU,cAAc;YAC7B,IAAI,KAAK,GAAG;gBACR,IAAI,MAAM,CAAC,GAAG,CAAC,IAAE,EAAE,GAAC,IAAI,IAAE,QAAQ,CAAC,GAAG,CAAC,IAAE,EAAE,GAAC,IAAI,IAAE,OAAO,CAAC,GAAG,CAAC,IAAE,EAAE,GAAC,IAAI,IAAE,KAAK,CAAC,UAAQ,IAAI;YAC/F,OAAO;gBACH,IAAI,MAAM,CAAC,SAAS,CAAC,IAAE,IAAE,WAAW,GAAC,IAAI,IAAE,QACjC,CAAC,CAAC,AAAC,IAAE,KAAK,IAAK,GAAG,CAAC,IAAE,EAAE,GAAG,SAAS,CAAC,IAAE,IAAE,WAAW,IAAE,IAAI,IAAE,OAC3D,CAAC,CAAC,AAAC,IAAE,KAAK,IAAK,GAAG,CAAC,IAAE,EAAE,GAAG,SAAS,CAAC,IAAE,IAAE,WAAW,IAAE,IAAI,IAAE,KAC3D,CAAC,UAAQ,IAAI;YAC3B;YACA,IAAI,MAAM,QAAQ,IAAI,CAAC,OAAO,CAAC,OAAO,EAAE;YACxC,QAAQ,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,IAAI,GAAG,MAAM,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,IAAI;QACtE,OACK,IAAI,SAAS,YAAY;YAC1B,UAAU,aAAa;YACvB;QACJ,OACK,IAAI,SAAS,WAAW;YACzB,IAAI,MAAM,IAAI,CAAC,cAAc,CAAC,YAAY,MAAM;YAChD,IAAK,IAAI,IAAI,GAAG,IAAI,IAAI,MAAM,GAAG,GAAG,IAAK;gBACrC,QAAQ,GAAG,CAAC,EAAE;gBACd,MAAM,CAAC,IAAI,GAAG,QAAQ;gBACtB,MAAM,CAAC,IAAI,GAAG,SAAS;YAC3B;YACA,QAAQ,GAAG,CAAC,IAAI,MAAM,GAAC,EAAE;QAC7B,OAEI,MAAM,IAAI,MAAM,6DAA6D,QAAQ,SAAS,UAAU,MAAM;QAElH,8EAA8E;QAC9E,IAAI,SAAS,SAAS;YAClB,SAAS;YACT,IAAI,YAAY,SAAU,SAAS;YACnC,MAAM,CAAC,IAAI,GAAG,YAAY;YAC1B,MAAM,CAAC,IAAI,GAAG,aAAa;YAE3B,QAAQ,SAAU,QAAQ;QAC9B;QACA,MAAM,CAAC,IAAI,GAAG,QAAQ;QACtB,MAAM,CAAC,IAAI,GAAG,SAAS;QAEvB,mBAAmB;QACnB,UAAU;QAAG,WAAW,IAAE;IAC9B;IAEA,IAAI,CAAC,OAAO,GAAG;IACf,IAAI,CAAC,SAAS,GAAG,AAAC,YAAY,IACxB,MAAM,SAAS,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,YAChC,UAAU,KAAK,CAAC,WAAW,YAAY,MAAM,CAAC,MAAM,SAAS,CAAC,KAAK,CAAC,IAAI,CAAC;IAE/E,OAAO,OAAO,KAAK,CAAC,GAAG,GAAG,QAAQ,CAAC;AACvC;AAEA,YAAY,SAAS,CAAC,GAAG,GAAG;IACxB,IAAI,MAAM;IAEV,oCAAoC;IACpC,MAAO,IAAI,CAAC,SAAS,CAAC,MAAM,GAAG,EAAG;QAC9B,kCAAkC;QAClC,OAAO,IAAI,CAAC,kBAAkB;QAC9B,IAAI,WAAW,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC;QAEpC,4BAA4B;QAC5B,IAAI,CAAC,SAAS,GAAG,EAAE;QACnB,IAAI,CAAC,OAAO,GAAG;QACf,IAAI,SAAS,MAAM,GAAG,GAClB,OAAO,IAAI,CAAC,KAAK,CAAC;IAC1B;IAEA,IAAI,CAAC,SAAS,GAAG,EAAE;IACnB,IAAI,CAAC,OAAO,GAAG;IACf,OAAO;AACX;AAEA,0EAA0E;AAC1E,SAAS,QAAQ,KAAK,EAAE,GAAG;IACvB,IAAI,KAAK,CAAC,EAAE,GAAG,KACX,OAAO,CAAC;IAEZ,IAAI,IAAI,GAAG,IAAI,MAAM,MAAM;IAC3B,MAAO,IAAI,IAAE,EAAG;QACZ,IAAI,MAAM,IAAI,CAAC,AAAC,IAAE,IAAE,KAAM,CAAC;QAC3B,IAAI,KAAK,CAAC,IAAI,IAAI,KACd,IAAI;aAEJ,IAAI;IACZ;IACA,OAAO;AACX", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1969, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/New%20folder%20%2825%29/streamzen/node_modules/iconv-lite/encodings/dbcs-data.js"], "sourcesContent": ["\"use strict\";\n\n// Description of supported double byte encodings and aliases.\n// Tables are not require()-d until they are needed to speed up library load.\n// require()-s are direct to support Browserify.\n\nmodule.exports = {\n    \n    // == Japanese/ShiftJIS ====================================================\n    // All japanese encodings are based on JIS X set of standards:\n    // JIS X 0201 - Single-byte encoding of ASCII + ¥ + Kana chars at 0xA1-0xDF.\n    // JIS X 0208 - Main set of 6879 characters, placed in 94x94 plane, to be encoded by 2 bytes. \n    //              Has several variations in 1978, 1983, 1990 and 1997.\n    // JIS X 0212 - Supplementary plane of 6067 chars in 94x94 plane. 1990. Effectively dead.\n    // JIS X 0213 - Extension and modern replacement of 0208 and 0212. Total chars: 11233.\n    //              2 planes, first is superset of 0208, second - revised 0212.\n    //              Introduced in 2000, revised 2004. Some characters are in Unicode Plane 2 (0x2xxxx)\n\n    // Byte encodings are:\n    //  * Shift_JIS: Compatible with 0201, uses not defined chars in top half as lead bytes for double-byte\n    //               encoding of 0208. Lead byte ranges: 0x81-0x9F, 0xE0-0xEF; Trail byte ranges: 0x40-0x7E, 0x80-0x9E, 0x9F-0xFC.\n    //               Windows CP932 is a superset of Shift_JIS. Some companies added more chars, notably KDDI.\n    //  * EUC-JP:    Up to 3 bytes per character. Used mostly on *nixes.\n    //               0x00-0x7F       - lower part of 0201\n    //               0x8E, 0xA1-0xDF - upper part of 0201\n    //               (0xA1-0xFE)x2   - 0208 plane (94x94).\n    //               0x8F, (0xA1-0xFE)x2 - 0212 plane (94x94).\n    //  * JIS X 208: 7-bit, direct encoding of 0208. Byte ranges: 0x21-0x7E (94 values). Uncommon.\n    //               Used as-is in ISO2022 family.\n    //  * ISO2022-JP: Stateful encoding, with escape sequences to switch between ASCII, \n    //                0201-1976 Roman, 0208-1978, 0208-1983.\n    //  * ISO2022-JP-1: Adds esc seq for 0212-1990.\n    //  * ISO2022-JP-2: Adds esc seq for GB2313-1980, KSX1001-1992, ISO8859-1, ISO8859-7.\n    //  * ISO2022-JP-3: Adds esc seq for 0201-1976 Kana set, 0213-2000 Planes 1, 2.\n    //  * ISO2022-JP-2004: Adds 0213-2004 Plane 1.\n    //\n    // After JIS X 0213 appeared, Shift_JIS-2004, EUC-JISX0213 and ISO2022-JP-2004 followed, with just changing the planes.\n    //\n    // Overall, it seems that it's a mess :( http://www8.plala.or.jp/tkubota1/unicode-symbols-map2.html\n\n    'shiftjis': {\n        type: '_dbcs',\n        table: function() { return require('./tables/shiftjis.json') },\n        encodeAdd: {'\\u00a5': 0x5C, '\\u203E': 0x7E},\n        encodeSkipVals: [{from: 0xED40, to: 0xF940}],\n    },\n    'csshiftjis': 'shiftjis',\n    'mskanji': 'shiftjis',\n    'sjis': 'shiftjis',\n    'windows31j': 'shiftjis',\n    'ms31j': 'shiftjis',\n    'xsjis': 'shiftjis',\n    'windows932': 'shiftjis',\n    'ms932': 'shiftjis',\n    '932': 'shiftjis',\n    'cp932': 'shiftjis',\n\n    'eucjp': {\n        type: '_dbcs',\n        table: function() { return require('./tables/eucjp.json') },\n        encodeAdd: {'\\u00a5': 0x5C, '\\u203E': 0x7E},\n    },\n\n    // TODO: KDDI extension to Shift_JIS\n    // TODO: IBM CCSID 942 = CP932, but F0-F9 custom chars and other char changes.\n    // TODO: IBM CCSID 943 = Shift_JIS = CP932 with original Shift_JIS lower 128 chars.\n\n\n    // == Chinese/GBK ==========================================================\n    // http://en.wikipedia.org/wiki/GBK\n    // We mostly implement W3C recommendation: https://www.w3.org/TR/encoding/#gbk-encoder\n\n    // Oldest GB2312 (1981, ~7600 chars) is a subset of CP936\n    'gb2312': 'cp936',\n    'gb231280': 'cp936',\n    'gb23121980': 'cp936',\n    'csgb2312': 'cp936',\n    'csiso58gb231280': 'cp936',\n    'euccn': 'cp936',\n\n    // Microsoft's CP936 is a subset and approximation of GBK.\n    'windows936': 'cp936',\n    'ms936': 'cp936',\n    '936': 'cp936',\n    'cp936': {\n        type: '_dbcs',\n        table: function() { return require('./tables/cp936.json') },\n    },\n\n    // GBK (~22000 chars) is an extension of CP936 that added user-mapped chars and some other.\n    'gbk': {\n        type: '_dbcs',\n        table: function() { return require('./tables/cp936.json').concat(require('./tables/gbk-added.json')) },\n    },\n    'xgbk': 'gbk',\n    'isoir58': 'gbk',\n\n    // GB18030 is an algorithmic extension of GBK.\n    // Main source: https://www.w3.org/TR/encoding/#gbk-encoder\n    // http://icu-project.org/docs/papers/gb18030.html\n    // http://source.icu-project.org/repos/icu/data/trunk/charset/data/xml/gb-18030-2000.xml\n    // http://www.khngai.com/chinese/charmap/tblgbk.php?page=0\n    'gb18030': {\n        type: '_dbcs',\n        table: function() { return require('./tables/cp936.json').concat(require('./tables/gbk-added.json')) },\n        gb18030: function() { return require('./tables/gb18030-ranges.json') },\n        encodeSkipVals: [0x80],\n        encodeAdd: {'€': 0xA2E3},\n    },\n\n    'chinese': 'gb18030',\n\n\n    // == Korean ===============================================================\n    // EUC-KR, KS_C_5601 and KS X 1001 are exactly the same.\n    'windows949': 'cp949',\n    'ms949': 'cp949',\n    '949': 'cp949',\n    'cp949': {\n        type: '_dbcs',\n        table: function() { return require('./tables/cp949.json') },\n    },\n\n    'cseuckr': 'cp949',\n    'csksc56011987': 'cp949',\n    'euckr': 'cp949',\n    'isoir149': 'cp949',\n    'korean': 'cp949',\n    'ksc56011987': 'cp949',\n    'ksc56011989': 'cp949',\n    'ksc5601': 'cp949',\n\n\n    // == Big5/Taiwan/Hong Kong ================================================\n    // There are lots of tables for Big5 and cp950. Please see the following links for history:\n    // http://moztw.org/docs/big5/  http://www.haible.de/bruno/charsets/conversion-tables/Big5.html\n    // Variations, in roughly number of defined chars:\n    //  * Windows CP 950: Microsoft variant of Big5. Canonical: http://www.unicode.org/Public/MAPPINGS/VENDORS/MICSFT/WINDOWS/CP950.TXT\n    //  * Windows CP 951: Microsoft variant of Big5-HKSCS-2001. Seems to be never public. http://me.abelcheung.org/articles/research/what-is-cp951/\n    //  * Big5-2003 (Taiwan standard) almost superset of cp950.\n    //  * Unicode-at-on (UAO) / Mozilla 1.8. Falling out of use on the Web. Not supported by other browsers.\n    //  * Big5-HKSCS (-2001, -2004, -2008). Hong Kong standard. \n    //    many unicode code points moved from PUA to Supplementary plane (U+2XXXX) over the years.\n    //    Plus, it has 4 combining sequences.\n    //    Seems that Mozilla refused to support it for 10 yrs. https://bugzilla.mozilla.org/show_bug.cgi?id=162431 https://bugzilla.mozilla.org/show_bug.cgi?id=310299\n    //    because big5-hkscs is the only encoding to include astral characters in non-algorithmic way.\n    //    Implementations are not consistent within browsers; sometimes labeled as just big5.\n    //    MS Internet Explorer switches from big5 to big5-hkscs when a patch applied.\n    //    Great discussion & recap of what's going on https://bugzilla.mozilla.org/show_bug.cgi?id=912470#c31\n    //    In the encoder, it might make sense to support encoding old PUA mappings to Big5 bytes seq-s.\n    //    Official spec: http://www.ogcio.gov.hk/en/business/tech_promotion/ccli/terms/doc/2003cmp_2008.txt\n    //                   http://www.ogcio.gov.hk/tc/business/tech_promotion/ccli/terms/doc/hkscs-2008-big5-iso.txt\n    // \n    // Current understanding of how to deal with Big5(-HKSCS) is in the Encoding Standard, http://encoding.spec.whatwg.org/#big5-encoder\n    // Unicode mapping (http://www.unicode.org/Public/MAPPINGS/OBSOLETE/EASTASIA/OTHER/BIG5.TXT) is said to be wrong.\n\n    'windows950': 'cp950',\n    'ms950': 'cp950',\n    '950': 'cp950',\n    'cp950': {\n        type: '_dbcs',\n        table: function() { return require('./tables/cp950.json') },\n    },\n\n    // Big5 has many variations and is an extension of cp950. We use Encoding Standard's as a consensus.\n    'big5': 'big5hkscs',\n    'big5hkscs': {\n        type: '_dbcs',\n        table: function() { return require('./tables/cp950.json').concat(require('./tables/big5-added.json')) },\n        encodeSkipVals: [\n            // Although Encoding Standard says we should avoid encoding to HKSCS area (See Step 1 of\n            // https://encoding.spec.whatwg.org/#index-big5-pointer), we still do it to increase compatibility with ICU.\n            // But if a single unicode point can be encoded both as HKSCS and regular Big5, we prefer the latter.\n            0x8e69, 0x8e6f, 0x8e7e, 0x8eab, 0x8eb4, 0x8ecd, 0x8ed0, 0x8f57, 0x8f69, 0x8f6e, 0x8fcb, 0x8ffe,\n            0x906d, 0x907a, 0x90c4, 0x90dc, 0x90f1, 0x91bf, 0x92af, 0x92b0, 0x92b1, 0x92b2, 0x92d1, 0x9447, 0x94ca,\n            0x95d9, 0x96fc, 0x9975, 0x9b76, 0x9b78, 0x9b7b, 0x9bc6, 0x9bde, 0x9bec, 0x9bf6, 0x9c42, 0x9c53, 0x9c62,\n            0x9c68, 0x9c6b, 0x9c77, 0x9cbc, 0x9cbd, 0x9cd0, 0x9d57, 0x9d5a, 0x9dc4, 0x9def, 0x9dfb, 0x9ea9, 0x9eef,\n            0x9efd, 0x9f60, 0x9fcb, 0xa077, 0xa0dc, 0xa0df, 0x8fcc, 0x92c8, 0x9644, 0x96ed,\n\n            // Step 2 of https://encoding.spec.whatwg.org/#index-big5-pointer: Use last pointer for U+2550, U+255E, U+2561, U+256A, U+5341, or U+5345\n            0xa2a4, 0xa2a5, 0xa2a7, 0xa2a6, 0xa2cc, 0xa2ce,\n        ],\n    },\n\n    'cnbig5': 'big5hkscs',\n    'csbig5': 'big5hkscs',\n    'xxbig5': 'big5hkscs',\n};\n"], "names": [], "mappings": "AAAA;AAEA,8DAA8D;AAC9D,6EAA6E;AAC7E,gDAAgD;AAEhD,OAAO,OAAO,GAAG;IAEb,4EAA4E;IAC5E,8DAA8D;IAC9D,4EAA4E;IAC5E,8FAA8F;IAC9F,oEAAoE;IACpE,yFAAyF;IACzF,sFAAsF;IACtF,2EAA2E;IAC3E,kGAAkG;IAElG,sBAAsB;IACtB,uGAAuG;IACvG,8HAA8H;IAC9H,yGAAyG;IACzG,oEAAoE;IACpE,qDAAqD;IACrD,qDAAqD;IACrD,sDAAsD;IACtD,0DAA0D;IAC1D,8FAA8F;IAC9F,8CAA8C;IAC9C,oFAAoF;IACpF,wDAAwD;IACxD,+CAA+C;IAC/C,qFAAqF;IACrF,+EAA+E;IAC/E,8CAA8C;IAC9C,EAAE;IACF,uHAAuH;IACvH,EAAE;IACF,mGAAmG;IAEnG,YAAY;QACR,MAAM;QACN,OAAO;YAAa;QAAyC;QAC7D,WAAW;YAAC,UAAU;YAAM,UAAU;QAAI;QAC1C,gBAAgB;YAAC;gBAAC,MAAM;gBAAQ,IAAI;YAAM;SAAE;IAChD;IACA,cAAc;IACd,WAAW;IACX,QAAQ;IACR,cAAc;IACd,SAAS;IACT,SAAS;IACT,cAAc;IACd,SAAS;IACT,OAAO;IACP,SAAS;IAET,SAAS;QACL,MAAM;QACN,OAAO;YAAa;QAAsC;QAC1D,WAAW;YAAC,UAAU;YAAM,UAAU;QAAI;IAC9C;IAEA,oCAAoC;IACpC,8EAA8E;IAC9E,mFAAmF;IAGnF,4EAA4E;IAC5E,mCAAmC;IACnC,sFAAsF;IAEtF,yDAAyD;IACzD,UAAU;IACV,YAAY;IACZ,cAAc;IACd,YAAY;IACZ,mBAAmB;IACnB,SAAS;IAET,0DAA0D;IAC1D,cAAc;IACd,SAAS;IACT,OAAO;IACP,SAAS;QACL,MAAM;QACN,OAAO;YAAa;QAAsC;IAC9D;IAEA,2FAA2F;IAC3F,OAAO;QACH,MAAM;QACN,OAAO;YAAa,OAAO,gGAA+B,MAAM;QAAqC;IACzG;IACA,QAAQ;IACR,WAAW;IAEX,8CAA8C;IAC9C,2DAA2D;IAC3D,kDAAkD;IAClD,wFAAwF;IACxF,0DAA0D;IAC1D,WAAW;QACP,MAAM;QACN,OAAO;YAAa,OAAO,gGAA+B,MAAM;QAAqC;QACrG,SAAS;YAAa;QAA+C;QACrE,gBAAgB;YAAC;SAAK;QACtB,WAAW;YAAC,KAAK;QAAM;IAC3B;IAEA,WAAW;IAGX,4EAA4E;IAC5E,wDAAwD;IACxD,cAAc;IACd,SAAS;IACT,OAAO;IACP,SAAS;QACL,MAAM;QACN,OAAO;YAAa;QAAsC;IAC9D;IAEA,WAAW;IACX,iBAAiB;IACjB,SAAS;IACT,YAAY;IACZ,UAAU;IACV,eAAe;IACf,eAAe;IACf,WAAW;IAGX,4EAA4E;IAC5E,2FAA2F;IAC3F,+FAA+F;IAC/F,kDAAkD;IAClD,mIAAmI;IACnI,+IAA+I;IAC/I,2DAA2D;IAC3D,wGAAwG;IACxG,4DAA4D;IAC5D,8FAA8F;IAC9F,yCAAyC;IACzC,kKAAkK;IAClK,kGAAkG;IAClG,yFAAyF;IACzF,iFAAiF;IACjF,yGAAyG;IACzG,mGAAmG;IACnG,uGAAuG;IACvG,8GAA8G;IAC9G,GAAG;IACH,oIAAoI;IACpI,iHAAiH;IAEjH,cAAc;IACd,SAAS;IACT,OAAO;IACP,SAAS;QACL,MAAM;QACN,OAAO;YAAa;QAAsC;IAC9D;IAEA,oGAAoG;IACpG,QAAQ;IACR,aAAa;QACT,MAAM;QACN,OAAO;YAAa,OAAO,gGAA+B,MAAM;QAAsC;QACtG,gBAAgB;YACZ,wFAAwF;YACxF,4GAA4G;YAC5G,qGAAqG;YACrG;YAAQ;YAAQ;YAAQ;YAAQ;YAAQ;YAAQ;YAAQ;YAAQ;YAAQ;YAAQ;YAAQ;YACxF;YAAQ;YAAQ;YAAQ;YAAQ;YAAQ;YAAQ;YAAQ;YAAQ;YAAQ;YAAQ;YAAQ;YAAQ;YAChG;YAAQ;YAAQ;YAAQ;YAAQ;YAAQ;YAAQ;YAAQ;YAAQ;YAAQ;YAAQ;YAAQ;YAAQ;YAChG;YAAQ;YAAQ;YAAQ;YAAQ;YAAQ;YAAQ;YAAQ;YAAQ;YAAQ;YAAQ;YAAQ;YAAQ;YAChG;YAAQ;YAAQ;YAAQ;YAAQ;YAAQ;YAAQ;YAAQ;YAAQ;YAAQ;YAExE,yIAAyI;YACzI;YAAQ;YAAQ;YAAQ;YAAQ;YAAQ;SAC3C;IACL;IAEA,UAAU;IACV,UAAU;IACV,UAAU;AACd", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2233, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/New%20folder%20%2825%29/streamzen/node_modules/iconv-lite/encodings/index.js"], "sourcesContent": ["\"use strict\";\n\n// Update this array if you add/rename/remove files in this directory.\n// We support Browserify by skipping automatic module discovery and requiring modules directly.\nvar modules = [\n    require(\"./internal\"),\n    require(\"./utf32\"),\n    require(\"./utf16\"),\n    require(\"./utf7\"),\n    require(\"./sbcs-codec\"),\n    require(\"./sbcs-data\"),\n    require(\"./sbcs-data-generated\"),\n    require(\"./dbcs-codec\"),\n    require(\"./dbcs-data\"),\n];\n\n// Put all encoding/alias/codec definitions to single object and export it.\nfor (var i = 0; i < modules.length; i++) {\n    var module = modules[i];\n    for (var enc in module)\n        if (Object.prototype.hasOwnProperty.call(module, enc))\n            exports[enc] = module[enc];\n}\n"], "names": [], "mappings": "AAAA;AAEA,sEAAsE;AACtE,+FAA+F;AAC/F,IAAI,UAAU;;;;;;;;;;CAUb;AAED,2EAA2E;AAC3E,IAAK,IAAI,IAAI,GAAG,IAAI,QAAQ,MAAM,EAAE,IAAK;IACrC,IAAI,SAAS,OAAO,CAAC,EAAE;IACvB,IAAK,IAAI,OAAO,OACZ,IAAI,OAAO,SAAS,CAAC,cAAc,CAAC,IAAI,CAAC,QAAQ,MAC7C,OAAO,CAAC,IAAI,GAAG,MAAM,CAAC,IAAI;AACtC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2257, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/New%20folder%20%2825%29/streamzen/node_modules/iconv-lite/lib/streams.js"], "sourcesContent": ["\"use strict\";\n\nvar Buffer = require(\"safer-buffer\").Buffer;\n\n// NOTE: Due to 'stream' module being pretty large (~100Kb, significant in browser environments), \n// we opt to dependency-inject it instead of creating a hard dependency.\nmodule.exports = function(stream_module) {\n    var Transform = stream_module.Transform;\n\n    // == Encoder stream =======================================================\n\n    function IconvLiteEncoderStream(conv, options) {\n        this.conv = conv;\n        options = options || {};\n        options.decodeStrings = false; // We accept only strings, so we don't need to decode them.\n        Transform.call(this, options);\n    }\n\n    IconvLiteEncoderStream.prototype = Object.create(Transform.prototype, {\n        constructor: { value: IconvLiteEncoderStream }\n    });\n\n    IconvLiteEncoderStream.prototype._transform = function(chunk, encoding, done) {\n        if (typeof chunk != 'string')\n            return done(new Error(\"Iconv encoding stream needs strings as its input.\"));\n        try {\n            var res = this.conv.write(chunk);\n            if (res && res.length) this.push(res);\n            done();\n        }\n        catch (e) {\n            done(e);\n        }\n    }\n\n    IconvLiteEncoderStream.prototype._flush = function(done) {\n        try {\n            var res = this.conv.end();\n            if (res && res.length) this.push(res);\n            done();\n        }\n        catch (e) {\n            done(e);\n        }\n    }\n\n    IconvLiteEncoderStream.prototype.collect = function(cb) {\n        var chunks = [];\n        this.on('error', cb);\n        this.on('data', function(chunk) { chunks.push(chunk); });\n        this.on('end', function() {\n            cb(null, Buffer.concat(chunks));\n        });\n        return this;\n    }\n\n\n    // == Decoder stream =======================================================\n\n    function IconvLiteDecoderStream(conv, options) {\n        this.conv = conv;\n        options = options || {};\n        options.encoding = this.encoding = 'utf8'; // We output strings.\n        Transform.call(this, options);\n    }\n\n    IconvLiteDecoderStream.prototype = Object.create(Transform.prototype, {\n        constructor: { value: IconvLiteDecoderStream }\n    });\n\n    IconvLiteDecoderStream.prototype._transform = function(chunk, encoding, done) {\n        if (!Buffer.isBuffer(chunk) && !(chunk instanceof Uint8Array))\n            return done(new Error(\"Iconv decoding stream needs buffers as its input.\"));\n        try {\n            var res = this.conv.write(chunk);\n            if (res && res.length) this.push(res, this.encoding);\n            done();\n        }\n        catch (e) {\n            done(e);\n        }\n    }\n\n    IconvLiteDecoderStream.prototype._flush = function(done) {\n        try {\n            var res = this.conv.end();\n            if (res && res.length) this.push(res, this.encoding);                \n            done();\n        }\n        catch (e) {\n            done(e);\n        }\n    }\n\n    IconvLiteDecoderStream.prototype.collect = function(cb) {\n        var res = '';\n        this.on('error', cb);\n        this.on('data', function(chunk) { res += chunk; });\n        this.on('end', function() {\n            cb(null, res);\n        });\n        return this;\n    }\n\n    return {\n        IconvLiteEncoderStream: IconvLiteEncoderStream,\n        IconvLiteDecoderStream: IconvLiteDecoderStream,\n    };\n};\n"], "names": [], "mappings": "AAAA;AAEA,IAAI,SAAS,+FAAwB,MAAM;AAE3C,kGAAkG;AAClG,wEAAwE;AACxE,OAAO,OAAO,GAAG,SAAS,aAAa;IACnC,IAAI,YAAY,cAAc,SAAS;IAEvC,4EAA4E;IAE5E,SAAS,uBAAuB,IAAI,EAAE,OAAO;QACzC,IAAI,CAAC,IAAI,GAAG;QACZ,UAAU,WAAW,CAAC;QACtB,QAAQ,aAAa,GAAG,OAAO,2DAA2D;QAC1F,UAAU,IAAI,CAAC,IAAI,EAAE;IACzB;IAEA,uBAAuB,SAAS,GAAG,OAAO,MAAM,CAAC,UAAU,SAAS,EAAE;QAClE,aAAa;YAAE,OAAO;QAAuB;IACjD;IAEA,uBAAuB,SAAS,CAAC,UAAU,GAAG,SAAS,KAAK,EAAE,QAAQ,EAAE,IAAI;QACxE,IAAI,OAAO,SAAS,UAChB,OAAO,KAAK,IAAI,MAAM;QAC1B,IAAI;YACA,IAAI,MAAM,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC;YAC1B,IAAI,OAAO,IAAI,MAAM,EAAE,IAAI,CAAC,IAAI,CAAC;YACjC;QACJ,EACA,OAAO,GAAG;YACN,KAAK;QACT;IACJ;IAEA,uBAAuB,SAAS,CAAC,MAAM,GAAG,SAAS,IAAI;QACnD,IAAI;YACA,IAAI,MAAM,IAAI,CAAC,IAAI,CAAC,GAAG;YACvB,IAAI,OAAO,IAAI,MAAM,EAAE,IAAI,CAAC,IAAI,CAAC;YACjC;QACJ,EACA,OAAO,GAAG;YACN,KAAK;QACT;IACJ;IAEA,uBAAuB,SAAS,CAAC,OAAO,GAAG,SAAS,EAAE;QAClD,IAAI,SAAS,EAAE;QACf,IAAI,CAAC,EAAE,CAAC,SAAS;QACjB,IAAI,CAAC,EAAE,CAAC,QAAQ,SAAS,KAAK;YAAI,OAAO,IAAI,CAAC;QAAQ;QACtD,IAAI,CAAC,EAAE,CAAC,OAAO;YACX,GAAG,MAAM,OAAO,MAAM,CAAC;QAC3B;QACA,OAAO,IAAI;IACf;IAGA,4EAA4E;IAE5E,SAAS,uBAAuB,IAAI,EAAE,OAAO;QACzC,IAAI,CAAC,IAAI,GAAG;QACZ,UAAU,WAAW,CAAC;QACtB,QAAQ,QAAQ,GAAG,IAAI,CAAC,QAAQ,GAAG,QAAQ,qBAAqB;QAChE,UAAU,IAAI,CAAC,IAAI,EAAE;IACzB;IAEA,uBAAuB,SAAS,GAAG,OAAO,MAAM,CAAC,UAAU,SAAS,EAAE;QAClE,aAAa;YAAE,OAAO;QAAuB;IACjD;IAEA,uBAAuB,SAAS,CAAC,UAAU,GAAG,SAAS,KAAK,EAAE,QAAQ,EAAE,IAAI;QACxE,IAAI,CAAC,OAAO,QAAQ,CAAC,UAAU,CAAC,CAAC,iBAAiB,UAAU,GACxD,OAAO,KAAK,IAAI,MAAM;QAC1B,IAAI;YACA,IAAI,MAAM,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC;YAC1B,IAAI,OAAO,IAAI,MAAM,EAAE,IAAI,CAAC,IAAI,CAAC,KAAK,IAAI,CAAC,QAAQ;YACnD;QACJ,EACA,OAAO,GAAG;YACN,KAAK;QACT;IACJ;IAEA,uBAAuB,SAAS,CAAC,MAAM,GAAG,SAAS,IAAI;QACnD,IAAI;YACA,IAAI,MAAM,IAAI,CAAC,IAAI,CAAC,GAAG;YACvB,IAAI,OAAO,IAAI,MAAM,EAAE,IAAI,CAAC,IAAI,CAAC,KAAK,IAAI,CAAC,QAAQ;YACnD;QACJ,EACA,OAAO,GAAG;YACN,KAAK;QACT;IACJ;IAEA,uBAAuB,SAAS,CAAC,OAAO,GAAG,SAAS,EAAE;QAClD,IAAI,MAAM;QACV,IAAI,CAAC,EAAE,CAAC,SAAS;QACjB,IAAI,CAAC,EAAE,CAAC,QAAQ,SAAS,KAAK;YAAI,OAAO;QAAO;QAChD,IAAI,CAAC,EAAE,CAAC,OAAO;YACX,GAAG,MAAM;QACb;QACA,OAAO,IAAI;IACf;IAEA,OAAO;QACH,wBAAwB;QACxB,wBAAwB;IAC5B;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2357, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/New%20folder%20%2825%29/streamzen/node_modules/iconv-lite/lib/index.js"], "sourcesContent": ["\"use strict\";\n\nvar Buffer = require(\"safer-buffer\").Buffer;\n\nvar bomHandling = require(\"./bom-handling\"),\n    iconv = module.exports;\n\n// All codecs and aliases are kept here, keyed by encoding name/alias.\n// They are lazy loaded in `iconv.getCodec` from `encodings/index.js`.\niconv.encodings = null;\n\n// Characters emitted in case of error.\niconv.defaultCharUnicode = '�';\niconv.defaultCharSingleByte = '?';\n\n// Public API.\niconv.encode = function encode(str, encoding, options) {\n    str = \"\" + (str || \"\"); // Ensure string.\n\n    var encoder = iconv.getEncoder(encoding, options);\n\n    var res = encoder.write(str);\n    var trail = encoder.end();\n    \n    return (trail && trail.length > 0) ? Buffer.concat([res, trail]) : res;\n}\n\niconv.decode = function decode(buf, encoding, options) {\n    if (typeof buf === 'string') {\n        if (!iconv.skipDecodeWarning) {\n            console.error('Iconv-lite warning: decode()-ing strings is deprecated. Refer to https://github.com/ashtuchkin/iconv-lite/wiki/Use-Buffers-when-decoding');\n            iconv.skipDecodeWarning = true;\n        }\n\n        buf = Buffer.from(\"\" + (buf || \"\"), \"binary\"); // Ensure buffer.\n    }\n\n    var decoder = iconv.getDecoder(encoding, options);\n\n    var res = decoder.write(buf);\n    var trail = decoder.end();\n\n    return trail ? (res + trail) : res;\n}\n\niconv.encodingExists = function encodingExists(enc) {\n    try {\n        iconv.getCodec(enc);\n        return true;\n    } catch (e) {\n        return false;\n    }\n}\n\n// Legacy aliases to convert functions\niconv.toEncoding = iconv.encode;\niconv.fromEncoding = iconv.decode;\n\n// Search for a codec in iconv.encodings. Cache codec data in iconv._codecDataCache.\niconv._codecDataCache = {};\niconv.getCodec = function getCodec(encoding) {\n    if (!iconv.encodings)\n        iconv.encodings = require(\"../encodings\"); // Lazy load all encoding definitions.\n    \n    // Canonicalize encoding name: strip all non-alphanumeric chars and appended year.\n    var enc = iconv._canonicalizeEncoding(encoding);\n\n    // Traverse iconv.encodings to find actual codec.\n    var codecOptions = {};\n    while (true) {\n        var codec = iconv._codecDataCache[enc];\n        if (codec)\n            return codec;\n\n        var codecDef = iconv.encodings[enc];\n\n        switch (typeof codecDef) {\n            case \"string\": // Direct alias to other encoding.\n                enc = codecDef;\n                break;\n\n            case \"object\": // Alias with options. Can be layered.\n                for (var key in codecDef)\n                    codecOptions[key] = codecDef[key];\n\n                if (!codecOptions.encodingName)\n                    codecOptions.encodingName = enc;\n                \n                enc = codecDef.type;\n                break;\n\n            case \"function\": // Codec itself.\n                if (!codecOptions.encodingName)\n                    codecOptions.encodingName = enc;\n\n                // The codec function must load all tables and return object with .encoder and .decoder methods.\n                // It'll be called only once (for each different options object).\n                codec = new codecDef(codecOptions, iconv);\n\n                iconv._codecDataCache[codecOptions.encodingName] = codec; // Save it to be reused later.\n                return codec;\n\n            default:\n                throw new Error(\"Encoding not recognized: '\" + encoding + \"' (searched as: '\"+enc+\"')\");\n        }\n    }\n}\n\niconv._canonicalizeEncoding = function(encoding) {\n    // Canonicalize encoding name: strip all non-alphanumeric chars and appended year.\n    return (''+encoding).toLowerCase().replace(/:\\d{4}$|[^0-9a-z]/g, \"\");\n}\n\niconv.getEncoder = function getEncoder(encoding, options) {\n    var codec = iconv.getCodec(encoding),\n        encoder = new codec.encoder(options, codec);\n\n    if (codec.bomAware && options && options.addBOM)\n        encoder = new bomHandling.PrependBOM(encoder, options);\n\n    return encoder;\n}\n\niconv.getDecoder = function getDecoder(encoding, options) {\n    var codec = iconv.getCodec(encoding),\n        decoder = new codec.decoder(options, codec);\n\n    if (codec.bomAware && !(options && options.stripBOM === false))\n        decoder = new bomHandling.StripBOM(decoder, options);\n\n    return decoder;\n}\n\n// Streaming API\n// NOTE: Streaming API naturally depends on 'stream' module from Node.js. Unfortunately in browser environments this module can add\n// up to 100Kb to the output bundle. To avoid unnecessary code bloat, we don't enable Streaming API in browser by default.\n// If you would like to enable it explicitly, please add the following code to your app:\n// > iconv.enableStreamingAPI(require('stream'));\niconv.enableStreamingAPI = function enableStreamingAPI(stream_module) {\n    if (iconv.supportsStreams)\n        return;\n\n    // Dependency-inject stream module to create IconvLite stream classes.\n    var streams = require(\"./streams\")(stream_module);\n\n    // Not public API yet, but expose the stream classes.\n    iconv.IconvLiteEncoderStream = streams.IconvLiteEncoderStream;\n    iconv.IconvLiteDecoderStream = streams.IconvLiteDecoderStream;\n\n    // Streaming API.\n    iconv.encodeStream = function encodeStream(encoding, options) {\n        return new iconv.IconvLiteEncoderStream(iconv.getEncoder(encoding, options), options);\n    }\n\n    iconv.decodeStream = function decodeStream(encoding, options) {\n        return new iconv.IconvLiteDecoderStream(iconv.getDecoder(encoding, options), options);\n    }\n\n    iconv.supportsStreams = true;\n}\n\n// Enable Streaming API automatically if 'stream' module is available and non-empty (the majority of environments).\nvar stream_module;\ntry {\n    stream_module = require(\"stream\");\n} catch (e) {}\n\nif (stream_module && stream_module.Transform) {\n    iconv.enableStreamingAPI(stream_module);\n\n} else {\n    // In rare cases where 'stream' module is not available by default, throw a helpful exception.\n    iconv.encodeStream = iconv.decodeStream = function() {\n        throw new Error(\"iconv-lite Streaming API is not enabled. Use iconv.enableStreamingAPI(require('stream')); to enable it.\");\n    };\n}\n\nif (\"Ā\" != \"\\u0100\") {\n    console.error(\"iconv-lite warning: js files use non-utf8 encoding. See https://github.com/ashtuchkin/iconv-lite/wiki/Javascript-source-file-encodings for more info.\");\n}\n"], "names": [], "mappings": "AAAA;AAEA,IAAI,SAAS,+FAAwB,MAAM;AAE3C,IAAI,uHACA,QAAQ,OAAO,OAAO;AAE1B,sEAAsE;AACtE,sEAAsE;AACtE,MAAM,SAAS,GAAG;AAElB,uCAAuC;AACvC,MAAM,kBAAkB,GAAG;AAC3B,MAAM,qBAAqB,GAAG;AAE9B,cAAc;AACd,MAAM,MAAM,GAAG,SAAS,OAAO,GAAG,EAAE,QAAQ,EAAE,OAAO;IACjD,MAAM,KAAK,CAAC,OAAO,EAAE,GAAG,iBAAiB;IAEzC,IAAI,UAAU,MAAM,UAAU,CAAC,UAAU;IAEzC,IAAI,MAAM,QAAQ,KAAK,CAAC;IACxB,IAAI,QAAQ,QAAQ,GAAG;IAEvB,OAAO,AAAC,SAAS,MAAM,MAAM,GAAG,IAAK,OAAO,MAAM,CAAC;QAAC;QAAK;KAAM,IAAI;AACvE;AAEA,MAAM,MAAM,GAAG,SAAS,OAAO,GAAG,EAAE,QAAQ,EAAE,OAAO;IACjD,IAAI,OAAO,QAAQ,UAAU;QACzB,IAAI,CAAC,MAAM,iBAAiB,EAAE;YAC1B,QAAQ,KAAK,CAAC;YACd,MAAM,iBAAiB,GAAG;QAC9B;QAEA,MAAM,OAAO,IAAI,CAAC,KAAK,CAAC,OAAO,EAAE,GAAG,WAAW,iBAAiB;IACpE;IAEA,IAAI,UAAU,MAAM,UAAU,CAAC,UAAU;IAEzC,IAAI,MAAM,QAAQ,KAAK,CAAC;IACxB,IAAI,QAAQ,QAAQ,GAAG;IAEvB,OAAO,QAAS,MAAM,QAAS;AACnC;AAEA,MAAM,cAAc,GAAG,SAAS,eAAe,GAAG;IAC9C,IAAI;QACA,MAAM,QAAQ,CAAC;QACf,OAAO;IACX,EAAE,OAAO,GAAG;QACR,OAAO;IACX;AACJ;AAEA,sCAAsC;AACtC,MAAM,UAAU,GAAG,MAAM,MAAM;AAC/B,MAAM,YAAY,GAAG,MAAM,MAAM;AAEjC,oFAAoF;AACpF,MAAM,eAAe,GAAG,CAAC;AACzB,MAAM,QAAQ,GAAG,SAAS,SAAS,QAAQ;IACvC,IAAI,CAAC,MAAM,SAAS,EAChB,MAAM,SAAS,2GAA4B,sCAAsC;IAErF,kFAAkF;IAClF,IAAI,MAAM,MAAM,qBAAqB,CAAC;IAEtC,iDAAiD;IACjD,IAAI,eAAe,CAAC;IACpB,MAAO,KAAM;QACT,IAAI,QAAQ,MAAM,eAAe,CAAC,IAAI;QACtC,IAAI,OACA,OAAO;QAEX,IAAI,WAAW,MAAM,SAAS,CAAC,IAAI;QAEnC,OAAQ,OAAO;YACX,KAAK;gBACD,MAAM;gBACN;YAEJ,KAAK;gBACD,IAAK,IAAI,OAAO,SACZ,YAAY,CAAC,IAAI,GAAG,QAAQ,CAAC,IAAI;gBAErC,IAAI,CAAC,aAAa,YAAY,EAC1B,aAAa,YAAY,GAAG;gBAEhC,MAAM,SAAS,IAAI;gBACnB;YAEJ,KAAK;gBACD,IAAI,CAAC,aAAa,YAAY,EAC1B,aAAa,YAAY,GAAG;gBAEhC,gGAAgG;gBAChG,iEAAiE;gBACjE,QAAQ,IAAI,SAAS,cAAc;gBAEnC,MAAM,eAAe,CAAC,aAAa,YAAY,CAAC,GAAG,OAAO,8BAA8B;gBACxF,OAAO;YAEX;gBACI,MAAM,IAAI,MAAM,+BAA+B,WAAW,sBAAoB,MAAI;QAC1F;IACJ;AACJ;AAEA,MAAM,qBAAqB,GAAG,SAAS,QAAQ;IAC3C,kFAAkF;IAClF,OAAO,CAAC,KAAG,QAAQ,EAAE,WAAW,GAAG,OAAO,CAAC,sBAAsB;AACrE;AAEA,MAAM,UAAU,GAAG,SAAS,WAAW,QAAQ,EAAE,OAAO;IACpD,IAAI,QAAQ,MAAM,QAAQ,CAAC,WACvB,UAAU,IAAI,MAAM,OAAO,CAAC,SAAS;IAEzC,IAAI,MAAM,QAAQ,IAAI,WAAW,QAAQ,MAAM,EAC3C,UAAU,IAAI,YAAY,UAAU,CAAC,SAAS;IAElD,OAAO;AACX;AAEA,MAAM,UAAU,GAAG,SAAS,WAAW,QAAQ,EAAE,OAAO;IACpD,IAAI,QAAQ,MAAM,QAAQ,CAAC,WACvB,UAAU,IAAI,MAAM,OAAO,CAAC,SAAS;IAEzC,IAAI,MAAM,QAAQ,IAAI,CAAC,CAAC,WAAW,QAAQ,QAAQ,KAAK,KAAK,GACzD,UAAU,IAAI,YAAY,QAAQ,CAAC,SAAS;IAEhD,OAAO;AACX;AAEA,gBAAgB;AAChB,mIAAmI;AACnI,0HAA0H;AAC1H,wFAAwF;AACxF,iDAAiD;AACjD,MAAM,kBAAkB,GAAG,SAAS,mBAAmB,aAAa;IAChE,IAAI,MAAM,eAAe,EACrB;IAEJ,sEAAsE;IACtE,IAAI,UAAU,mGAAqB;IAEnC,qDAAqD;IACrD,MAAM,sBAAsB,GAAG,QAAQ,sBAAsB;IAC7D,MAAM,sBAAsB,GAAG,QAAQ,sBAAsB;IAE7D,iBAAiB;IACjB,MAAM,YAAY,GAAG,SAAS,aAAa,QAAQ,EAAE,OAAO;QACxD,OAAO,IAAI,MAAM,sBAAsB,CAAC,MAAM,UAAU,CAAC,UAAU,UAAU;IACjF;IAEA,MAAM,YAAY,GAAG,SAAS,aAAa,QAAQ,EAAE,OAAO;QACxD,OAAO,IAAI,MAAM,sBAAsB,CAAC,MAAM,UAAU,CAAC,UAAU,UAAU;IACjF;IAEA,MAAM,eAAe,GAAG;AAC5B;AAEA,mHAAmH;AACnH,IAAI;AACJ,IAAI;IACA;AACJ,EAAE,OAAO,GAAG,CAAC;AAEb,IAAI,iBAAiB,cAAc,SAAS,EAAE;IAC1C,MAAM,kBAAkB,CAAC;AAE7B,OAAO;IACH,8FAA8F;IAC9F,MAAM,YAAY,GAAG,MAAM,YAAY,GAAG;QACtC,MAAM,IAAI,MAAM;IACpB;AACJ;AAEA,uCAAqB;;AAErB", "ignoreList": [0], "debugId": null}}]}