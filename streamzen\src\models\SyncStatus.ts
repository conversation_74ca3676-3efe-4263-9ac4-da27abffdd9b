import mongoose, { Schema, Document } from 'mongoose';

export interface ISyncStatus extends Document {
  lastSyncTime?: Date;
  nextSyncTime: Date;
  isRunning: boolean;
  lastSyncResults: {
    movies: number;
    series: number;
    episodes: number;
  };
  createdAt: Date;
  updatedAt: Date;
}

const SyncStatusSchema: Schema = new Schema({
  lastSyncTime: {
    type: Date,
    default: null
  },
  nextSyncTime: {
    type: Date,
    required: true,
    index: true
  },
  isRunning: {
    type: Boolean,
    default: false,
    index: true
  },
  lastSyncResults: {
    movies: {
      type: Number,
      default: 0
    },
    series: {
      type: Number,
      default: 0
    },
    episodes: {
      type: Number,
      default: 0
    }
  }
}, {
  timestamps: true
});

// Index for querying sync status
SyncStatusSchema.index({ createdAt: -1 });
SyncStatusSchema.index({ nextSyncTime: 1, isRunning: 1 });

export default mongoose.models.SyncStatus || mongoose.model<ISyncStatus>('SyncStatus', SyncStatusSchema);
