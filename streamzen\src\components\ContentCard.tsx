'use client';

import React, { useState } from 'react';
import Link from 'next/link';
import Image from 'next/image';
import { Play, Star, Calendar, Sparkles } from 'lucide-react';
import { motion } from 'framer-motion';
import { cn, getImageUrl, formatRating, truncateText } from '@/lib/utils';

interface ContentCardProps {
  id: string;
  imdbId: string;
  title: string;
  year?: number;
  posterUrl?: string;
  imdbRating?: number;
  description?: string;
  type: 'movie' | 'series' | 'episode';
  season?: number;
  episode?: number;
  seriesTitle?: string;
  seriesPosterUrl?: string;
  className?: string;
}

const ContentCard: React.FC<ContentCardProps> = ({
  id,
  imdbId,
  title,
  year,
  posterUrl,
  imdbRating,
  description,
  type,
  season,
  episode,
  seriesTitle,
  seriesPosterUrl,
  className
}) => {
  const [isHovered, setIsHovered] = useState(false);
  const [isLoaded, setIsLoaded] = useState(false);

  const href = type === 'movie'
    ? `/watch/movie/${imdbId}`
    : type === 'series'
    ? `/watch/series/${imdbId}`
    : `/watch/series/${imdbId}?season=${season}&episode=${episode}`;

  const displayTitle = type === 'episode' ? seriesTitle : title;
  const subtitle = type === 'episode' ? `S${season}E${episode}: ${title}` : year ? `${year}` : '';
  const displayPosterUrl = type === 'episode' ? (seriesPosterUrl || posterUrl) : posterUrl;

  return (
    <Link href={href} className="block">
      <motion.div
        className={cn('group relative cursor-pointer', className)}
        onMouseEnter={() => setIsHovered(true)}
        onMouseLeave={() => setIsHovered(false)}
        whileHover={{ y: -4, transition: { duration: 0.2, ease: "easeOut" } }}
        whileTap={{ scale: 0.98 }}
      >
        {/* Main Card Container */}
        <div className="relative aspect-[2/3] bg-gray-900 overflow-hidden rounded-2xl border border-gray-800 hover:border-gray-600 transition-all duration-300 shadow-lg hover:shadow-2xl">

          {/* Poster Image */}
          <div className="relative w-full h-full overflow-hidden">
            <Image
              src={getImageUrl(displayPosterUrl)}
              alt={displayTitle || 'Content poster'}
              fill
              className={cn(
                'object-cover transition-all duration-300',
                isHovered ? 'scale-105' : 'scale-100'
              )}
              sizes="(max-width: 768px) 50vw, (max-width: 1200px) 33vw, 25vw"
              priority={false}
              onLoad={() => setIsLoaded(true)}
            />

            {/* Loading shimmer */}
            {!isLoaded && (
              <div className="absolute inset-0 bg-gradient-to-r from-gray-800 via-gray-700 to-gray-800 animate-pulse" />
            )}
          </div>

          {/* Gradient Overlay */}
          <div className="absolute inset-0 bg-gradient-to-t from-black/80 via-transparent to-transparent" />

          {/* Type Badge */}
          <div className="absolute top-3 left-3 z-10">
            <div className="px-2 py-1 bg-black/70 backdrop-blur-sm rounded-lg border border-white/20">
              <span className="text-white text-xs font-semibold">
                {type === 'episode' ? 'EP' : type === 'movie' ? 'MOVIE' : 'SERIES'}
              </span>
            </div>
          </div>

          {/* Rating Badge */}
          {imdbRating && (
            <div className="absolute top-3 right-3 z-10">
              <div className="flex items-center space-x-1 px-2 py-1 bg-black/70 backdrop-blur-sm rounded-lg border border-white/20">
                <Star size={12} className="text-yellow-400 fill-current" />
                <span className="text-white text-xs font-semibold">{formatRating(imdbRating)}</span>
              </div>
            </div>
          )}

          {/* High Rating Indicator */}
          {imdbRating && imdbRating >= 8.5 && (
            <div className="absolute top-12 right-3 z-10">
              <div className="p-1 bg-gradient-to-r from-yellow-400 to-orange-500 rounded-full">
                <Sparkles size={12} className="text-white" />
              </div>
            </div>
          )}

          {/* Hover Play Button */}
          <motion.div
            className="absolute inset-0 flex items-center justify-center z-20"
            initial={{ opacity: 0, scale: 0.8 }}
            animate={isHovered ? { opacity: 1, scale: 1 } : { opacity: 0, scale: 0.8 }}
            transition={{ duration: 0.2 }}
          >
            <div className="w-16 h-16 bg-red-500 hover:bg-red-600 rounded-full flex items-center justify-center shadow-2xl transition-colors duration-200">
              <Play size={20} className="text-white fill-current ml-1" />
            </div>
          </motion.div>

          {/* Content Info */}
          <div className="absolute bottom-0 left-0 right-0 p-4 z-10">
            <h3 className="text-white font-bold text-sm mb-1 line-clamp-2 leading-tight">
              {displayTitle}
            </h3>
            {subtitle && (
              <p className="text-gray-300 text-xs mb-2 line-clamp-1">
                {subtitle}
              </p>
            )}

            {/* Additional info on hover */}
            {isHovered && description && (
              <motion.p
                className="text-gray-400 text-xs line-clamp-2 leading-relaxed"
                initial={{ opacity: 0, y: 10 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.1 }}
              >
                {truncateText(description, 80)}
              </motion.p>
            )}
          </div>

          {/* Hover Border Glow */}
          <div className={cn(
            'absolute inset-0 rounded-2xl border-2 transition-all duration-300 pointer-events-none',
            isHovered ? 'border-white/30 shadow-lg' : 'border-transparent'
          )} />
        </div>
      </motion.div>
    </Link>
  );
};

export default ContentCard;
