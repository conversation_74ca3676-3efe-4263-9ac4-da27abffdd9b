'use client';

import React, { useState, useRef, useEffect } from 'react';
import Link from 'next/link';
import Image from 'next/image';
import { Play, Star, Calendar, Info, Plus, Sparkles, TrendingUp, Clock, Eye } from 'lucide-react';
import { motion, useMotionValue, useSpring, useTransform } from 'framer-motion';
import { cn, getImageUrl, formatRating, truncateText } from '@/lib/utils';

interface ContentCardProps {
  id: string;
  imdbId: string;
  title: string;
  year?: number;
  posterUrl?: string;
  imdbRating?: number;
  description?: string;
  type: 'movie' | 'series' | 'episode';
  season?: number;
  episode?: number;
  seriesTitle?: string;
  seriesPosterUrl?: string; // For episodes to use series poster
  className?: string;
}

const ContentCard: React.FC<ContentCardProps> = ({
  id,
  imdbId,
  title,
  year,
  posterUrl,
  imdbRating,
  description,
  type,
  season,
  episode,
  seriesTitle,
  seriesPosterUrl,
  className
}) => {
  const [isHovered, setIsHovered] = useState(false);
  const [isLoaded, setIsLoaded] = useState(false);
  const cardRef = useRef<HTMLDivElement>(null);

  // Advanced mouse tracking for 3D effects
  const mouseX = useMotionValue(0);
  const mouseY = useMotionValue(0);
  const rotateX = useSpring(useTransform(mouseY, [-0.5, 0.5], [10, -10]), { stiffness: 300, damping: 30 });
  const rotateY = useSpring(useTransform(mouseX, [-0.5, 0.5], [-10, 10]), { stiffness: 300, damping: 30 });

  const href = type === 'movie'
    ? `/watch/movie/${imdbId}`
    : type === 'series'
    ? `/watch/series/${imdbId}`
    : `/watch/series/${imdbId}?season=${season}&episode=${episode}`;

  const displayTitle = type === 'episode' ? seriesTitle : title;
  const subtitle = type === 'episode' ? `S${season}E${episode}: ${title}` : year ? `${year}` : '';

  // Use series poster for episodes, fallback to episode poster, then to placeholder
  const displayPosterUrl = type === 'episode' ? (seriesPosterUrl || posterUrl) : posterUrl;

  // Enhanced mouse tracking for 3D tilt effect
  const handleMouseMove = (e: React.MouseEvent<HTMLDivElement>) => {
    if (!cardRef.current) return;

    const rect = cardRef.current.getBoundingClientRect();
    const centerX = rect.left + rect.width / 2;
    const centerY = rect.top + rect.height / 2;

    const mouseXPos = (e.clientX - centerX) / (rect.width / 2);
    const mouseYPos = (e.clientY - centerY) / (rect.height / 2);

    mouseX.set(mouseXPos);
    mouseY.set(mouseYPos);
  };

  const handleMouseLeave = () => {
    setIsHovered(false);
    mouseX.set(0);
    mouseY.set(0);
  };

  // Quality indicator based on rating
  const getQualityBadge = () => {
    if (!imdbRating) return null;

    if (imdbRating >= 8.5) return { label: 'Masterpiece', color: 'from-yellow-400 to-orange-500', icon: Sparkles };
    if (imdbRating >= 8.0) return { label: 'Excellent', color: 'from-green-400 to-emerald-500', icon: Star };
    if (imdbRating >= 7.0) return { label: 'Great', color: 'from-blue-400 to-cyan-500', icon: TrendingUp };
    if (imdbRating >= 6.0) return { label: 'Good', color: 'from-purple-400 to-pink-500', icon: Eye };
    return null;
  };

  const qualityBadge = getQualityBadge();

  return (
    <Link href={href} className="block">
      <motion.div
        ref={cardRef}
        className={cn(
          'group relative cursor-pointer perspective-1000',
          className
        )}
        style={{
          rotateX,
          rotateY,
          transformStyle: 'preserve-3d'
        }}
        onMouseMove={handleMouseMove}
        onMouseEnter={() => setIsHovered(true)}
        onMouseLeave={handleMouseLeave}
        whileHover={{
          scale: 1.05,
          y: -8,
          transition: { duration: 0.3, ease: "easeOut" }
        }}
        whileTap={{ scale: 0.98 }}
      >
        {/* Main Card Container with Glassmorphism */}
        <div className="relative aspect-[2/3] bg-gradient-to-br from-gray-900/90 to-black/90 overflow-hidden rounded-3xl backdrop-blur-xl border border-white/10 hover:border-white/20 transition-all duration-500 shadow-2xl hover:shadow-4xl">

          {/* Animated Background Gradient */}
          <div className="absolute inset-0 bg-gradient-to-br from-blue-600/10 via-purple-600/10 to-pink-600/10 opacity-0 group-hover:opacity-100 transition-opacity duration-500" />

          {/* Poster Image with Enhanced Effects */}
          <div className="relative w-full h-full overflow-hidden">
            <Image
              src={getImageUrl(displayPosterUrl)}
              alt={displayTitle || 'Content poster'}
              fill
              className={cn(
                'object-cover transition-all duration-700 ease-out',
                isHovered ? 'scale-110 brightness-75 blur-[1px]' : 'scale-100 brightness-90'
              )}
              sizes="(max-width: 768px) 50vw, (max-width: 1200px) 33vw, 25vw"
              priority={false}
              onLoad={() => setIsLoaded(true)}
            />

            {/* Loading shimmer effect */}
            {!isLoaded && (
              <div className="absolute inset-0 bg-gradient-to-r from-gray-800 via-gray-700 to-gray-800 animate-pulse" />
            )}
          </div>

          {/* Multi-layer Gradient Overlays */}
          <div className="absolute inset-0 bg-gradient-to-t from-black/95 via-black/30 to-transparent" />
          <div className={cn(
            'absolute inset-0 bg-gradient-to-br from-transparent via-transparent to-black/50 transition-opacity duration-500',
            isHovered ? 'opacity-100' : 'opacity-60'
          )} />

          {/* Glassmorphism Inner Glow */}
          <div className={cn(
            'absolute inset-0 rounded-3xl transition-all duration-500',
            isHovered ? 'shadow-[inset_0_0_60px_rgba(255,255,255,0.15)]' : 'shadow-[inset_0_0_20px_rgba(255,255,255,0.05)]'
          )} />

          {/* Enhanced Quality Badge */}
          {qualityBadge && (
            <motion.div
              className="absolute top-3 right-3 z-20"
              initial={{ opacity: 0, scale: 0.8 }}
              animate={{ opacity: 1, scale: 1 }}
              transition={{ delay: 0.2 }}
            >
              <div className={cn(
                'px-3 py-1.5 rounded-full flex items-center space-x-1.5 backdrop-blur-xl border border-white/20 shadow-lg',
                `bg-gradient-to-r ${qualityBadge.color}`
              )}>
                <qualityBadge.icon size={12} className="text-white" />
                <span className="text-white text-xs font-bold">{formatRating(imdbRating!)}</span>
              </div>
            </motion.div>
          )}

          {/* Type Badge with Enhanced Design */}
          <motion.div
            className="absolute top-3 left-3 z-20"
            initial={{ opacity: 0, x: -20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ delay: 0.1 }}
          >
            <div className="px-3 py-1.5 rounded-full backdrop-blur-xl bg-black/40 border border-white/20 shadow-lg">
              <span className="text-white text-xs font-bold uppercase tracking-wider">
                {type === 'episode' ? '📺 Episode' : type === 'movie' ? '🎬 Movie' : '📺 Series'}
              </span>
            </div>
          </motion.div>

          {/* Trending Indicator for High Ratings */}
          {imdbRating && imdbRating >= 8.5 && (
            <motion.div
              className="absolute top-3 left-1/2 -translate-x-1/2 z-20"
              initial={{ opacity: 0, y: -20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.3 }}
            >
              <div className="px-2 py-1 rounded-full backdrop-blur-xl bg-gradient-to-r from-yellow-400/20 to-orange-500/20 border border-yellow-400/30 shadow-lg">
                <Sparkles size={12} className="text-yellow-400" />
              </div>
            </motion.div>
          )}

          {/* Enhanced Hover Play Button with Pulse Animation */}
          <motion.div
            className="absolute inset-0 flex items-center justify-center pointer-events-none z-30"
            initial={{ opacity: 0, scale: 0.5 }}
            animate={isHovered ? { opacity: 1, scale: 1 } : { opacity: 0, scale: 0.5 }}
            transition={{ duration: 0.3, ease: "easeOut" }}
          >
            <motion.div
              className="relative"
              whileHover={{ scale: 1.1 }}
              whileTap={{ scale: 0.95 }}
            >
              {/* Pulsing background ring */}
              <div className="absolute inset-0 w-20 h-20 bg-red-500/30 rounded-full animate-ping" />

              {/* Main play button */}
              <div className="relative w-20 h-20 bg-gradient-to-br from-red-500 to-red-600 rounded-full flex items-center justify-center shadow-2xl border-2 border-white/30 backdrop-blur-sm">
                <Play size={28} className="text-white fill-current ml-1" />
              </div>
            </motion.div>
          </motion.div>

          {/* Enhanced Bottom Content Info with Slide Animation */}
          <motion.div
            className="absolute bottom-0 left-0 right-0 p-4 pointer-events-none"
            initial={{ y: 20, opacity: 0.8 }}
            animate={isHovered ? { y: 0, opacity: 1 } : { y: 20, opacity: 0.8 }}
            transition={{ duration: 0.3 }}
          >
            <div className="space-y-2">
              <h3 className="text-white font-bold text-base line-clamp-2 leading-tight drop-shadow-2xl">
                {displayTitle}
              </h3>

              {subtitle && (
                <p className="text-gray-200 text-sm font-medium drop-shadow-lg">
                  {subtitle}
                </p>
              )}

              {/* Additional info on hover */}
              {isHovered && description && (
                <motion.p
                  className="text-gray-300 text-xs line-clamp-2 leading-relaxed drop-shadow-md"
                  initial={{ opacity: 0, y: 10 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: 0.2 }}
                >
                  {truncateText(description, 80)}
                </motion.p>
              )}
            </div>
          </motion.div>

          {/* Premium Border Glow with Enhanced Effects */}
          <div className={cn(
            'absolute inset-0 rounded-3xl border transition-all duration-500 pointer-events-none',
            isHovered
              ? 'border-white/30 shadow-[0_0_40px_rgba(255,255,255,0.2),inset_0_0_40px_rgba(255,255,255,0.1)]'
              : 'border-white/10 shadow-[0_0_20px_rgba(255,255,255,0.05)]'
          )} />

          {/* Floating particles effect on hover */}
          {isHovered && (
            <div className="absolute inset-0 overflow-hidden rounded-3xl pointer-events-none">
              {[...Array(6)].map((_, i) => (
                <motion.div
                  key={i}
                  className="absolute w-1 h-1 bg-white/40 rounded-full"
                  initial={{
                    x: Math.random() * 100 + '%',
                    y: '100%',
                    opacity: 0
                  }}
                  animate={{
                    y: '-10%',
                    opacity: [0, 1, 0],
                  }}
                  transition={{
                    duration: 2,
                    repeat: Infinity,
                    delay: i * 0.3,
                    ease: "easeOut"
                  }}
                />
              ))}
            </div>
          )}
        </div>
      </motion.div>
    </Link>
  );
};

export default ContentCard;
