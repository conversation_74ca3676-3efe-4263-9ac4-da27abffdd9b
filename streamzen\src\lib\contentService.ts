import connectDB from './mongodb';
import Movie, { IMovie } from '../models/Movie';
import Series, { ISeries } from '../models/Series';
import Episode, { IEpisode } from '../models/Episode';
import Request, { IRequest } from '../models/Request';
import IMDbScraper from './scraper';
import VidSrcAPI from './vidsrc';

export interface ContentFilters {
  genre?: string;
  year?: number;
  language?: string;
  country?: string;
  rating?: string;
  quality?: string;
  sortBy?: 'title' | 'year' | 'imdbRating' | 'popularity' | 'createdAt';
  sortOrder?: 'asc' | 'desc';
  page?: number;
  limit?: number;
  search?: string;
}

export interface PaginatedResponse<T> {
  data: T[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    pages: number;
  };
}

export interface GenreCount {
  genre: string;
  count: number;
}

export interface LanguageCount {
  language: string;
  count: number;
}

export interface CountryCount {
  country: string;
  count: number;
}

export interface FilterOptions {
  genres: GenreCount[];
  languages: LanguageCount[];
  countries: CountryCount[];
  years: number[];
  ratings: string[];
  qualities: string[];
}

class ContentService {
  private static instance: ContentService;
  private scraper: IMDbScraper;
  private vidsrc: VidSrcAPI;

  constructor() {
    this.scraper = IMDbScraper.getInstance();
    this.vidsrc = VidSrcAPI.getInstance();
  }

  static getInstance(): ContentService {
    if (!ContentService.instance) {
      ContentService.instance = new ContentService();
    }
    return ContentService.instance;
  }

  async getMovies(filters: ContentFilters = {}): Promise<PaginatedResponse<IMovie>> {
    await connectDB();

    const {
      genre,
      year,
      language,
      country,
      rating,
      quality,
      sortBy = 'createdAt',
      sortOrder = 'desc',
      page = 1,
      limit = 24, // Default to 24 for better UI layout
      search
    } = filters;

    console.log(`🎬 Database query - Page: ${page}, Limit: ${limit}, Sort: ${sortBy} ${sortOrder}`);

    // Build query
    const query: any = {};

    if (genre) query.genres = { $in: [genre] };
    if (year) query.year = year;
    if (language) query.language = language;
    if (country) query.country = country;
    if (rating) query.rating = rating;
    if (quality) query.quality = quality;
    if (search) {
      query.$or = [
        { title: { $regex: search, $options: 'i' } },
        { description: { $regex: search, $options: 'i' } }
      ];
    }

    // Build sort
    const sort: any = {};
    sort[sortBy] = sortOrder === 'asc' ? 1 : -1;

    const skip = (page - 1) * limit;

    console.log(`📊 Query: ${JSON.stringify(query)}, Skip: ${skip}, Limit: ${limit}`);

    try {
      // Use lean() for better performance and only select necessary fields
      const data = await Movie.find(query, {
        title: 1,
        year: 1,
        genres: 1,
        imdbRating: 1,
        posterUrl: 1,
        embedUrl: 1,
        vidsrcUrl: 1,
        description: 1,
        runtime: 1,
        language: 1,
        country: 1,
        rating: 1,
        director: 1,
        cast: 1,
        imdbId: 1,
        createdAt: 1
      })
        .sort(sort)
        .skip(skip)
        .limit(limit)
        .lean()
        .exec();

      console.log(`✅ Fetched ${data.length} movies from database`);

      // For total count, use estimatedDocumentCount for better performance on large collections
      // Only get exact count when filters are applied
      let total: number;

      if (Object.keys(query).length === 0) {
        // No filters - use estimated count for much better performance
        total = await Movie.estimatedDocumentCount();
        console.log(`📈 Using estimated count: ${total}`);
      } else {
        // Filters applied - need exact count but limit the counting
        total = await Movie.countDocuments(query).maxTimeMS(5000); // 5 second timeout
        console.log(`📊 Exact count with filters: ${total}`);
      }

      return {
        data,
        pagination: {
          page,
          limit,
          total,
          pages: Math.ceil(total / limit)
        }
      };

    } catch (error) {
      console.error('❌ Database query error:', error);

      // Fallback: return limited data without total count if query fails
      const data = await Movie.find(query)
        .sort(sort)
        .skip(skip)
        .limit(limit)
        .lean()
        .exec();

      return {
        data,
        pagination: {
          page,
          limit,
          total: data.length, // Fallback total
          pages: 1 // Fallback pages
        }
      };
    }
  }

  async getSeries(filters: ContentFilters = {}): Promise<PaginatedResponse<ISeries>> {
    await connectDB();

    const {
      genre,
      year,
      language,
      country,
      rating,
      sortBy = 'createdAt',
      sortOrder = 'desc',
      page = 1,
      limit = 24,
      search
    } = filters;

    console.log(`📺 Database query - Page: ${page}, Limit: ${limit}, Sort: ${sortBy} ${sortOrder}`);

    const query: any = {};

    if (genre) query.genres = { $in: [genre] };
    if (year) query.startYear = year;
    if (language) query.language = language;
    if (country) query.country = country;
    if (rating) query.rating = rating;
    if (search) {
      query.$or = [
        { title: { $regex: search, $options: 'i' } },
        { description: { $regex: search, $options: 'i' } }
      ];
    }

    const sort: any = {};
    sort[sortBy === 'year' ? 'startYear' : sortBy] = sortOrder === 'asc' ? 1 : -1;

    const skip = (page - 1) * limit;

    console.log(`📊 Series Query: ${JSON.stringify(query)}, Skip: ${skip}, Limit: ${limit}`);

    try {
      // Use lean() for better performance and only select necessary fields
      const data = await Series.find(query, {
        title: 1,
        startYear: 1,
        endYear: 1,
        genres: 1,
        imdbRating: 1,
        posterUrl: 1,
        embedUrl: 1,
        vidsrcUrl: 1,
        description: 1,
        language: 1,
        country: 1,
        rating: 1,
        creator: 1,
        cast: 1,
        totalSeasons: 1,
        status: 1,
        imdbId: 1,
        createdAt: 1
      })
        .sort(sort)
        .skip(skip)
        .limit(limit)
        .lean()
        .exec();

      console.log(`✅ Fetched ${data.length} series from database`);

      // For total count, use estimatedDocumentCount for better performance
      let total: number;

      if (Object.keys(query).length === 0) {
        total = await Series.estimatedDocumentCount();
        console.log(`📈 Using estimated series count: ${total}`);
      } else {
        total = await Series.countDocuments(query).maxTimeMS(5000);
        console.log(`📊 Exact series count with filters: ${total}`);
      }

      return {
        data,
        pagination: {
          page,
          limit,
          total,
          pages: Math.ceil(total / limit)
        }
      };

    } catch (error) {
      console.error('❌ Series database query error:', error);

      // Fallback
      const data = await Series.find(query)
        .sort(sort)
        .skip(skip)
        .limit(limit)
        .lean()
        .exec();

      return {
        data,
        pagination: {
          page,
          limit,
          total: data.length,
          pages: 1
        }
      };
    }
  }

  async getEpisodes(filters: ContentFilters = {}): Promise<PaginatedResponse<IEpisode>> {
    await connectDB();

    const {
      genre,
      quality,
      sortBy = 'createdAt',
      sortOrder = 'desc',
      page = 1,
      limit = 20,
      search,
      language,
      country,
      year
    } = filters;

    console.log('🔍 Episodes Query using SERIES collection - Filters:', filters);

    // Check series collection stats
    const totalSeriesCount = await Series.countDocuments({});
    console.log(`📊 Database Stats: ${totalSeriesCount} total series in database`);

    // Build query for SERIES collection
    const matchStage: any = {};

    if (genre) matchStage.genres = { $in: [genre] };
    if (language) matchStage.language = language;
    if (country) matchStage.country = country;
    if (year) matchStage.startYear = year;
    if (search) {
      matchStage.$or = [
        { title: { $regex: search, $options: 'i' } },
        { description: { $regex: search, $options: 'i' } }
      ];
    }

    const sort: any = {};
    sort[sortBy] = sortOrder === 'asc' ? 1 : -1;

    const skip = (page - 1) * limit;

    // Query SERIES collection and get latest episode for each series
    console.log('🔍 Using SERIES collection to get latest episodes');

    // Aggregation pipeline to get series with their latest episodes
    const aggregationPipeline = [
      { $match: matchStage },
      // Lookup episodes for each series
      {
        $lookup: {
          from: 'episodes',
          localField: 'imdbId',
          foreignField: 'imdbId',
          as: 'episodes'
        }
      },
      // Filter out series that don't have episodes
      {
        $match: {
          'episodes.0': { $exists: true }
        }
      },
      // Get the latest episode from each series
      {
        $addFields: {
          latestEpisode: {
            $arrayElemAt: [
              {
                $sortArray: {
                  input: '$episodes',
                  sortBy: {
                    season: -1,
                    episode: -1,
                    createdAt: -1
                  }
                }
              },
              0
            ]
          }
        }
      },
      // Create episode document with series information
      {
        $addFields: {
          episodeData: {
            _id: '$latestEpisode._id',
            imdbId: '$imdbId',
            seriesTitle: '$title',
            season: '$latestEpisode.season',
            episode: '$latestEpisode.episode',
            episodeTitle: '$latestEpisode.episodeTitle',
            description: '$latestEpisode.description',
            posterUrl: {
              $cond: {
                if: { $and: [{ $ne: ['$latestEpisode.posterUrl', ''] }, { $ne: ['$latestEpisode.posterUrl', null] }] },
                then: '$latestEpisode.posterUrl',
                else: '$posterUrl'
              }
            },
            seriesPosterUrl: '$posterUrl',
            runtime: '$latestEpisode.runtime',
            imdbRating: '$latestEpisode.imdbRating',
            airDate: '$latestEpisode.airDate',
            embedUrl: '$latestEpisode.embedUrl',
            genres: '$genres',
            createdAt: '$latestEpisode.createdAt',
            updatedAt: '$latestEpisode.updatedAt'
          }
        }
      },
      // Replace root with episode data
      {
        $replaceRoot: { newRoot: '$episodeData' }
      },
      // Sort by creation date
      { $sort: sort },
      { $skip: skip },
      { $limit: limit }
    ];

    // Count pipeline for series that have episodes
    const countPipeline = [
      { $match: matchStage },
      // Lookup episodes for each series
      {
        $lookup: {
          from: 'episodes',
          localField: 'imdbId',
          foreignField: 'imdbId',
          as: 'episodes'
        }
      },
      // Filter out series that don't have episodes
      {
        $match: {
          'episodes.0': { $exists: true }
        }
      },
      {
        $count: 'total'
      }
    ];

    console.log('🔍 Querying SERIES collection for latest episodes');

    // Debug: Check series with episodes
    const sampleSeries = await Series.find({}).limit(3).lean();
    console.log('📝 Sample series:', sampleSeries.map(s => ({
      imdbId: s.imdbId,
      title: s.title,
      genres: s.genres
    })));

    const [episodeData, totalResult] = await Promise.all([
      Series.aggregate(aggregationPipeline),
      Series.aggregate(countPipeline)
    ]);

    const data = episodeData;
    const total = totalResult.length > 0 ? totalResult[0].total : 0;

    console.log(`📊 Episodes Query Results: Found ${data.length} latest episodes from ${total} series with episodes`);

    // Debug: Show some results
    if (data.length > 0) {
      console.log('📝 Sample results:', data.slice(0, 3).map(ep => ({
        imdbId: ep.imdbId,
        seriesTitle: ep.seriesTitle,
        season: ep.season,
        episode: ep.episode
      })));
    }

    // Data is already enriched with series information from aggregation pipeline
    console.log(`✅ Enhanced Episodes Query - Found ${data.length} episodes (${total} total)`);

    return {
      data,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit)
      }
    };
  }

  async getMovieById(id: string): Promise<IMovie | null> {
    await connectDB();
    return Movie.findById(id).lean();
  }

  async getMovieByImdbId(imdbId: string): Promise<IMovie | null> {
    await connectDB();
    return Movie.findOne({ imdbId }).lean();
  }

  async getSeriesById(id: string): Promise<ISeries | null> {
    await connectDB();
    return Series.findById(id).lean();
  }

  async getSeriesByImdbId(imdbId: string): Promise<ISeries | null> {
    await connectDB();
    return Series.findOne({ imdbId }).lean();
  }

  async getSeriesEpisodes(imdbId: string, season?: number): Promise<IEpisode[]> {
    await connectDB();
    const query: any = { imdbId };
    if (season) query.season = season;

    // Use aggregation to join with series data and get poster URLs
    const existingEpisodes = await Episode.aggregate([
      { $match: query },
      { $sort: { season: 1, episode: 1 } },
      {
        $lookup: {
          from: 'series',
          localField: 'imdbId',
          foreignField: 'imdbId',
          as: 'seriesData'
        }
      },
      {
        $addFields: {
          seriesPosterUrl: { $arrayElemAt: ['$seriesData.posterUrl', 0] },
          posterUrl: {
            $cond: {
              if: { $ne: ['$posterUrl', ''] },
              then: '$posterUrl',
              else: { $arrayElemAt: ['$seriesData.posterUrl', 0] }
            }
          }
        }
      },
      {
        $project: {
          seriesData: 0 // Remove the joined series data to keep response clean
        }
      }
    ]);

    // Auto-populate missing episodes
    const populatedEpisodes = await this.autoPopulateMissingEpisodes(imdbId, existingEpisodes, season);

    return populatedEpisodes.sort((a, b) => {
      if (a.season !== b.season) return a.season - b.season;
      return a.episode - b.episode;
    });
  }

  private async autoPopulateMissingEpisodes(imdbId: string, existingEpisodes: IEpisode[], targetSeason?: number): Promise<IEpisode[]> {
    const episodeMap = new Map<string, IEpisode>();

    // Add existing episodes to map
    existingEpisodes.forEach(ep => {
      episodeMap.set(`${ep.season}-${ep.episode}`, ep);
    });

    // Get series info for poster
    const series = await this.getSeriesByImdbId(imdbId);
    const seriesPosterUrl = series?.posterUrl || '';

    // Group by season and find missing episodes
    const seasonGroups = new Map<number, number[]>();
    existingEpisodes.forEach(ep => {
      if (!seasonGroups.has(ep.season)) {
        seasonGroups.set(ep.season, []);
      }
      seasonGroups.get(ep.season)!.push(ep.episode);
    });

    // For each season, fill in missing episodes
    for (const [seasonNum, episodes] of seasonGroups) {
      if (targetSeason && seasonNum !== targetSeason) continue;

      const maxEpisode = Math.max(...episodes);

      // Create missing episodes from 1 to maxEpisode
      for (let epNum = 1; epNum <= maxEpisode; epNum++) {
        const key = `${seasonNum}-${epNum}`;
        if (!episodeMap.has(key)) {
          // Create placeholder episode
          const placeholderEpisode: IEpisode = {
            _id: new Date().getTime().toString() + Math.random().toString(36),
            imdbId,
            season: seasonNum,
            episode: epNum,
            episodeTitle: `Episode ${epNum}`,
            description: `Episode ${epNum} of Season ${seasonNum}`,
            posterUrl: seriesPosterUrl,
            seriesPosterUrl: seriesPosterUrl,
            runtime: '45 min',
            imdbRating: 0,
            airDate: new Date().toISOString(),
            createdAt: new Date(),
            updatedAt: new Date()
          };

          episodeMap.set(key, placeholderEpisode);
        }
      }
    }

    return Array.from(episodeMap.values());
  }

  async processImdbId(imdbId: string): Promise<{ success: boolean; type?: string; error?: string }> {
    try {
      await connectDB();

      // Check if already exists
      const existingMovie = await Movie.findOne({ imdbId });
      const existingSeries = await Series.findOne({ imdbId });

      if (existingMovie || existingSeries) {
        console.log(`⚠️ Content already exists: ${imdbId}`);
        return { success: true, type: existingMovie ? 'movie' : 'series' };
      }

      console.log(`🔍 Enhanced IMDb scraping for: ${imdbId}`);

      // First, detect content type by fetching basic info
      let contentType: 'movie' | 'series';
      let imdbData: any;

      try {
        // Try scraping as movie first
        imdbData = await this.scraper.scrapeMovie(imdbId);
        contentType = 'movie';
        console.log(`🎯 Auto-detected content type: movie for ${imdbData.title}`);
      } catch (movieError) {
        try {
          // If movie fails, try as series
          imdbData = await this.scraper.scrapeSeries(imdbId);
          contentType = 'series';
          console.log(`🎯 Auto-detected content type: series for ${imdbData.title}`);
        } catch (seriesError) {
          console.error(`❌ Failed to scrape as both movie and series:`, { movieError, seriesError });
          throw new Error('Failed to scrape IMDb data - content may not exist or be inaccessible');
        }
      }

      if (!imdbData) {
        throw new Error('Failed to scrape IMDb data - content may not exist or be inaccessible');
      }

      if (contentType === 'movie') {
        // Create movie with enhanced metadata
        const embedUrl = this.vidsrc.generateMovieEmbedUrl(imdbId);

        await Movie.create({
          title: imdbData.title,
          year: imdbData.year, // Required field
          description: imdbData.description || 'No description available',
          releaseDate: new Date(imdbData.year, 0, 1), // Convert year to date
          runtime: imdbData.runtime,
          genres: imdbData.genres || [],
          cast: imdbData.cast || [],
          director: imdbData.director,
          rating: imdbData.rating, // MPAA rating
          imdbRating: imdbData.imdbRating, // IMDb numeric rating
          imdbId: imdbId,
          posterUrl: imdbData.posterUrl,
          backdropUrl: imdbData.backdropUrl,
          trailerUrl: imdbData.trailerUrl,
          language: imdbData.language,
          country: imdbData.country,
          embedUrl,
          createdAt: new Date(),
          updatedAt: new Date()
        });

        console.log(`✅ Movie created with full metadata: ${imdbData.title}`);
        return { success: true, type: 'movie' };
      } else {
        // Create series with enhanced episode handling
        console.log(`🔍 Series data received:`, JSON.stringify(imdbData, null, 2));

        // Generate a default embed URL for the series (S1E1)
        const embedUrl = this.vidsrc.generateEpisodeEmbedUrl(imdbId, 1, 1);

        const series = await Series.create({
          title: imdbData.title,
          startYear: imdbData.startYear || new Date().getFullYear(), // Required field with fallback
          endYear: imdbData.endYear,
          description: imdbData.description || 'No description available',
          genres: imdbData.genres || [],
          cast: imdbData.cast || [],
          creator: imdbData.creator,
          rating: imdbData.rating, // MPAA rating
          imdbRating: imdbData.imdbRating, // IMDb numeric rating
          imdbId: imdbId,
          posterUrl: imdbData.posterUrl,
          backdropUrl: imdbData.backdropUrl,
          trailerUrl: imdbData.trailerUrl,
          language: imdbData.language,
          country: imdbData.country,
          totalSeasons: imdbData.totalSeasons || 1,
          status: imdbData.status || 'ongoing',
          embedUrl: embedUrl, // Default to S1E1
          createdAt: new Date(),
          updatedAt: new Date()
        });

        console.log(`✅ Series created with full metadata: ${imdbData.title}`);
        console.log(`📺 Episodes will be populated separately using the episode checking system`);

        return { success: true, type: 'series' };
      }
    } catch (error) {
      console.error(`❌ Error processing IMDb ID ${imdbId}:`, error);
      const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';
      return { success: false, error: errorMessage };
    }
  }

  async createBulkRequest(imdbIds: string[], submittedBy?: string, contentType: 'auto' | 'movie' | 'series' = 'auto'): Promise<IRequest> {
    await connectDB();

    const request = await Request.create({
      imdbIds,
      totalCount: imdbIds.length,
      submittedBy,
      contentType
    });

    // Process in background (don't await)
    this.processBulkRequest(request._id.toString()).catch(console.error);

    return request;
  }

  private async processBulkRequest(requestId: string): Promise<void> {
    await connectDB();
    
    const request = await Request.findById(requestId);
    if (!request) return;

    await Request.findByIdAndUpdate(requestId, { status: 'processing' });

    let processedCount = 0;
    const failedIds: string[] = [];
    const errorMessages: string[] = [];

    for (const imdbId of request.imdbIds) {
      try {
        const result = await this.processImdbId(imdbId);
        if (result.success) {
          processedCount++;
        } else {
          failedIds.push(imdbId);
          errorMessages.push(result.error || 'Unknown error');
        }
      } catch (error) {
        failedIds.push(imdbId);
        errorMessages.push(error.message);
      }

      // Update progress
      await Request.findByIdAndUpdate(requestId, {
        processedCount,
        failedIds,
        errorMessages
      });

      // Rate limiting
      await new Promise(resolve => setTimeout(resolve, 2000));
    }

    // Mark as completed
    await Request.findByIdAndUpdate(requestId, {
      status: processedCount === request.imdbIds.length ? 'completed' : 'failed',
      completedAt: new Date()
    });
  }

  async getRequestStatus(requestId: string): Promise<IRequest | null> {
    await connectDB();
    return Request.findById(requestId).lean();
  }

  async syncLatestContent(): Promise<{ success: boolean; counts: { movies: number; series: number; episodes: number } }> {
    try {
      const { movies, series, episodes } = await this.vidsrc.syncLatestContent();
      
      let movieCount = 0;
      let seriesCount = 0;
      let episodeCount = 0;

      // Process movies
      for (const movie of movies) {
        const result = await this.processImdbId(movie.imdb_id);
        if (result.success && result.type === 'movie') movieCount++;
      }

      // Process series
      for (const show of series) {
        const result = await this.processImdbId(show.imdb_id);
        if (result.success && result.type === 'series') seriesCount++;
      }

      // Process episodes
      await connectDB();
      for (const episode of episodes) {
        try {
          const embedUrl = this.vidsrc.generateEpisodeEmbedUrl(
            episode.imdb_id,
            parseInt(episode.season),
            parseInt(episode.episode)
          );

          // Get genres from series for new episodes
          const genres = await this.populateEpisodeGenres(episode.imdb_id);

          await Episode.findOneAndUpdate(
            {
              imdbId: episode.imdb_id,
              season: parseInt(episode.season),
              episode: parseInt(episode.episode)
            },
            {
              imdbId: episode.imdb_id,
              seriesTitle: episode.show_title,
              season: parseInt(episode.season),
              episode: parseInt(episode.episode),
              embedUrl,
              quality: episode.quality,
              genres: genres // Populate genres from series
            },
            { upsert: true }
          );

          episodeCount++;
        } catch (error) {
          console.error(`Error processing episode ${episode.imdb_id} S${episode.season}E${episode.episode}:`, error);
        }
      }

      return {
        success: true,
        counts: { movies: movieCount, series: seriesCount, episodes: episodeCount }
      };
    } catch (error) {
      console.error('Error syncing latest content:', error);
      return {
        success: false,
        counts: { movies: 0, series: 0, episodes: 0 }
      };
    }
  }

  async addEpisode(episodeData: Partial<IEpisode>): Promise<IEpisode> {
    await connectDB();

    // Check if series exists, if not create it
    if (episodeData.imdbId) {
      await this.ensureSeriesExists(episodeData.imdbId, episodeData);

      // Populate genres from series if not already provided
      if (!episodeData.genres) {
        episodeData.genres = await this.populateEpisodeGenres(episodeData.imdbId);
      }
    }

    const episode = new Episode(episodeData);
    return episode.save();
  }

  private async ensureSeriesExists(imdbId: string, episodeData: Partial<IEpisode>): Promise<void> {
    const existingSeries = await this.getSeriesByImdbId(imdbId);

    if (!existingSeries) {
      // Create series from episode data
      const seriesData: Partial<ISeries> = {
        imdbId,
        title: episodeData.seriesTitle || `Series ${imdbId}`,
        description: episodeData.description || `Series for ${episodeData.seriesTitle || imdbId}`,
        posterUrl: episodeData.seriesPosterUrl || episodeData.posterUrl || '',
        backdropUrl: episodeData.seriesPosterUrl || episodeData.posterUrl || '',
        releaseDate: episodeData.airDate || new Date().toISOString(),
        genres: ['Drama'], // Default genre
        imdbRating: episodeData.imdbRating || 0,
        runtime: episodeData.runtime || '45 min',
        status: 'Ongoing',
        totalSeasons: episodeData.season || 1,
        totalEpisodes: 1,
        language: 'English',
        country: 'US',
        network: 'Unknown',
        createdAt: new Date(),
        updatedAt: new Date()
      };

      const series = new Series(seriesData);
      await series.save();
      console.log(`Auto-created series: ${seriesData.title} (${imdbId})`);
    }
  }

  async getMovieFilterOptions(): Promise<FilterOptions> {
    await connectDB();

    // Get genre counts using aggregation
    const genreCounts = await Movie.aggregate([
      { $unwind: '$genres' },
      { $group: { _id: '$genres', count: { $sum: 1 } } },
      { $sort: { count: -1 } },
      { $project: { genre: '$_id', count: 1, _id: 0 } }
    ]);

    // Get language counts
    const languageCounts = await Movie.aggregate([
      { $match: { language: { $exists: true, $ne: null, $ne: '' } } },
      { $group: { _id: '$language', count: { $sum: 1 } } },
      { $sort: { count: -1 } },
      { $project: { language: '$_id', count: 1, _id: 0 } }
    ]);

    // Get country counts
    const countryCounts = await Movie.aggregate([
      { $match: { country: { $exists: true, $ne: null, $ne: '' } } },
      { $group: { _id: '$country', count: { $sum: 1 } } },
      { $sort: { count: -1 } },
      { $project: { country: '$_id', count: 1, _id: 0 } }
    ]);

    // Get unique years
    const years = await Movie.distinct('year', { year: { $exists: true, $ne: null } });

    // Get unique ratings
    const ratings = await Movie.distinct('rating', { rating: { $exists: true, $ne: null } });

    // Get unique qualities
    const qualities = await Movie.distinct('quality', { quality: { $exists: true, $ne: null } });

    return {
      genres: genreCounts,
      languages: languageCounts,
      countries: countryCounts,
      years: years.filter(Boolean).sort((a, b) => b - a),
      ratings: ratings.filter(Boolean).sort(),
      qualities: qualities.filter(Boolean).sort()
    };
  }

  async getSeriesFilterOptions(): Promise<FilterOptions> {
    await connectDB();

    // Get genre counts using aggregation
    const genreCounts = await Series.aggregate([
      { $unwind: '$genres' },
      { $group: { _id: '$genres', count: { $sum: 1 } } },
      { $sort: { count: -1 } },
      { $project: { genre: '$_id', count: 1, _id: 0 } }
    ]);

    // Get language counts
    const languageCounts = await Series.aggregate([
      { $match: { language: { $exists: true, $ne: null, $ne: '' } } },
      { $group: { _id: '$language', count: { $sum: 1 } } },
      { $sort: { count: -1 } },
      { $project: { language: '$_id', count: 1, _id: 0 } }
    ]);

    // Get country counts
    const countryCounts = await Series.aggregate([
      { $match: { country: { $exists: true, $ne: null, $ne: '' } } },
      { $group: { _id: '$country', count: { $sum: 1 } } },
      { $sort: { count: -1 } },
      { $project: { country: '$_id', count: 1, _id: 0 } }
    ]);

    // Get unique years (using startYear for series)
    const years = await Series.distinct('startYear', { startYear: { $exists: true, $ne: null } });

    // Get unique ratings
    const ratings = await Series.distinct('rating', { rating: { $exists: true, $ne: null } });

    return {
      genres: genreCounts,
      languages: languageCounts,
      countries: countryCounts,
      years: years.filter(Boolean).sort((a, b) => b - a),
      ratings: ratings.filter(Boolean).sort(),
      qualities: [] // Series don't have quality field
    };
  }

  async getEpisodeFilterOptions(): Promise<FilterOptions> {
    await connectDB();

    console.log('🔍 Getting enhanced episode filter options with series data...');

    // Enhanced aggregation to get filter options from both episodes and series
    const [genreCounts, languageCounts, countryCounts, yearCounts, qualities] = await Promise.all([
      // Genre counts from episodes
      Episode.aggregate([
        { $unwind: '$genres' },
        { $group: { _id: '$genres', count: { $sum: 1 } } },
        { $sort: { count: -1 } },
        { $project: { genre: '$_id', count: 1, _id: 0 } }
      ]),

      // Language counts from series (via episodes)
      Episode.aggregate([
        {
          $lookup: {
            from: 'series',
            localField: 'imdbId',
            foreignField: 'imdbId',
            as: 'seriesInfo'
          }
        },
        {
          $addFields: {
            seriesLanguage: { $arrayElemAt: ['$seriesInfo.language', 0] }
          }
        },
        {
          $match: {
            seriesLanguage: { $exists: true, $ne: null, $ne: '' }
          }
        },
        {
          $group: {
            _id: '$seriesLanguage',
            count: { $sum: 1 }
          }
        },
        { $sort: { count: -1 } },
        { $project: { language: '$_id', count: 1, _id: 0 } }
      ]),

      // Country counts from series (via episodes)
      Episode.aggregate([
        {
          $lookup: {
            from: 'series',
            localField: 'imdbId',
            foreignField: 'imdbId',
            as: 'seriesInfo'
          }
        },
        {
          $addFields: {
            seriesCountry: { $arrayElemAt: ['$seriesInfo.country', 0] }
          }
        },
        {
          $match: {
            seriesCountry: { $exists: true, $ne: null, $ne: '' }
          }
        },
        {
          $group: {
            _id: '$seriesCountry',
            count: { $sum: 1 }
          }
        },
        { $sort: { count: -1 } },
        { $project: { country: '$_id', count: 1, _id: 0 } }
      ]),

      // Year counts from series (via episodes)
      Episode.aggregate([
        {
          $lookup: {
            from: 'series',
            localField: 'imdbId',
            foreignField: 'imdbId',
            as: 'seriesInfo'
          }
        },
        {
          $addFields: {
            seriesYear: { $arrayElemAt: ['$seriesInfo.startYear', 0] }
          }
        },
        {
          $match: {
            seriesYear: { $exists: true, $ne: null }
          }
        },
        {
          $group: {
            _id: '$seriesYear',
            count: { $sum: 1 }
          }
        },
        { $sort: { _id: -1 } }, // Sort years descending
        { $project: { year: '$_id', count: 1, _id: 0 } }
      ]),

      // Get unique qualities from episodes
      Episode.distinct('quality', { quality: { $exists: true, $ne: null } })
    ]);

    console.log(`✅ Episode filter options: ${genreCounts.length} genres, ${languageCounts.length} languages, ${countryCounts.length} countries, ${yearCounts.length} years, ${qualities.length} qualities`);

    return {
      genres: genreCounts,
      languages: languageCounts,
      countries: countryCounts,
      years: yearCounts.map(item => item.year),
      ratings: [], // Episodes use series ratings, not separate episode ratings
      qualities: qualities.filter(Boolean).sort()
    };
  }

  /**
   * Helper function to populate episode genres from its series
   * This should be called whenever a new episode is created
   */
  async populateEpisodeGenres(imdbId: string): Promise<string[]> {
    await connectDB();

    try {
      const series = await Series.findOne({ imdbId }, { genres: 1 });
      return series?.genres || [];
    } catch (error) {
      console.error(`Error fetching genres for series ${imdbId}:`, error);
      return [];
    }
  }

  /**
   * Utility function to update existing episodes without genres
   * This can be run manually if needed to fix episodes that were created before genre population
   */
  async updateEpisodesWithoutGenres(): Promise<{ updated: number; errors: number }> {
    await connectDB();

    let updated = 0;
    let errors = 0;

    try {
      // Find episodes without genres
      const episodesWithoutGenres = await Episode.find(
        { $or: [{ genres: { $exists: false } }, { genres: { $size: 0 } }] },
        { imdbId: 1, seriesTitle: 1, season: 1, episode: 1 }
      ).limit(1000); // Process in batches

      console.log(`🔄 Found ${episodesWithoutGenres.length} episodes without genres`);

      // Group by imdbId to minimize series lookups
      const episodesByImdb = episodesWithoutGenres.reduce((acc, ep) => {
        if (!acc[ep.imdbId]) acc[ep.imdbId] = [];
        acc[ep.imdbId].push(ep);
        return acc;
      }, {} as Record<string, any[]>);

      // Update episodes by series
      for (const [imdbId, episodes] of Object.entries(episodesByImdb)) {
        try {
          const genres = await this.populateEpisodeGenres(imdbId);

          if (genres.length > 0) {
            const episodeIds = episodes.map(ep => ep._id);
            const result = await Episode.updateMany(
              { _id: { $in: episodeIds } },
              { $set: { genres: genres } }
            );

            updated += result.modifiedCount;
            console.log(`✅ Updated ${result.modifiedCount} episodes for series ${imdbId} with genres: ${genres.join(', ')}`);
          }
        } catch (error) {
          console.error(`❌ Error updating episodes for series ${imdbId}:`, error);
          errors++;
        }
      }

      console.log(`🎉 Genre update completed: ${updated} episodes updated, ${errors} errors`);

    } catch (error) {
      console.error('❌ Error in updateEpisodesWithoutGenres:', error);
      errors++;
    }

    return { updated, errors };
  }

}

export default ContentService;
