import { Suspense } from 'react';
import { Metadata } from 'next';
import EnhancedHeroSection from '@/components/EnhancedHeroSection';
import TrendingSection from '@/components/TrendingSection';
import GenreSpotlight from '@/components/GenreSpotlight';
import DecadeCollection from '@/components/DecadeCollection';
import DirectorSpotlight from '@/components/DirectorSpotlight';
import GlobalCinema from '@/components/GlobalCinema';
import ContentSection from '@/components/ContentSection';
import BingeWorthy from '@/components/BingeWorthy';
import HiddenGems from '@/components/HiddenGems';
import QuickPicks from '@/components/QuickPicks';
import CastSpotlight from '@/components/CastSpotlight';
import { apiClient } from '@/lib/api';
import InitializeButton from '@/components/ClientInitializeButton';
// import { SchemaGenerator } from '@/lib/schema';

async function getEnhancedHomePageData() {
  try {
    const currentYear = new Date().getFullYear();

    // Fetch diverse content for different sections
    const [
      // Hero content - top rated recent content
      heroMoviesResponse,
      heroSeriesResponse,

      // Trending content - high popularity with positive delta
      trendingMoviesResponse,
      trendingSeriesResponse,

      // Recently added content
      recentMoviesResponse,
      recentSeriesResponse,
      recentEpisodesResponse,

      // Top rated content by different criteria
      topRatedMoviesResponse,
      topRatedSeriesResponse,

      // Genre-specific content (we'll fetch popular genres)
      actionMoviesResponse,
      dramaSeriesResponse,
      comedyMoviesResponse,

      // Decade collections
      modernMoviesResponse, // 2020s
      classicMoviesResponse, // 2000s-2010s

      // Global content
      internationalMoviesResponse,
      internationalSeriesResponse,

      // Binge-worthy series (ongoing with high ratings)
      ongoingSeriesResponse,

      // Quick picks (shorter content)
      shortMoviesResponse
    ] = await Promise.all([
      // Hero content
      apiClient.getMovies({ limit: 8, sortBy: 'imdbRating', sortOrder: 'desc', year: currentYear - 1 }),
      apiClient.getSeries({ limit: 8, sortBy: 'imdbRating', sortOrder: 'desc' }),

      // Trending (we'll simulate trending by using popularity)
      apiClient.getMovies({ limit: 12, sortBy: 'popularity', sortOrder: 'desc' }),
      apiClient.getSeries({ limit: 12, sortBy: 'popularity', sortOrder: 'desc' }),

      // Recently added
      apiClient.getMovies({ limit: 15, sortBy: 'createdAt', sortOrder: 'desc' }),
      apiClient.getSeries({ limit: 15, sortBy: 'createdAt', sortOrder: 'desc' }),
      apiClient.getEpisodes({ limit: 15, sortBy: 'createdAt', sortOrder: 'desc' }),

      // Top rated
      apiClient.getMovies({ limit: 20, sortBy: 'imdbRating', sortOrder: 'desc' }),
      apiClient.getSeries({ limit: 20, sortBy: 'imdbRating', sortOrder: 'desc' }),

      // Genre content
      apiClient.getMovies({ limit: 12, genre: 'Action', sortBy: 'imdbRating', sortOrder: 'desc' }),
      apiClient.getSeries({ limit: 12, genre: 'Drama', sortBy: 'imdbRating', sortOrder: 'desc' }),
      apiClient.getMovies({ limit: 12, genre: 'Comedy', sortBy: 'imdbRating', sortOrder: 'desc' }),

      // Decade collections
      apiClient.getMovies({ limit: 15, year: currentYear - 4, sortBy: 'imdbRating', sortOrder: 'desc' }), // Recent years
      apiClient.getMovies({ limit: 15, year: 2010, sortBy: 'imdbRating', sortOrder: 'desc' }), // 2010s

      // International content
      apiClient.getMovies({ limit: 12, language: 'English', sortBy: 'imdbRating', sortOrder: 'desc' }),
      apiClient.getSeries({ limit: 12, language: 'English', sortBy: 'imdbRating', sortOrder: 'desc' }),

      // Ongoing series
      apiClient.getSeries({ limit: 12, sortBy: 'imdbRating', sortOrder: 'desc' }),

      // Short content (we'll filter by runtime later)
      apiClient.getMovies({ limit: 20, sortBy: 'imdbRating', sortOrder: 'desc' })
    ]);

    return {
      hero: {
        movies: heroMoviesResponse.data,
        series: heroSeriesResponse.data
      },
      trending: {
        movies: trendingMoviesResponse.data,
        series: trendingSeriesResponse.data
      },
      recent: {
        movies: recentMoviesResponse.data,
        series: recentSeriesResponse.data,
        episodes: recentEpisodesResponse.data
      },
      topRated: {
        movies: topRatedMoviesResponse.data,
        series: topRatedSeriesResponse.data
      },
      genres: {
        action: actionMoviesResponse.data,
        drama: dramaSeriesResponse.data,
        comedy: comedyMoviesResponse.data
      },
      decades: {
        modern: modernMoviesResponse.data,
        classic: classicMoviesResponse.data
      },
      international: {
        movies: internationalMoviesResponse.data,
        series: internationalSeriesResponse.data
      },
      bingeWorthy: ongoingSeriesResponse.data,
      quickPicks: shortMoviesResponse.data.filter(movie =>
        movie.runtime && parseInt(movie.runtime.replace(/\D/g, '')) < 120
      )
    };
  } catch (error) {
    console.error('Error fetching enhanced homepage data:', error);
    return {
      hero: { movies: [], series: [] },
      trending: { movies: [], series: [] },
      recent: { movies: [], series: [], episodes: [] },
      topRated: { movies: [], series: [] },
      genres: { action: [], drama: [], comedy: [] },
      decades: { modern: [], classic: [] },
      international: { movies: [], series: [] },
      bingeWorthy: [],
      quickPicks: []
    };
  }
}

function transformMovieToHeroItem(movie: any) {
  return {
    id: movie._id,
    imdbId: movie.imdbId,
    title: movie.title,
    year: movie.year,
    posterUrl: movie.posterUrl,
    imdbRating: movie.imdbRating,
    description: movie.description,
    type: 'movie' as const
  };
}

function transformSeriesToHeroItem(series: any) {
  return {
    id: series._id,
    imdbId: series.imdbId,
    title: series.title,
    year: series.startYear,
    posterUrl: series.posterUrl,
    imdbRating: series.imdbRating,
    description: series.description,
    type: 'series' as const
  };
}

function transformMovieToContentItem(movie: any) {
  return {
    id: movie._id,
    imdbId: movie.imdbId,
    title: movie.title,
    year: movie.year,
    posterUrl: movie.posterUrl,
    imdbRating: movie.imdbRating,
    description: movie.description,
    type: 'movie' as const
  };
}

function transformSeriesToContentItem(series: any) {
  return {
    id: series._id,
    imdbId: series.imdbId,
    title: series.title,
    year: series.startYear,
    posterUrl: series.posterUrl,
    imdbRating: series.imdbRating,
    description: series.description,
    type: 'series' as const
  };
}

function transformEpisodeToContentItem(episode: any) {
  return {
    id: episode._id,
    imdbId: episode.imdbId,
    title: episode.episodeTitle || `Episode ${episode.episode}`,
    season: episode.season,
    episode: episode.episode,
    seriesTitle: episode.seriesTitle,
    posterUrl: episode.posterUrl,
    seriesPosterUrl: episode.seriesPosterUrl, // Use series poster for episodes
    imdbRating: episode.imdbRating,
    description: episode.description,
    type: 'episode' as const
  };
}

export default async function Home() {
  const data = await getEnhancedHomePageData();

  // Check if we have any content at all
  const hasContent = data.hero.movies.length > 0 || data.hero.series.length > 0 ||
                    data.recent.movies.length > 0 || data.recent.series.length > 0;

  // If no data, show initialization prompt
  if (!hasContent) {
    return (
      <div className="min-h-screen bg-black flex items-center justify-center">
        <div className="text-center">
          <h1 className="text-4xl font-bold text-white mb-4">Welcome to freeMoviesWatchNow</h1>
          <p className="text-gray-400 mb-8">Initialize the database to start streaming</p>
          <InitializeButton />
        </div>
      </div>
    );
  }

  // Create hero items from top-rated movies and series
  const heroItems = [
    ...data.hero.movies.slice(0, 4).map(transformMovieToHeroItem),
    ...data.hero.series.slice(0, 4).map(transformSeriesToHeroItem)
  ];

  return (
    <div className="min-h-screen bg-black overflow-hidden">
      {/* Structured Data for SEO - Temporarily commented out */}
      {/* <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{
          __html: JSON.stringify([
            SchemaGenerator.generateWebsiteSchema(),
            SchemaGenerator.generateCollectionPageSchema(
              'Latest Movies and TV Series',
              'Watch the latest movies and TV series online free in HD quality. Discover trending content updated daily.',
              '/',
              [
                ...data.hero.movies.slice(0, 5).map(movie => ({
                  name: `${movie.title} (${movie.year})`,
                  url: `/watch/movie/${movie.imdbId}`,
                  image: movie.posterUrl
                })),
                ...data.hero.series.slice(0, 5).map(show => ({
                  name: `${show.title} (${show.startYear})`,
                  url: `/watch/series/${show.imdbId}`,
                  image: show.posterUrl
                }))
              ]
            )
          ])
        }}
      /> */}

      {/* Enhanced Interactive Hero Section */}
      <EnhancedHeroSection items={heroItems} />

      {/* Dynamic Content Sections with Modern Design */}
      <div className="relative">
        {/* Advanced Background Effects */}
        <div className="absolute inset-0 overflow-hidden pointer-events-none">
          <div className="absolute top-0 left-0 w-full h-full bg-gradient-to-b from-black via-gray-900/50 to-black" />
          <div className="absolute top-1/4 left-1/4 w-96 h-96 bg-blue-600/5 rounded-full blur-3xl animate-pulse" />
          <div className="absolute top-1/2 right-1/4 w-80 h-80 bg-purple-600/5 rounded-full blur-3xl animate-pulse" style={{ animationDelay: '2s' }} />
          <div className="absolute bottom-1/4 left-1/3 w-72 h-72 bg-red-600/5 rounded-full blur-3xl animate-pulse" style={{ animationDelay: '4s' }} />
        </div>

        <div className="relative space-y-24 py-20 pb-32">
          {/* Trending Now Section */}
          <TrendingSection
            movies={data.trending.movies}
            series={data.trending.series}
            className="animate-fade-in"
          />

          {/* Genre Spotlights */}
          <GenreSpotlight
            actionMovies={data.genres.action}
            dramaSeries={data.genres.drama}
            comedyMovies={data.genres.comedy}
            className="animate-fade-in"
            style={{ animationDelay: '0.2s' }}
          />

          {/* Decade Collections */}
          <DecadeCollection
            modernContent={data.decades.modern}
            classicContent={data.decades.classic}
            className="animate-fade-in"
            style={{ animationDelay: '0.4s' }}
          />

          {/* Binge-Worthy Series */}
          <BingeWorthy
            series={data.bingeWorthy}
            className="animate-fade-in"
            style={{ animationDelay: '0.6s' }}
          />

          {/* Global Cinema */}
          <GlobalCinema
            movies={data.international.movies}
            series={data.international.series}
            className="animate-fade-in"
            style={{ animationDelay: '0.8s' }}
          />

          {/* Hidden Gems */}
          <HiddenGems
            movies={data.topRated.movies.filter(m => m.imdbRating && m.imdbRating >= 8.0)}
            series={data.topRated.series.filter(s => s.imdbRating && s.imdbRating >= 8.0)}
            className="animate-fade-in"
            style={{ animationDelay: '1.0s' }}
          />

          {/* Quick Picks */}
          <QuickPicks
            movies={data.quickPicks}
            className="animate-fade-in"
            style={{ animationDelay: '1.2s' }}
          />

          {/* Recently Added */}
          <ContentSection
            title="🆕 Recently Added"
            items={[
              ...data.recent.movies.slice(0, 8).map(transformMovieToContentItem),
              ...data.recent.series.slice(0, 8).map(transformSeriesToContentItem)
            ]}
            viewAllHref="/movies"
            className="animate-fade-in"
            style={{ animationDelay: '1.4s' }}
          />

          {/* Latest Episodes */}
          <ContentSection
            title="📺 Latest Episodes"
            items={data.recent.episodes.map(transformEpisodeToContentItem)}
            viewAllHref="/episodes"
            className="animate-fade-in"
            style={{ animationDelay: '1.6s' }}
          />
        </div>
      </div>
    </div>
  );
}
