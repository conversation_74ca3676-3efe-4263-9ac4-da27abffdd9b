import { NextRequest, NextResponse } from 'next/server';
import connectDB from '@/lib/mongodb';
import Series from '@/models/Series';
import Episode from '@/models/Episode';
import VidSrcAPI from '@/lib/vidsrc';
import IMDbScraper from '@/lib/scraper';

const vidsrc = VidSrcAPI.getInstance();
const imdbScraper = IMDbScraper.getInstance();

export async function POST(request: NextRequest) {
  try {
    await connectDB();
    
    const { search, genre } = await request.json();
    
    console.log('🎬 Fetching latest episodes from VidSrc API (pages 1-15)...');
    
    // Fetch episodes from VidSrc pages 1-15
    const allEpisodes = [];
    const maxPages = 15;
    
    for (let page = 1; page <= maxPages; page++) {
      try {
        console.log(`📄 Fetching VidSrc episodes page ${page}...`);
        const pageEpisodes = await vidsrc.getLatestEpisodes(page);
        
        if (pageEpisodes && pageEpisodes.length > 0) {
          allEpisodes.push(...pageEpisodes);
          console.log(`✅ Page ${page}: Found ${pageEpisodes.length} episodes`);
        } else {
          console.log(`⚠️ Page ${page}: No episodes found`);
        }
      } catch (error) {
        console.error(`❌ Error fetching page ${page}:`, error);
        // Continue with other pages even if one fails
      }
    }
    
    console.log(`📊 Total episodes fetched from VidSrc: ${allEpisodes.length}`);
    
    if (allEpisodes.length === 0) {
      return NextResponse.json({
        episodes: [],
        message: 'No episodes found from VidSrc API'
      });
    }
    
    // Process episodes and create series if they don't exist
    const processedEpisodes = [];
    const processedSeries = new Set();
    
    for (const vidsrcEpisode of allEpisodes) {
      try {
        const imdbId = vidsrcEpisode.imdb_id;
        
        // Skip if we've already processed this series
        if (processedSeries.has(imdbId)) {
          continue;
        }
        processedSeries.add(imdbId);
        
        // Check if series exists in database
        let series = await Series.findOne({ imdbId });
        
        if (!series) {
          console.log(`🆕 Creating new series for IMDb ID: ${imdbId}`);
          
          // Scrape series data from IMDb
          const seriesData = await imdbScraper.scrapeMovie(imdbId);
          
          if (seriesData) {
            // Create new series
            series = new Series({
              imdbId,
              title: seriesData.title || vidsrcEpisode.show_title,
              description: seriesData.description,
              posterUrl: seriesData.posterUrl,
              imdbRating: seriesData.imdbRating,
              startYear: seriesData.year,
              endYear: seriesData.endYear,
              genres: seriesData.genres || [],
              cast: seriesData.cast || [],
              director: seriesData.director,
              language: seriesData.language,
              country: seriesData.country,
              runtime: seriesData.runtime,
              type: 'series'
            });
            
            await series.save();
            console.log(`✅ Created series: ${series.title}`);
          } else {
            // Create minimal series if IMDb scraping fails
            series = new Series({
              imdbId,
              title: vidsrcEpisode.show_title,
              type: 'series',
              genres: []
            });
            
            await series.save();
            console.log(`✅ Created minimal series: ${series.title}`);
          }
        }
        
        // Find the latest episode for this series
        const latestEpisode = await Episode.findOne({ imdbId })
          .sort({ season: -1, episode: -1, createdAt: -1 })
          .lean();
        
        if (latestEpisode) {
          // Create episode object for display
          const episodeData = {
            _id: latestEpisode._id,
            imdbId: series.imdbId,
            seriesTitle: series.title,
            season: latestEpisode.season,
            episode: latestEpisode.episode,
            episodeTitle: latestEpisode.episodeTitle || `Episode ${latestEpisode.episode}`,
            description: latestEpisode.description,
            posterUrl: latestEpisode.posterUrl || series.posterUrl,
            seriesPosterUrl: series.posterUrl,
            runtime: latestEpisode.runtime,
            imdbRating: latestEpisode.imdbRating,
            airDate: latestEpisode.airDate,
            embedUrl: latestEpisode.embedUrl,
            genres: series.genres,
            createdAt: latestEpisode.createdAt,
            updatedAt: latestEpisode.updatedAt
          };
          
          // Apply filters
          let includeEpisode = true;
          
          if (search) {
            const searchLower = search.toLowerCase();
            includeEpisode = 
              series.title.toLowerCase().includes(searchLower) ||
              (episodeData.episodeTitle && episodeData.episodeTitle.toLowerCase().includes(searchLower)) ||
              (episodeData.description && episodeData.description.toLowerCase().includes(searchLower));
          }
          
          if (genre && includeEpisode) {
            includeEpisode = series.genres && series.genres.includes(genre);
          }
          
          if (includeEpisode) {
            processedEpisodes.push(episodeData);
          }
        }
        
      } catch (error) {
        console.error(`❌ Error processing episode for ${vidsrcEpisode.imdb_id}:`, error);
        // Continue with other episodes
      }
    }
    
    console.log(`📊 Processed episodes: ${processedEpisodes.length}`);
    
    // Sort by creation date (newest first)
    processedEpisodes.sort((a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime());
    
    return NextResponse.json({
      episodes: processedEpisodes,
      total: processedEpisodes.length,
      message: `Found ${processedEpisodes.length} latest episodes from ${processedSeries.size} series`
    });
    
  } catch (error) {
    console.error('❌ Error in VidSrc latest episodes API:', error);
    return NextResponse.json(
      { 
        error: 'Failed to fetch latest episodes from VidSrc',
        episodes: []
      },
      { status: 500 }
    );
  }
}
