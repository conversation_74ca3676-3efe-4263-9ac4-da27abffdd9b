{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/New%20folder%20%2825%29/streamzen/src/components/TrendingSection.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useRef, useState, useEffect } from 'react';\nimport Link from 'next/link';\nimport Image from 'next/image';\nimport { ChevronLeft, ChevronRight, TrendingUp, Star, Play, Flame } from 'lucide-react';\nimport { motion, useInView } from 'framer-motion';\nimport { cn, getImageUrl, formatRating } from '@/lib/utils';\n\ninterface ContentItem {\n  _id: string;\n  imdbId: string;\n  title: string;\n  year?: number;\n  startYear?: number;\n  posterUrl?: string;\n  imdbRating?: number;\n  description?: string;\n  popularity?: number;\n}\n\ninterface TrendingSectionProps {\n  movies: ContentItem[];\n  series: ContentItem[];\n  className?: string;\n  style?: React.CSSProperties;\n}\n\nconst TrendingSection: React.FC<TrendingSectionProps> = ({\n  movies,\n  series,\n  className,\n  style\n}) => {\n  const scrollContainerRef = useRef<HTMLDivElement>(null);\n  const sectionRef = useRef<HTMLDivElement>(null);\n  const isInView = useInView(sectionRef, { once: true, margin: \"-100px\" });\n  const [canScrollLeft, setCanScrollLeft] = useState(false);\n  const [canScrollRight, setCanScrollRight] = useState(true);\n  const [activeTab, setActiveTab] = useState<'all' | 'movies' | 'series'>('all');\n\n  // Combine and sort content by popularity\n  const allContent = [\n    ...movies.map(item => ({ ...item, type: 'movie' as const })),\n    ...series.map(item => ({ ...item, type: 'series' as const }))\n  ].sort((a, b) => (b.popularity || 0) - (a.popularity || 0));\n\n  const getFilteredContent = () => {\n    switch (activeTab) {\n      case 'movies':\n        return movies.map(item => ({ ...item, type: 'movie' as const }));\n      case 'series':\n        return series.map(item => ({ ...item, type: 'series' as const }));\n      default:\n        return allContent;\n    }\n  };\n\n  const filteredContent = getFilteredContent();\n\n  const checkScrollButtons = () => {\n    if (!scrollContainerRef.current) return;\n\n    const container = scrollContainerRef.current;\n    setCanScrollLeft(container.scrollLeft > 0);\n    setCanScrollRight(\n      container.scrollLeft < container.scrollWidth - container.clientWidth - 1\n    );\n  };\n\n  useEffect(() => {\n    checkScrollButtons();\n    const container = scrollContainerRef.current;\n    if (container) {\n      container.addEventListener('scroll', checkScrollButtons);\n      return () => container.removeEventListener('scroll', checkScrollButtons);\n    }\n  }, [filteredContent]);\n\n  const scroll = (direction: 'left' | 'right') => {\n    if (!scrollContainerRef.current) return;\n\n    const container = scrollContainerRef.current;\n    const scrollAmount = container.clientWidth * 0.8;\n    const targetScroll = direction === 'left' \n      ? container.scrollLeft - scrollAmount\n      : container.scrollLeft + scrollAmount;\n\n    container.scrollTo({\n      left: targetScroll,\n      behavior: 'smooth'\n    });\n  };\n\n  if (filteredContent.length === 0) {\n    return null;\n  }\n\n  return (\n    <motion.section\n      ref={sectionRef}\n      initial={{ opacity: 0, y: 50 }}\n      animate={isInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 50 }}\n      transition={{ duration: 0.8 }}\n      className={cn(\"relative\", className)}\n      style={style}\n    >\n      <div className=\"max-w-[2560px] mx-auto px-6 lg:px-12\">\n        {/* Section Header */}\n        <motion.div\n          initial={{ opacity: 0, y: 30 }}\n          animate={isInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 30 }}\n          transition={{ duration: 0.6, delay: 0.2 }}\n          className=\"flex items-center justify-between mb-8\"\n        >\n          <div className=\"flex items-center space-x-4\">\n            <div className=\"flex items-center space-x-3\">\n              <div className=\"p-3 bg-gradient-to-br from-orange-500 to-red-600 rounded-xl\">\n                <Flame className=\"w-6 h-6 text-white\" />\n              </div>\n              <div>\n                <h2 className=\"text-3xl lg:text-4xl font-black text-white\">\n                  Trending Now\n                </h2>\n                <p className=\"text-gray-400 text-sm\">What everyone's watching</p>\n              </div>\n            </div>\n            <div className=\"hidden sm:flex items-center space-x-1 px-3 py-1 bg-gradient-to-r from-orange-500/20 to-red-600/20 border border-orange-500/30 rounded-full\">\n              <TrendingUp className=\"w-4 h-4 text-orange-400\" />\n              <span className=\"text-orange-400 text-sm font-semibold\">Hot</span>\n            </div>\n          </div>\n\n          {/* Tab Filters */}\n          <div className=\"flex items-center space-x-2 bg-gray-900/50 rounded-xl p-1 backdrop-blur-sm border border-gray-800\">\n            {[\n              { key: 'all', label: 'All', count: allContent.length },\n              { key: 'movies', label: 'Movies', count: movies.length },\n              { key: 'series', label: 'Series', count: series.length }\n            ].map(({ key, label, count }) => (\n              <button\n                key={key}\n                onClick={() => setActiveTab(key as any)}\n                className={cn(\n                  \"px-4 py-2 rounded-lg text-sm font-semibold transition-all duration-300\",\n                  activeTab === key\n                    ? \"bg-gradient-to-r from-orange-500 to-red-600 text-white shadow-lg\"\n                    : \"text-gray-400 hover:text-white hover:bg-gray-800/50\"\n                )}\n              >\n                {label} ({count})\n              </button>\n            ))}\n          </div>\n        </motion.div>\n\n        {/* Content Carousel */}\n        <div className=\"relative group\">\n          {/* Navigation Buttons */}\n          {canScrollLeft && (\n            <motion.button\n              initial={{ opacity: 0 }}\n              animate={{ opacity: 1 }}\n              onClick={() => scroll('left')}\n              className=\"absolute left-0 top-1/2 -translate-y-1/2 z-10 p-3 bg-black/80 hover:bg-black text-white rounded-full backdrop-blur-sm transition-all duration-300 hover:scale-110 shadow-xl opacity-0 group-hover:opacity-100\"\n            >\n              <ChevronLeft className=\"w-6 h-6\" />\n            </motion.button>\n          )}\n\n          {canScrollRight && (\n            <motion.button\n              initial={{ opacity: 0 }}\n              animate={{ opacity: 1 }}\n              onClick={() => scroll('right')}\n              className=\"absolute right-0 top-1/2 -translate-y-1/2 z-10 p-3 bg-black/80 hover:bg-black text-white rounded-full backdrop-blur-sm transition-all duration-300 hover:scale-110 shadow-xl opacity-0 group-hover:opacity-100\"\n            >\n              <ChevronRight className=\"w-6 h-6\" />\n            </motion.button>\n          )}\n\n          {/* Scrollable Content */}\n          <div\n            ref={scrollContainerRef}\n            className=\"flex space-x-6 overflow-x-auto scrollbar-hide pb-4\"\n            style={{ scrollbarWidth: 'none', msOverflowStyle: 'none' }}\n          >\n            {filteredContent.map((item, index) => (\n              <motion.div\n                key={`${item.type}-${item._id}`}\n                initial={{ opacity: 0, y: 30 }}\n                animate={isInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 30 }}\n                transition={{ duration: 0.6, delay: 0.1 * index }}\n                className=\"flex-shrink-0 group\"\n              >\n                <Link href={`/watch/${item.type}/${item.imdbId}`}>\n                  <div className=\"relative w-64 h-96 rounded-2xl overflow-hidden bg-gray-900 group-hover:scale-105 transition-all duration-500 shadow-xl hover:shadow-2xl\">\n                    {/* Trending Badge */}\n                    <div className=\"absolute top-3 left-3 z-10 flex items-center space-x-1 px-2 py-1 bg-gradient-to-r from-orange-500 to-red-600 rounded-full text-xs font-bold text-white\">\n                      <Flame className=\"w-3 h-3\" />\n                      <span>#{index + 1}</span>\n                    </div>\n\n                    {/* Type Badge */}\n                    <div className=\"absolute top-3 right-3 z-10 px-2 py-1 bg-black/70 rounded-full text-xs font-semibold text-white backdrop-blur-sm\">\n                      {item.type === 'movie' ? '🎬' : '📺'}\n                    </div>\n\n                    {/* Poster Image */}\n                    <Image\n                      src={getImageUrl(item.posterUrl)}\n                      alt={item.title}\n                      fill\n                      className=\"object-cover transition-transform duration-500 group-hover:scale-110\"\n                    />\n\n                    {/* Gradient Overlay */}\n                    <div className=\"absolute inset-0 bg-gradient-to-t from-black via-transparent to-transparent opacity-60 group-hover:opacity-80 transition-opacity duration-300\" />\n\n                    {/* Play Button Overlay */}\n                    <div className=\"absolute inset-0 flex items-center justify-center opacity-0 group-hover:opacity-100 transition-all duration-300\">\n                      <div className=\"p-4 bg-white/20 rounded-full backdrop-blur-sm border border-white/30 group-hover:scale-110 transition-transform duration-300\">\n                        <Play className=\"w-8 h-8 text-white fill-current\" />\n                      </div>\n                    </div>\n\n                    {/* Content Info */}\n                    <div className=\"absolute bottom-0 left-0 right-0 p-4 space-y-2\">\n                      <h3 className=\"text-white font-bold text-lg leading-tight line-clamp-2\">\n                        {item.title}\n                      </h3>\n                      \n                      <div className=\"flex items-center justify-between\">\n                        <span className=\"text-gray-300 text-sm\">\n                          {item.year || item.startYear}\n                        </span>\n                        \n                        {item.imdbRating && (\n                          <div className=\"flex items-center space-x-1 px-2 py-1 bg-yellow-500/20 rounded-full\">\n                            <Star className=\"w-3 h-3 text-yellow-400 fill-current\" />\n                            <span className=\"text-yellow-400 text-xs font-bold\">\n                              {formatRating(item.imdbRating)}\n                            </span>\n                          </div>\n                        )}\n                      </div>\n\n                      {/* Popularity Indicator */}\n                      {item.popularity && (\n                        <div className=\"flex items-center space-x-1\">\n                          <TrendingUp className=\"w-3 h-3 text-green-400\" />\n                          <span className=\"text-green-400 text-xs font-semibold\">\n                            {Math.round(item.popularity)} views\n                          </span>\n                        </div>\n                      )}\n                    </div>\n                  </div>\n                </Link>\n              </motion.div>\n            ))}\n          </div>\n        </div>\n\n        {/* View All Button */}\n        <motion.div\n          initial={{ opacity: 0, y: 20 }}\n          animate={isInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 20 }}\n          transition={{ duration: 0.6, delay: 0.4 }}\n          className=\"flex justify-center mt-8\"\n        >\n          <Link\n            href=\"/movies\"\n            className=\"group px-8 py-3 bg-gradient-to-r from-orange-500/20 to-red-600/20 hover:from-orange-500/30 hover:to-red-600/30 border border-orange-500/30 hover:border-orange-500/50 text-orange-400 hover:text-orange-300 font-semibold rounded-xl transition-all duration-300 backdrop-blur-sm\"\n          >\n            <div className=\"flex items-center space-x-2\">\n              <span>Explore All Trending</span>\n              <ChevronRight className=\"w-4 h-4 group-hover:translate-x-1 transition-transform duration-300\" />\n            </div>\n          </Link>\n        </motion.div>\n      </div>\n    </motion.section>\n  );\n};\n\nexport default TrendingSection;\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AAAA;AACA;;;AAPA;;;;;;;AA4BA,MAAM,kBAAkD,CAAC,EACvD,MAAM,EACN,MAAM,EACN,SAAS,EACT,KAAK,EACN;;IACC,MAAM,qBAAqB,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAkB;IAClD,MAAM,aAAa,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAkB;IAC1C,MAAM,WAAW,CAAA,GAAA,gLAAA,CAAA,YAAS,AAAD,EAAE,YAAY;QAAE,MAAM;QAAM,QAAQ;IAAS;IACtE,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACnD,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACrD,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAA+B;IAExE,yCAAyC;IACzC,MAAM,aAAa;WACd,OAAO,GAAG,CAAC,CAAA,OAAQ,CAAC;gBAAE,GAAG,IAAI;gBAAE,MAAM;YAAiB,CAAC;WACvD,OAAO,GAAG,CAAC,CAAA,OAAQ,CAAC;gBAAE,GAAG,IAAI;gBAAE,MAAM;YAAkB,CAAC;KAC5D,CAAC,IAAI,CAAC,CAAC,GAAG,IAAM,CAAC,EAAE,UAAU,IAAI,CAAC,IAAI,CAAC,EAAE,UAAU,IAAI,CAAC;IAEzD,MAAM,qBAAqB;QACzB,OAAQ;YACN,KAAK;gBACH,OAAO,OAAO,GAAG,CAAC,CAAA,OAAQ,CAAC;wBAAE,GAAG,IAAI;wBAAE,MAAM;oBAAiB,CAAC;YAChE,KAAK;gBACH,OAAO,OAAO,GAAG,CAAC,CAAA,OAAQ,CAAC;wBAAE,GAAG,IAAI;wBAAE,MAAM;oBAAkB,CAAC;YACjE;gBACE,OAAO;QACX;IACF;IAEA,MAAM,kBAAkB;IAExB,MAAM,qBAAqB;QACzB,IAAI,CAAC,mBAAmB,OAAO,EAAE;QAEjC,MAAM,YAAY,mBAAmB,OAAO;QAC5C,iBAAiB,UAAU,UAAU,GAAG;QACxC,kBACE,UAAU,UAAU,GAAG,UAAU,WAAW,GAAG,UAAU,WAAW,GAAG;IAE3E;IAEA,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;qCAAE;YACR;YACA,MAAM,YAAY,mBAAmB,OAAO;YAC5C,IAAI,WAAW;gBACb,UAAU,gBAAgB,CAAC,UAAU;gBACrC;iDAAO,IAAM,UAAU,mBAAmB,CAAC,UAAU;;YACvD;QACF;oCAAG;QAAC;KAAgB;IAEpB,MAAM,SAAS,CAAC;QACd,IAAI,CAAC,mBAAmB,OAAO,EAAE;QAEjC,MAAM,YAAY,mBAAmB,OAAO;QAC5C,MAAM,eAAe,UAAU,WAAW,GAAG;QAC7C,MAAM,eAAe,cAAc,SAC/B,UAAU,UAAU,GAAG,eACvB,UAAU,UAAU,GAAG;QAE3B,UAAU,QAAQ,CAAC;YACjB,MAAM;YACN,UAAU;QACZ;IACF;IAEA,IAAI,gBAAgB,MAAM,KAAK,GAAG;QAChC,OAAO;IACT;IAEA,qBACE,6LAAC,6LAAA,CAAA,SAAM,CAAC,OAAO;QACb,KAAK;QACL,SAAS;YAAE,SAAS;YAAG,GAAG;QAAG;QAC7B,SAAS,WAAW;YAAE,SAAS;YAAG,GAAG;QAAE,IAAI;YAAE,SAAS;YAAG,GAAG;QAAG;QAC/D,YAAY;YAAE,UAAU;QAAI;QAC5B,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,YAAY;QAC1B,OAAO;kBAEP,cAAA,6LAAC;YAAI,WAAU;;8BAEb,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC7B,SAAS,WAAW;wBAAE,SAAS;wBAAG,GAAG;oBAAE,IAAI;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC/D,YAAY;wBAAE,UAAU;wBAAK,OAAO;oBAAI;oBACxC,WAAU;;sCAEV,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC,uMAAA,CAAA,QAAK;gDAAC,WAAU;;;;;;;;;;;sDAEnB,6LAAC;;8DACC,6LAAC;oDAAG,WAAU;8DAA6C;;;;;;8DAG3D,6LAAC;oDAAE,WAAU;8DAAwB;;;;;;;;;;;;;;;;;;8CAGzC,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,qNAAA,CAAA,aAAU;4CAAC,WAAU;;;;;;sDACtB,6LAAC;4CAAK,WAAU;sDAAwC;;;;;;;;;;;;;;;;;;sCAK5D,6LAAC;4BAAI,WAAU;sCACZ;gCACC;oCAAE,KAAK;oCAAO,OAAO;oCAAO,OAAO,WAAW,MAAM;gCAAC;gCACrD;oCAAE,KAAK;oCAAU,OAAO;oCAAU,OAAO,OAAO,MAAM;gCAAC;gCACvD;oCAAE,KAAK;oCAAU,OAAO;oCAAU,OAAO,OAAO,MAAM;gCAAC;6BACxD,CAAC,GAAG,CAAC,CAAC,EAAE,GAAG,EAAE,KAAK,EAAE,KAAK,EAAE,iBAC1B,6LAAC;oCAEC,SAAS,IAAM,aAAa;oCAC5B,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,0EACA,cAAc,MACV,qEACA;;wCAGL;wCAAM;wCAAG;wCAAM;;mCATX;;;;;;;;;;;;;;;;8BAgBb,6LAAC;oBAAI,WAAU;;wBAEZ,+BACC,6LAAC,6LAAA,CAAA,SAAM,CAAC,MAAM;4BACZ,SAAS;gCAAE,SAAS;4BAAE;4BACtB,SAAS;gCAAE,SAAS;4BAAE;4BACtB,SAAS,IAAM,OAAO;4BACtB,WAAU;sCAEV,cAAA,6LAAC,uNAAA,CAAA,cAAW;gCAAC,WAAU;;;;;;;;;;;wBAI1B,gCACC,6LAAC,6LAAA,CAAA,SAAM,CAAC,MAAM;4BACZ,SAAS;gCAAE,SAAS;4BAAE;4BACtB,SAAS;gCAAE,SAAS;4BAAE;4BACtB,SAAS,IAAM,OAAO;4BACtB,WAAU;sCAEV,cAAA,6LAAC,yNAAA,CAAA,eAAY;gCAAC,WAAU;;;;;;;;;;;sCAK5B,6LAAC;4BACC,KAAK;4BACL,WAAU;4BACV,OAAO;gCAAE,gBAAgB;gCAAQ,iBAAiB;4BAAO;sCAExD,gBAAgB,GAAG,CAAC,CAAC,MAAM,sBAC1B,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oCAET,SAAS;wCAAE,SAAS;wCAAG,GAAG;oCAAG;oCAC7B,SAAS,WAAW;wCAAE,SAAS;wCAAG,GAAG;oCAAE,IAAI;wCAAE,SAAS;wCAAG,GAAG;oCAAG;oCAC/D,YAAY;wCAAE,UAAU;wCAAK,OAAO,MAAM;oCAAM;oCAChD,WAAU;8CAEV,cAAA,6LAAC,+JAAA,CAAA,UAAI;wCAAC,MAAM,CAAC,OAAO,EAAE,KAAK,IAAI,CAAC,CAAC,EAAE,KAAK,MAAM,EAAE;kDAC9C,cAAA,6LAAC;4CAAI,WAAU;;8DAEb,6LAAC;oDAAI,WAAU;;sEACb,6LAAC,uMAAA,CAAA,QAAK;4DAAC,WAAU;;;;;;sEACjB,6LAAC;;gEAAK;gEAAE,QAAQ;;;;;;;;;;;;;8DAIlB,6LAAC;oDAAI,WAAU;8DACZ,KAAK,IAAI,KAAK,UAAU,OAAO;;;;;;8DAIlC,6LAAC,gIAAA,CAAA,UAAK;oDACJ,KAAK,CAAA,GAAA,sHAAA,CAAA,cAAW,AAAD,EAAE,KAAK,SAAS;oDAC/B,KAAK,KAAK,KAAK;oDACf,IAAI;oDACJ,WAAU;;;;;;8DAIZ,6LAAC;oDAAI,WAAU;;;;;;8DAGf,6LAAC;oDAAI,WAAU;8DACb,cAAA,6LAAC;wDAAI,WAAU;kEACb,cAAA,6LAAC,qMAAA,CAAA,OAAI;4DAAC,WAAU;;;;;;;;;;;;;;;;8DAKpB,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAG,WAAU;sEACX,KAAK,KAAK;;;;;;sEAGb,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAK,WAAU;8EACb,KAAK,IAAI,IAAI,KAAK,SAAS;;;;;;gEAG7B,KAAK,UAAU,kBACd,6LAAC;oEAAI,WAAU;;sFACb,6LAAC,qMAAA,CAAA,OAAI;4EAAC,WAAU;;;;;;sFAChB,6LAAC;4EAAK,WAAU;sFACb,CAAA,GAAA,sHAAA,CAAA,eAAY,AAAD,EAAE,KAAK,UAAU;;;;;;;;;;;;;;;;;;wDAOpC,KAAK,UAAU,kBACd,6LAAC;4DAAI,WAAU;;8EACb,6LAAC,qNAAA,CAAA,aAAU;oEAAC,WAAU;;;;;;8EACtB,6LAAC;oEAAK,WAAU;;wEACb,KAAK,KAAK,CAAC,KAAK,UAAU;wEAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;mCA/DpC,GAAG,KAAK,IAAI,CAAC,CAAC,EAAE,KAAK,GAAG,EAAE;;;;;;;;;;;;;;;;8BA4EvC,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC7B,SAAS,WAAW;wBAAE,SAAS;wBAAG,GAAG;oBAAE,IAAI;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC/D,YAAY;wBAAE,UAAU;wBAAK,OAAO;oBAAI;oBACxC,WAAU;8BAEV,cAAA,6LAAC,+JAAA,CAAA,UAAI;wBACH,MAAK;wBACL,WAAU;kCAEV,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;8CAAK;;;;;;8CACN,6LAAC,yNAAA,CAAA,eAAY;oCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOtC;GAhQM;;QAQa,gLAAA,CAAA,YAAS;;;KARtB;uCAkQS", "debugId": null}}, {"offset": {"line": 607, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/New%20folder%20%2825%29/streamzen/src/components/GenreSpotlight.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useRef, useState, useEffect } from 'react';\nimport Link from 'next/link';\nimport Image from 'next/image';\nimport { ChevronLeft, ChevronRight, Star, Play, Zap, Heart, Smile } from 'lucide-react';\nimport { motion, useInView } from 'framer-motion';\nimport { cn, getImageUrl, formatRating } from '@/lib/utils';\n\ninterface ContentItem {\n  _id: string;\n  imdbId: string;\n  title: string;\n  year?: number;\n  startYear?: number;\n  posterUrl?: string;\n  imdbRating?: number;\n  description?: string;\n}\n\ninterface GenreSpotlightProps {\n  actionMovies: ContentItem[];\n  dramaSeries: ContentItem[];\n  comedyMovies: ContentItem[];\n  className?: string;\n  style?: React.CSSProperties;\n}\n\nconst GenreSpotlight: React.FC<GenreSpotlightProps> = ({\n  actionMovies,\n  dramaSeries,\n  comedyMovies,\n  className,\n  style\n}) => {\n  const sectionRef = useRef<HTMLDivElement>(null);\n  const isInView = useInView(sectionRef, { once: true, margin: \"-100px\" });\n\n  const genres = [\n    {\n      title: 'Action & Adventure',\n      subtitle: 'Heart-pounding thrills',\n      icon: Zap,\n      color: 'from-red-500 to-orange-600',\n      bgColor: 'from-red-500/20 to-orange-600/20',\n      borderColor: 'border-red-500/30',\n      textColor: 'text-red-400',\n      content: actionMovies.map(item => ({ ...item, type: 'movie' as const })),\n      href: '/movies?genre=Action'\n    },\n    {\n      title: 'Drama Series',\n      subtitle: 'Compelling stories',\n      icon: Heart,\n      color: 'from-purple-500 to-pink-600',\n      bgColor: 'from-purple-500/20 to-pink-600/20',\n      borderColor: 'border-purple-500/30',\n      textColor: 'text-purple-400',\n      content: dramaSeries.map(item => ({ ...item, type: 'series' as const })),\n      href: '/series?genre=Drama'\n    },\n    {\n      title: 'Comedy Gold',\n      subtitle: 'Laugh out loud',\n      icon: Smile,\n      color: 'from-yellow-500 to-green-600',\n      bgColor: 'from-yellow-500/20 to-green-600/20',\n      borderColor: 'border-yellow-500/30',\n      textColor: 'text-yellow-400',\n      content: comedyMovies.map(item => ({ ...item, type: 'movie' as const })),\n      href: '/movies?genre=Comedy'\n    }\n  ];\n\n  const GenreCarousel = ({ genre, index }: { genre: typeof genres[0], index: number }) => {\n    const scrollRef = useRef<HTMLDivElement>(null);\n    const [canScrollLeft, setCanScrollLeft] = useState(false);\n    const [canScrollRight, setCanScrollRight] = useState(true);\n\n    const checkScrollButtons = () => {\n      if (!scrollRef.current) return;\n      const container = scrollRef.current;\n      setCanScrollLeft(container.scrollLeft > 0);\n      setCanScrollRight(\n        container.scrollLeft < container.scrollWidth - container.clientWidth - 1\n      );\n    };\n\n    useEffect(() => {\n      checkScrollButtons();\n      const container = scrollRef.current;\n      if (container) {\n        container.addEventListener('scroll', checkScrollButtons);\n        return () => container.removeEventListener('scroll', checkScrollButtons);\n      }\n    }, [genre.content]);\n\n    const scroll = (direction: 'left' | 'right') => {\n      if (!scrollRef.current) return;\n      const container = scrollRef.current;\n      const scrollAmount = container.clientWidth * 0.7;\n      const targetScroll = direction === 'left' \n        ? container.scrollLeft - scrollAmount\n        : container.scrollLeft + scrollAmount;\n\n      container.scrollTo({\n        left: targetScroll,\n        behavior: 'smooth'\n      });\n    };\n\n    if (genre.content.length === 0) return null;\n\n    return (\n      <motion.div\n        initial={{ opacity: 0, y: 50 }}\n        animate={isInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 50 }}\n        transition={{ duration: 0.8, delay: 0.2 * index }}\n        className=\"space-y-6\"\n      >\n        {/* Genre Header */}\n        <div className=\"flex items-center justify-between\">\n          <div className=\"flex items-center space-x-4\">\n            <div className={cn(\"p-3 bg-gradient-to-br rounded-xl\", genre.color)}>\n              <genre.icon className=\"w-6 h-6 text-white\" />\n            </div>\n            <div>\n              <h3 className=\"text-2xl lg:text-3xl font-black text-white\">\n                {genre.title}\n              </h3>\n              <p className=\"text-gray-400 text-sm\">{genre.subtitle}</p>\n            </div>\n          </div>\n\n          <Link\n            href={genre.href}\n            className={cn(\n              \"group px-4 py-2 bg-gradient-to-r border rounded-xl transition-all duration-300 backdrop-blur-sm hover:scale-105\",\n              genre.bgColor,\n              genre.borderColor,\n              genre.textColor\n            )}\n          >\n            <div className=\"flex items-center space-x-2\">\n              <span className=\"font-semibold\">View All</span>\n              <ChevronRight className=\"w-4 h-4 group-hover:translate-x-1 transition-transform duration-300\" />\n            </div>\n          </Link>\n        </div>\n\n        {/* Content Carousel */}\n        <div className=\"relative group\">\n          {/* Navigation Buttons */}\n          {canScrollLeft && (\n            <button\n              onClick={() => scroll('left')}\n              className=\"absolute left-0 top-1/2 -translate-y-1/2 z-10 p-2 bg-black/80 hover:bg-black text-white rounded-full backdrop-blur-sm transition-all duration-300 hover:scale-110 shadow-xl opacity-0 group-hover:opacity-100\"\n            >\n              <ChevronLeft className=\"w-5 h-5\" />\n            </button>\n          )}\n\n          {canScrollRight && (\n            <button\n              onClick={() => scroll('right')}\n              className=\"absolute right-0 top-1/2 -translate-y-1/2 z-10 p-2 bg-black/80 hover:bg-black text-white rounded-full backdrop-blur-sm transition-all duration-300 hover:scale-110 shadow-xl opacity-0 group-hover:opacity-100\"\n            >\n              <ChevronRight className=\"w-5 h-5\" />\n            </button>\n          )}\n\n          {/* Scrollable Content */}\n          <div\n            ref={scrollRef}\n            className=\"flex space-x-4 overflow-x-auto scrollbar-hide pb-2\"\n            style={{ scrollbarWidth: 'none', msOverflowStyle: 'none' }}\n          >\n            {genre.content.slice(0, 12).map((item, itemIndex) => (\n              <motion.div\n                key={`${genre.title}-${item._id}`}\n                initial={{ opacity: 0, scale: 0.9 }}\n                animate={isInView ? { opacity: 1, scale: 1 } : { opacity: 0, scale: 0.9 }}\n                transition={{ duration: 0.5, delay: 0.1 * itemIndex }}\n                className=\"flex-shrink-0 group\"\n              >\n                <Link href={`/watch/${item.type}/${item.imdbId}`}>\n                  <div className=\"relative w-48 h-72 rounded-xl overflow-hidden bg-gray-900 group-hover:scale-105 transition-all duration-500 shadow-lg hover:shadow-2xl\">\n                    {/* Type Badge */}\n                    <div className=\"absolute top-2 right-2 z-10 px-2 py-1 bg-black/70 rounded-full text-xs font-semibold text-white backdrop-blur-sm\">\n                      {item.type === 'movie' ? '🎬' : '📺'}\n                    </div>\n\n                    {/* Poster Image */}\n                    <Image\n                      src={getImageUrl(item.posterUrl)}\n                      alt={item.title}\n                      fill\n                      className=\"object-cover transition-transform duration-500 group-hover:scale-110\"\n                    />\n\n                    {/* Gradient Overlay */}\n                    <div className=\"absolute inset-0 bg-gradient-to-t from-black via-transparent to-transparent opacity-60 group-hover:opacity-80 transition-opacity duration-300\" />\n\n                    {/* Play Button Overlay */}\n                    <div className=\"absolute inset-0 flex items-center justify-center opacity-0 group-hover:opacity-100 transition-all duration-300\">\n                      <div className=\"p-3 bg-white/20 rounded-full backdrop-blur-sm border border-white/30 group-hover:scale-110 transition-transform duration-300\">\n                        <Play className=\"w-6 h-6 text-white fill-current\" />\n                      </div>\n                    </div>\n\n                    {/* Content Info */}\n                    <div className=\"absolute bottom-0 left-0 right-0 p-3 space-y-1\">\n                      <h4 className=\"text-white font-bold text-sm leading-tight line-clamp-2\">\n                        {item.title}\n                      </h4>\n                      \n                      <div className=\"flex items-center justify-between\">\n                        <span className=\"text-gray-300 text-xs\">\n                          {item.year || item.startYear}\n                        </span>\n                        \n                        {item.imdbRating && (\n                          <div className=\"flex items-center space-x-1 px-1.5 py-0.5 bg-yellow-500/20 rounded-full\">\n                            <Star className=\"w-2.5 h-2.5 text-yellow-400 fill-current\" />\n                            <span className=\"text-yellow-400 text-xs font-bold\">\n                              {formatRating(item.imdbRating)}\n                            </span>\n                          </div>\n                        )}\n                      </div>\n                    </div>\n\n                    {/* Genre-specific accent */}\n                    <div className={cn(\n                      \"absolute top-0 left-0 w-full h-1 bg-gradient-to-r\",\n                      genre.color\n                    )} />\n                  </div>\n                </Link>\n              </motion.div>\n            ))}\n          </div>\n        </div>\n      </motion.div>\n    );\n  };\n\n  return (\n    <motion.section\n      ref={sectionRef}\n      initial={{ opacity: 0, y: 50 }}\n      animate={isInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 50 }}\n      transition={{ duration: 0.8 }}\n      className={cn(\"relative\", className)}\n      style={style}\n    >\n      <div className=\"max-w-[2560px] mx-auto px-6 lg:px-12\">\n        {/* Section Header */}\n        <motion.div\n          initial={{ opacity: 0, y: 30 }}\n          animate={isInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 30 }}\n          transition={{ duration: 0.6, delay: 0.2 }}\n          className=\"text-center mb-12\"\n        >\n          <h2 className=\"text-4xl lg:text-5xl font-black text-white mb-4\">\n            Genre Spotlights\n          </h2>\n          <p className=\"text-gray-400 text-lg max-w-2xl mx-auto\">\n            Discover your next favorite from our curated genre collections\n          </p>\n        </motion.div>\n\n        {/* Genre Carousels */}\n        <div className=\"space-y-16\">\n          {genres.map((genre, index) => (\n            <GenreCarousel key={genre.title} genre={genre} index={index} />\n          ))}\n        </div>\n\n        {/* Explore All Genres */}\n        <motion.div\n          initial={{ opacity: 0, y: 20 }}\n          animate={isInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 20 }}\n          transition={{ duration: 0.6, delay: 0.8 }}\n          className=\"flex justify-center mt-16\"\n        >\n          <Link\n            href=\"/movies\"\n            className=\"group px-8 py-4 bg-gradient-to-r from-gray-800/50 to-gray-700/50 hover:from-gray-700/50 hover:to-gray-600/50 border border-gray-700 hover:border-gray-600 text-white font-semibold rounded-xl transition-all duration-300 backdrop-blur-sm hover:scale-105\"\n          >\n            <div className=\"flex items-center space-x-3\">\n              <span className=\"text-lg\">Explore All Genres</span>\n              <ChevronRight className=\"w-5 h-5 group-hover:translate-x-1 transition-transform duration-300\" />\n            </div>\n          </Link>\n        </motion.div>\n      </div>\n    </motion.section>\n  );\n};\n\nexport default GenreSpotlight;\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AAAA;AACA;;;AAPA;;;;;;;AA4BA,MAAM,iBAAgD,CAAC,EACrD,YAAY,EACZ,WAAW,EACX,YAAY,EACZ,SAAS,EACT,KAAK,EACN;;;IACC,MAAM,aAAa,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAkB;IAC1C,MAAM,WAAW,CAAA,GAAA,gLAAA,CAAA,YAAS,AAAD,EAAE,YAAY;QAAE,MAAM;QAAM,QAAQ;IAAS;IAEtE,MAAM,SAAS;QACb;YACE,OAAO;YACP,UAAU;YACV,MAAM,mMAAA,CAAA,MAAG;YACT,OAAO;YACP,SAAS;YACT,aAAa;YACb,WAAW;YACX,SAAS,aAAa,GAAG,CAAC,CAAA,OAAQ,CAAC;oBAAE,GAAG,IAAI;oBAAE,MAAM;gBAAiB,CAAC;YACtE,MAAM;QACR;QACA;YACE,OAAO;YACP,UAAU;YACV,MAAM,uMAAA,CAAA,QAAK;YACX,OAAO;YACP,SAAS;YACT,aAAa;YACb,WAAW;YACX,SAAS,YAAY,GAAG,CAAC,CAAA,OAAQ,CAAC;oBAAE,GAAG,IAAI;oBAAE,MAAM;gBAAkB,CAAC;YACtE,MAAM;QACR;QACA;YACE,OAAO;YACP,UAAU;YACV,MAAM,uMAAA,CAAA,QAAK;YACX,OAAO;YACP,SAAS;YACT,aAAa;YACb,WAAW;YACX,SAAS,aAAa,GAAG,CAAC,CAAA,OAAQ,CAAC;oBAAE,GAAG,IAAI;oBAAE,MAAM;gBAAiB,CAAC;YACtE,MAAM;QACR;KACD;IAED,MAAM,gBAAgB,CAAC,EAAE,KAAK,EAAE,KAAK,EAA8C;;QACjF,MAAM,YAAY,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAkB;QACzC,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;QACnD,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;QAErD,MAAM,qBAAqB;YACzB,IAAI,CAAC,UAAU,OAAO,EAAE;YACxB,MAAM,YAAY,UAAU,OAAO;YACnC,iBAAiB,UAAU,UAAU,GAAG;YACxC,kBACE,UAAU,UAAU,GAAG,UAAU,WAAW,GAAG,UAAU,WAAW,GAAG;QAE3E;QAEA,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;sDAAE;gBACR;gBACA,MAAM,YAAY,UAAU,OAAO;gBACnC,IAAI,WAAW;oBACb,UAAU,gBAAgB,CAAC,UAAU;oBACrC;kEAAO,IAAM,UAAU,mBAAmB,CAAC,UAAU;;gBACvD;YACF;qDAAG;YAAC,MAAM,OAAO;SAAC;QAElB,MAAM,SAAS,CAAC;YACd,IAAI,CAAC,UAAU,OAAO,EAAE;YACxB,MAAM,YAAY,UAAU,OAAO;YACnC,MAAM,eAAe,UAAU,WAAW,GAAG;YAC7C,MAAM,eAAe,cAAc,SAC/B,UAAU,UAAU,GAAG,eACvB,UAAU,UAAU,GAAG;YAE3B,UAAU,QAAQ,CAAC;gBACjB,MAAM;gBACN,UAAU;YACZ;QACF;QAEA,IAAI,MAAM,OAAO,CAAC,MAAM,KAAK,GAAG,OAAO;QAEvC,qBACE,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;YACT,SAAS;gBAAE,SAAS;gBAAG,GAAG;YAAG;YAC7B,SAAS,WAAW;gBAAE,SAAS;gBAAG,GAAG;YAAE,IAAI;gBAAE,SAAS;gBAAG,GAAG;YAAG;YAC/D,YAAY;gBAAE,UAAU;gBAAK,OAAO,MAAM;YAAM;YAChD,WAAU;;8BAGV,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,oCAAoC,MAAM,KAAK;8CAChE,cAAA,6LAAC,MAAM,IAAI;wCAAC,WAAU;;;;;;;;;;;8CAExB,6LAAC;;sDACC,6LAAC;4CAAG,WAAU;sDACX,MAAM,KAAK;;;;;;sDAEd,6LAAC;4CAAE,WAAU;sDAAyB,MAAM,QAAQ;;;;;;;;;;;;;;;;;;sCAIxD,6LAAC,+JAAA,CAAA,UAAI;4BACH,MAAM,MAAM,IAAI;4BAChB,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,mHACA,MAAM,OAAO,EACb,MAAM,WAAW,EACjB,MAAM,SAAS;sCAGjB,cAAA,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAK,WAAU;kDAAgB;;;;;;kDAChC,6LAAC,yNAAA,CAAA,eAAY;wCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;8BAM9B,6LAAC;oBAAI,WAAU;;wBAEZ,+BACC,6LAAC;4BACC,SAAS,IAAM,OAAO;4BACtB,WAAU;sCAEV,cAAA,6LAAC,uNAAA,CAAA,cAAW;gCAAC,WAAU;;;;;;;;;;;wBAI1B,gCACC,6LAAC;4BACC,SAAS,IAAM,OAAO;4BACtB,WAAU;sCAEV,cAAA,6LAAC,yNAAA,CAAA,eAAY;gCAAC,WAAU;;;;;;;;;;;sCAK5B,6LAAC;4BACC,KAAK;4BACL,WAAU;4BACV,OAAO;gCAAE,gBAAgB;gCAAQ,iBAAiB;4BAAO;sCAExD,MAAM,OAAO,CAAC,KAAK,CAAC,GAAG,IAAI,GAAG,CAAC,CAAC,MAAM,0BACrC,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oCAET,SAAS;wCAAE,SAAS;wCAAG,OAAO;oCAAI;oCAClC,SAAS,WAAW;wCAAE,SAAS;wCAAG,OAAO;oCAAE,IAAI;wCAAE,SAAS;wCAAG,OAAO;oCAAI;oCACxE,YAAY;wCAAE,UAAU;wCAAK,OAAO,MAAM;oCAAU;oCACpD,WAAU;8CAEV,cAAA,6LAAC,+JAAA,CAAA,UAAI;wCAAC,MAAM,CAAC,OAAO,EAAE,KAAK,IAAI,CAAC,CAAC,EAAE,KAAK,MAAM,EAAE;kDAC9C,cAAA,6LAAC;4CAAI,WAAU;;8DAEb,6LAAC;oDAAI,WAAU;8DACZ,KAAK,IAAI,KAAK,UAAU,OAAO;;;;;;8DAIlC,6LAAC,gIAAA,CAAA,UAAK;oDACJ,KAAK,CAAA,GAAA,sHAAA,CAAA,cAAW,AAAD,EAAE,KAAK,SAAS;oDAC/B,KAAK,KAAK,KAAK;oDACf,IAAI;oDACJ,WAAU;;;;;;8DAIZ,6LAAC;oDAAI,WAAU;;;;;;8DAGf,6LAAC;oDAAI,WAAU;8DACb,cAAA,6LAAC;wDAAI,WAAU;kEACb,cAAA,6LAAC,qMAAA,CAAA,OAAI;4DAAC,WAAU;;;;;;;;;;;;;;;;8DAKpB,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAG,WAAU;sEACX,KAAK,KAAK;;;;;;sEAGb,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAK,WAAU;8EACb,KAAK,IAAI,IAAI,KAAK,SAAS;;;;;;gEAG7B,KAAK,UAAU,kBACd,6LAAC;oEAAI,WAAU;;sFACb,6LAAC,qMAAA,CAAA,OAAI;4EAAC,WAAU;;;;;;sFAChB,6LAAC;4EAAK,WAAU;sFACb,CAAA,GAAA,sHAAA,CAAA,eAAY,AAAD,EAAE,KAAK,UAAU;;;;;;;;;;;;;;;;;;;;;;;;8DAQvC,6LAAC;oDAAI,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACf,qDACA,MAAM,KAAK;;;;;;;;;;;;;;;;;mCAxDZ,GAAG,MAAM,KAAK,CAAC,CAAC,EAAE,KAAK,GAAG,EAAE;;;;;;;;;;;;;;;;;;;;;;IAkE/C;QA3KM;IA6KN,qBACE,6LAAC,6LAAA,CAAA,SAAM,CAAC,OAAO;QACb,KAAK;QACL,SAAS;YAAE,SAAS;YAAG,GAAG;QAAG;QAC7B,SAAS,WAAW;YAAE,SAAS;YAAG,GAAG;QAAE,IAAI;YAAE,SAAS;YAAG,GAAG;QAAG;QAC/D,YAAY;YAAE,UAAU;QAAI;QAC5B,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,YAAY;QAC1B,OAAO;kBAEP,cAAA,6LAAC;YAAI,WAAU;;8BAEb,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC7B,SAAS,WAAW;wBAAE,SAAS;wBAAG,GAAG;oBAAE,IAAI;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC/D,YAAY;wBAAE,UAAU;wBAAK,OAAO;oBAAI;oBACxC,WAAU;;sCAEV,6LAAC;4BAAG,WAAU;sCAAkD;;;;;;sCAGhE,6LAAC;4BAAE,WAAU;sCAA0C;;;;;;;;;;;;8BAMzD,6LAAC;oBAAI,WAAU;8BACZ,OAAO,GAAG,CAAC,CAAC,OAAO,sBAClB,6LAAC;4BAAgC,OAAO;4BAAO,OAAO;2BAAlC,MAAM,KAAK;;;;;;;;;;8BAKnC,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC7B,SAAS,WAAW;wBAAE,SAAS;wBAAG,GAAG;oBAAE,IAAI;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC/D,YAAY;wBAAE,UAAU;wBAAK,OAAO;oBAAI;oBACxC,WAAU;8BAEV,cAAA,6LAAC,+JAAA,CAAA,UAAI;wBACH,MAAK;wBACL,WAAU;kCAEV,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAK,WAAU;8CAAU;;;;;;8CAC1B,6LAAC,yNAAA,CAAA,eAAY;oCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOtC;GA/QM;;QAQa,gLAAA,CAAA,YAAS;;;KARtB;uCAiRS", "debugId": null}}, {"offset": {"line": 1195, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/New%20folder%20%2825%29/streamzen/src/components/DecadeCollection.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useRef, useState, useEffect } from 'react';\nimport Link from 'next/link';\nimport Image from 'next/image';\nimport { ChevronLeft, ChevronRight, Star, Play, Calendar, Clock } from 'lucide-react';\nimport { motion, useInView } from 'framer-motion';\nimport { cn, getImageUrl, formatRating } from '@/lib/utils';\n\ninterface ContentItem {\n  _id: string;\n  imdbId: string;\n  title: string;\n  year?: number;\n  startYear?: number;\n  posterUrl?: string;\n  imdbRating?: number;\n  description?: string;\n}\n\ninterface DecadeCollectionProps {\n  modernContent: ContentItem[];\n  classicContent: ContentItem[];\n  className?: string;\n  style?: React.CSSProperties;\n}\n\nconst DecadeCollection: React.FC<DecadeCollectionProps> = ({\n  modernContent,\n  classicContent,\n  className,\n  style\n}) => {\n  const sectionRef = useRef<HTMLDivElement>(null);\n  const isInView = useInView(sectionRef, { once: true, margin: \"-100px\" });\n\n  const decades = [\n    {\n      title: 'Modern Masterpieces',\n      subtitle: '2020s - Latest & Greatest',\n      period: '2020s',\n      icon: Clock,\n      color: 'from-blue-500 to-cyan-600',\n      bgColor: 'from-blue-500/20 to-cyan-600/20',\n      borderColor: 'border-blue-500/30',\n      textColor: 'text-blue-400',\n      content: modernContent.map(item => ({ ...item, type: 'movie' as const })),\n      href: '/movies?year=2020'\n    },\n    {\n      title: 'Timeless Classics',\n      subtitle: '2000s-2010s - Golden Era',\n      period: '2000s-2010s',\n      icon: Calendar,\n      color: 'from-amber-500 to-orange-600',\n      bgColor: 'from-amber-500/20 to-orange-600/20',\n      borderColor: 'border-amber-500/30',\n      textColor: 'text-amber-400',\n      content: classicContent.map(item => ({ ...item, type: 'movie' as const })),\n      href: '/movies?year=2010'\n    }\n  ];\n\n  const DecadeCarousel = ({ decade, index }: { decade: typeof decades[0], index: number }) => {\n    const scrollRef = useRef<HTMLDivElement>(null);\n    const [canScrollLeft, setCanScrollLeft] = useState(false);\n    const [canScrollRight, setCanScrollRight] = useState(true);\n\n    const checkScrollButtons = () => {\n      if (!scrollRef.current) return;\n      const container = scrollRef.current;\n      setCanScrollLeft(container.scrollLeft > 0);\n      setCanScrollRight(\n        container.scrollLeft < container.scrollWidth - container.clientWidth - 1\n      );\n    };\n\n    useEffect(() => {\n      checkScrollButtons();\n      const container = scrollRef.current;\n      if (container) {\n        container.addEventListener('scroll', checkScrollButtons);\n        return () => container.removeEventListener('scroll', checkScrollButtons);\n      }\n    }, [decade.content]);\n\n    const scroll = (direction: 'left' | 'right') => {\n      if (!scrollRef.current) return;\n      const container = scrollRef.current;\n      const scrollAmount = container.clientWidth * 0.7;\n      const targetScroll = direction === 'left' \n        ? container.scrollLeft - scrollAmount\n        : container.scrollLeft + scrollAmount;\n\n      container.scrollTo({\n        left: targetScroll,\n        behavior: 'smooth'\n      });\n    };\n\n    if (decade.content.length === 0) return null;\n\n    return (\n      <motion.div\n        initial={{ opacity: 0, y: 50 }}\n        animate={isInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 50 }}\n        transition={{ duration: 0.8, delay: 0.2 * index }}\n        className=\"space-y-6\"\n      >\n        {/* Decade Header */}\n        <div className=\"flex items-center justify-between\">\n          <div className=\"flex items-center space-x-4\">\n            <div className={cn(\"p-3 bg-gradient-to-br rounded-xl\", decade.color)}>\n              <decade.icon className=\"w-6 h-6 text-white\" />\n            </div>\n            <div>\n              <h3 className=\"text-2xl lg:text-3xl font-black text-white\">\n                {decade.title}\n              </h3>\n              <p className=\"text-gray-400 text-sm\">{decade.subtitle}</p>\n            </div>\n          </div>\n\n          <div className=\"flex items-center space-x-3\">\n            <div className={cn(\n              \"px-3 py-1 bg-gradient-to-r border rounded-full text-sm font-semibold\",\n              decade.bgColor,\n              decade.borderColor,\n              decade.textColor\n            )}>\n              {decade.period}\n            </div>\n            <Link\n              href={decade.href}\n              className={cn(\n                \"group px-4 py-2 bg-gradient-to-r border rounded-xl transition-all duration-300 backdrop-blur-sm hover:scale-105\",\n                decade.bgColor,\n                decade.borderColor,\n                decade.textColor\n              )}\n            >\n              <div className=\"flex items-center space-x-2\">\n                <span className=\"font-semibold\">View All</span>\n                <ChevronRight className=\"w-4 h-4 group-hover:translate-x-1 transition-transform duration-300\" />\n              </div>\n            </Link>\n          </div>\n        </div>\n\n        {/* Content Carousel */}\n        <div className=\"relative group\">\n          {/* Navigation Buttons */}\n          {canScrollLeft && (\n            <button\n              onClick={() => scroll('left')}\n              className=\"absolute left-0 top-1/2 -translate-y-1/2 z-10 p-2 bg-black/80 hover:bg-black text-white rounded-full backdrop-blur-sm transition-all duration-300 hover:scale-110 shadow-xl opacity-0 group-hover:opacity-100\"\n            >\n              <ChevronLeft className=\"w-5 h-5\" />\n            </button>\n          )}\n\n          {canScrollRight && (\n            <button\n              onClick={() => scroll('right')}\n              className=\"absolute right-0 top-1/2 -translate-y-1/2 z-10 p-2 bg-black/80 hover:bg-black text-white rounded-full backdrop-blur-sm transition-all duration-300 hover:scale-110 shadow-xl opacity-0 group-hover:opacity-100\"\n            >\n              <ChevronRight className=\"w-5 h-5\" />\n            </button>\n          )}\n\n          {/* Scrollable Content */}\n          <div\n            ref={scrollRef}\n            className=\"flex space-x-4 overflow-x-auto scrollbar-hide pb-2\"\n            style={{ scrollbarWidth: 'none', msOverflowStyle: 'none' }}\n          >\n            {decade.content.slice(0, 15).map((item, itemIndex) => (\n              <motion.div\n                key={`${decade.title}-${item._id}`}\n                initial={{ opacity: 0, scale: 0.9 }}\n                animate={isInView ? { opacity: 1, scale: 1 } : { opacity: 0, scale: 0.9 }}\n                transition={{ duration: 0.5, delay: 0.1 * itemIndex }}\n                className=\"flex-shrink-0 group\"\n              >\n                <Link href={`/watch/${item.type}/${item.imdbId}`}>\n                  <div className=\"relative w-52 h-78 rounded-xl overflow-hidden bg-gray-900 group-hover:scale-105 transition-all duration-500 shadow-lg hover:shadow-2xl\">\n                    {/* Year Badge */}\n                    <div className=\"absolute top-2 left-2 z-10 px-2 py-1 bg-black/70 rounded-full text-xs font-semibold text-white backdrop-blur-sm\">\n                      {item.year || item.startYear}\n                    </div>\n\n                    {/* Type Badge */}\n                    <div className=\"absolute top-2 right-2 z-10 px-2 py-1 bg-black/70 rounded-full text-xs font-semibold text-white backdrop-blur-sm\">\n                      🎬\n                    </div>\n\n                    {/* Poster Image */}\n                    <Image\n                      src={getImageUrl(item.posterUrl)}\n                      alt={item.title}\n                      fill\n                      className=\"object-cover transition-transform duration-500 group-hover:scale-110\"\n                    />\n\n                    {/* Gradient Overlay */}\n                    <div className=\"absolute inset-0 bg-gradient-to-t from-black via-transparent to-transparent opacity-60 group-hover:opacity-80 transition-opacity duration-300\" />\n\n                    {/* Play Button Overlay */}\n                    <div className=\"absolute inset-0 flex items-center justify-center opacity-0 group-hover:opacity-100 transition-all duration-300\">\n                      <div className=\"p-3 bg-white/20 rounded-full backdrop-blur-sm border border-white/30 group-hover:scale-110 transition-transform duration-300\">\n                        <Play className=\"w-6 h-6 text-white fill-current\" />\n                      </div>\n                    </div>\n\n                    {/* Content Info */}\n                    <div className=\"absolute bottom-0 left-0 right-0 p-3 space-y-2\">\n                      <h4 className=\"text-white font-bold text-sm leading-tight line-clamp-2\">\n                        {item.title}\n                      </h4>\n                      \n                      <div className=\"flex items-center justify-between\">\n                        <span className={cn(\"text-xs font-semibold\", decade.textColor)}>\n                          {decade.period}\n                        </span>\n                        \n                        {item.imdbRating && (\n                          <div className=\"flex items-center space-x-1 px-1.5 py-0.5 bg-yellow-500/20 rounded-full\">\n                            <Star className=\"w-2.5 h-2.5 text-yellow-400 fill-current\" />\n                            <span className=\"text-yellow-400 text-xs font-bold\">\n                              {formatRating(item.imdbRating)}\n                            </span>\n                          </div>\n                        )}\n                      </div>\n                    </div>\n\n                    {/* Decade-specific accent */}\n                    <div className={cn(\n                      \"absolute bottom-0 left-0 w-full h-1 bg-gradient-to-r\",\n                      decade.color\n                    )} />\n                  </div>\n                </Link>\n              </motion.div>\n            ))}\n          </div>\n        </div>\n      </motion.div>\n    );\n  };\n\n  return (\n    <motion.section\n      ref={sectionRef}\n      initial={{ opacity: 0, y: 50 }}\n      animate={isInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 50 }}\n      transition={{ duration: 0.8 }}\n      className={cn(\"relative\", className)}\n      style={style}\n    >\n      <div className=\"max-w-[2560px] mx-auto px-6 lg:px-12\">\n        {/* Section Header */}\n        <motion.div\n          initial={{ opacity: 0, y: 30 }}\n          animate={isInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 30 }}\n          transition={{ duration: 0.6, delay: 0.2 }}\n          className=\"text-center mb-12\"\n        >\n          <h2 className=\"text-4xl lg:text-5xl font-black text-white mb-4\">\n            Through the Decades\n          </h2>\n          <p className=\"text-gray-400 text-lg max-w-2xl mx-auto\">\n            Journey through cinema history with our curated decade collections\n          </p>\n        </motion.div>\n\n        {/* Decade Carousels */}\n        <div className=\"space-y-16\">\n          {decades.map((decade, index) => (\n            <DecadeCarousel key={decade.title} decade={decade} index={index} />\n          ))}\n        </div>\n\n        {/* Timeline Visual */}\n        <motion.div\n          initial={{ opacity: 0, y: 20 }}\n          animate={isInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 20 }}\n          transition={{ duration: 0.6, delay: 0.8 }}\n          className=\"flex justify-center mt-16\"\n        >\n          <div className=\"flex items-center space-x-4 px-8 py-4 bg-gray-900/50 rounded-xl border border-gray-800 backdrop-blur-sm\">\n            <Calendar className=\"w-5 h-5 text-gray-400\" />\n            <span className=\"text-gray-300 font-semibold\">Explore Cinema Through Time</span>\n          </div>\n        </motion.div>\n      </div>\n    </motion.section>\n  );\n};\n\nexport default DecadeCollection;\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AAAA;AACA;;;AAPA;;;;;;;AA2BA,MAAM,mBAAoD,CAAC,EACzD,aAAa,EACb,cAAc,EACd,SAAS,EACT,KAAK,EACN;;;IACC,MAAM,aAAa,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAkB;IAC1C,MAAM,WAAW,CAAA,GAAA,gLAAA,CAAA,YAAS,AAAD,EAAE,YAAY;QAAE,MAAM;QAAM,QAAQ;IAAS;IAEtE,MAAM,UAAU;QACd;YACE,OAAO;YACP,UAAU;YACV,QAAQ;YACR,MAAM,uMAAA,CAAA,QAAK;YACX,OAAO;YACP,SAAS;YACT,aAAa;YACb,WAAW;YACX,SAAS,cAAc,GAAG,CAAC,CAAA,OAAQ,CAAC;oBAAE,GAAG,IAAI;oBAAE,MAAM;gBAAiB,CAAC;YACvE,MAAM;QACR;QACA;YACE,OAAO;YACP,UAAU;YACV,QAAQ;YACR,MAAM,6MAAA,CAAA,WAAQ;YACd,OAAO;YACP,SAAS;YACT,aAAa;YACb,WAAW;YACX,SAAS,eAAe,GAAG,CAAC,CAAA,OAAQ,CAAC;oBAAE,GAAG,IAAI;oBAAE,MAAM;gBAAiB,CAAC;YACxE,MAAM;QACR;KACD;IAED,MAAM,iBAAiB,CAAC,EAAE,MAAM,EAAE,KAAK,EAAgD;;QACrF,MAAM,YAAY,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAkB;QACzC,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;QACnD,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;QAErD,MAAM,qBAAqB;YACzB,IAAI,CAAC,UAAU,OAAO,EAAE;YACxB,MAAM,YAAY,UAAU,OAAO;YACnC,iBAAiB,UAAU,UAAU,GAAG;YACxC,kBACE,UAAU,UAAU,GAAG,UAAU,WAAW,GAAG,UAAU,WAAW,GAAG;QAE3E;QAEA,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;yDAAE;gBACR;gBACA,MAAM,YAAY,UAAU,OAAO;gBACnC,IAAI,WAAW;oBACb,UAAU,gBAAgB,CAAC,UAAU;oBACrC;qEAAO,IAAM,UAAU,mBAAmB,CAAC,UAAU;;gBACvD;YACF;wDAAG;YAAC,OAAO,OAAO;SAAC;QAEnB,MAAM,SAAS,CAAC;YACd,IAAI,CAAC,UAAU,OAAO,EAAE;YACxB,MAAM,YAAY,UAAU,OAAO;YACnC,MAAM,eAAe,UAAU,WAAW,GAAG;YAC7C,MAAM,eAAe,cAAc,SAC/B,UAAU,UAAU,GAAG,eACvB,UAAU,UAAU,GAAG;YAE3B,UAAU,QAAQ,CAAC;gBACjB,MAAM;gBACN,UAAU;YACZ;QACF;QAEA,IAAI,OAAO,OAAO,CAAC,MAAM,KAAK,GAAG,OAAO;QAExC,qBACE,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;YACT,SAAS;gBAAE,SAAS;gBAAG,GAAG;YAAG;YAC7B,SAAS,WAAW;gBAAE,SAAS;gBAAG,GAAG;YAAE,IAAI;gBAAE,SAAS;gBAAG,GAAG;YAAG;YAC/D,YAAY;gBAAE,UAAU;gBAAK,OAAO,MAAM;YAAM;YAChD,WAAU;;8BAGV,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,oCAAoC,OAAO,KAAK;8CACjE,cAAA,6LAAC,OAAO,IAAI;wCAAC,WAAU;;;;;;;;;;;8CAEzB,6LAAC;;sDACC,6LAAC;4CAAG,WAAU;sDACX,OAAO,KAAK;;;;;;sDAEf,6LAAC;4CAAE,WAAU;sDAAyB,OAAO,QAAQ;;;;;;;;;;;;;;;;;;sCAIzD,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACf,wEACA,OAAO,OAAO,EACd,OAAO,WAAW,EAClB,OAAO,SAAS;8CAEf,OAAO,MAAM;;;;;;8CAEhB,6LAAC,+JAAA,CAAA,UAAI;oCACH,MAAM,OAAO,IAAI;oCACjB,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,mHACA,OAAO,OAAO,EACd,OAAO,WAAW,EAClB,OAAO,SAAS;8CAGlB,cAAA,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAK,WAAU;0DAAgB;;;;;;0DAChC,6LAAC,yNAAA,CAAA,eAAY;gDAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8BAOhC,6LAAC;oBAAI,WAAU;;wBAEZ,+BACC,6LAAC;4BACC,SAAS,IAAM,OAAO;4BACtB,WAAU;sCAEV,cAAA,6LAAC,uNAAA,CAAA,cAAW;gCAAC,WAAU;;;;;;;;;;;wBAI1B,gCACC,6LAAC;4BACC,SAAS,IAAM,OAAO;4BACtB,WAAU;sCAEV,cAAA,6LAAC,yNAAA,CAAA,eAAY;gCAAC,WAAU;;;;;;;;;;;sCAK5B,6LAAC;4BACC,KAAK;4BACL,WAAU;4BACV,OAAO;gCAAE,gBAAgB;gCAAQ,iBAAiB;4BAAO;sCAExD,OAAO,OAAO,CAAC,KAAK,CAAC,GAAG,IAAI,GAAG,CAAC,CAAC,MAAM,0BACtC,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oCAET,SAAS;wCAAE,SAAS;wCAAG,OAAO;oCAAI;oCAClC,SAAS,WAAW;wCAAE,SAAS;wCAAG,OAAO;oCAAE,IAAI;wCAAE,SAAS;wCAAG,OAAO;oCAAI;oCACxE,YAAY;wCAAE,UAAU;wCAAK,OAAO,MAAM;oCAAU;oCACpD,WAAU;8CAEV,cAAA,6LAAC,+JAAA,CAAA,UAAI;wCAAC,MAAM,CAAC,OAAO,EAAE,KAAK,IAAI,CAAC,CAAC,EAAE,KAAK,MAAM,EAAE;kDAC9C,cAAA,6LAAC;4CAAI,WAAU;;8DAEb,6LAAC;oDAAI,WAAU;8DACZ,KAAK,IAAI,IAAI,KAAK,SAAS;;;;;;8DAI9B,6LAAC;oDAAI,WAAU;8DAAmH;;;;;;8DAKlI,6LAAC,gIAAA,CAAA,UAAK;oDACJ,KAAK,CAAA,GAAA,sHAAA,CAAA,cAAW,AAAD,EAAE,KAAK,SAAS;oDAC/B,KAAK,KAAK,KAAK;oDACf,IAAI;oDACJ,WAAU;;;;;;8DAIZ,6LAAC;oDAAI,WAAU;;;;;;8DAGf,6LAAC;oDAAI,WAAU;8DACb,cAAA,6LAAC;wDAAI,WAAU;kEACb,cAAA,6LAAC,qMAAA,CAAA,OAAI;4DAAC,WAAU;;;;;;;;;;;;;;;;8DAKpB,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAG,WAAU;sEACX,KAAK,KAAK;;;;;;sEAGb,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAK,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,yBAAyB,OAAO,SAAS;8EAC1D,OAAO,MAAM;;;;;;gEAGf,KAAK,UAAU,kBACd,6LAAC;oEAAI,WAAU;;sFACb,6LAAC,qMAAA,CAAA,OAAI;4EAAC,WAAU;;;;;;sFAChB,6LAAC;4EAAK,WAAU;sFACb,CAAA,GAAA,sHAAA,CAAA,eAAY,AAAD,EAAE,KAAK,UAAU;;;;;;;;;;;;;;;;;;;;;;;;8DAQvC,6LAAC;oDAAI,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACf,wDACA,OAAO,KAAK;;;;;;;;;;;;;;;;;mCA7Db,GAAG,OAAO,KAAK,CAAC,CAAC,EAAE,KAAK,GAAG,EAAE;;;;;;;;;;;;;;;;;;;;;;IAuEhD;QA1LM;IA4LN,qBACE,6LAAC,6LAAA,CAAA,SAAM,CAAC,OAAO;QACb,KAAK;QACL,SAAS;YAAE,SAAS;YAAG,GAAG;QAAG;QAC7B,SAAS,WAAW;YAAE,SAAS;YAAG,GAAG;QAAE,IAAI;YAAE,SAAS;YAAG,GAAG;QAAG;QAC/D,YAAY;YAAE,UAAU;QAAI;QAC5B,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,YAAY;QAC1B,OAAO;kBAEP,cAAA,6LAAC;YAAI,WAAU;;8BAEb,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC7B,SAAS,WAAW;wBAAE,SAAS;wBAAG,GAAG;oBAAE,IAAI;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC/D,YAAY;wBAAE,UAAU;wBAAK,OAAO;oBAAI;oBACxC,WAAU;;sCAEV,6LAAC;4BAAG,WAAU;sCAAkD;;;;;;sCAGhE,6LAAC;4BAAE,WAAU;sCAA0C;;;;;;;;;;;;8BAMzD,6LAAC;oBAAI,WAAU;8BACZ,QAAQ,GAAG,CAAC,CAAC,QAAQ,sBACpB,6LAAC;4BAAkC,QAAQ;4BAAQ,OAAO;2BAArC,OAAO,KAAK;;;;;;;;;;8BAKrC,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC7B,SAAS,WAAW;wBAAE,SAAS;wBAAG,GAAG;oBAAE,IAAI;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC/D,YAAY;wBAAE,UAAU;wBAAK,OAAO;oBAAI;oBACxC,WAAU;8BAEV,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,6MAAA,CAAA,WAAQ;gCAAC,WAAU;;;;;;0CACpB,6LAAC;gCAAK,WAAU;0CAA8B;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAM1D;GA/QM;;QAOa,gLAAA,CAAA,YAAS;;;KAPtB;uCAiRS", "debugId": null}}, {"offset": {"line": 1787, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/New%20folder%20%2825%29/streamzen/src/components/GlobalCinema.tsx"], "sourcesContent": ["'use client';\n\nimport React from 'react';\nimport Link from 'next/link';\nimport Image from 'next/image';\nimport { Globe, Star, Play } from 'lucide-react';\nimport { motion, useInView } from 'framer-motion';\nimport { cn, getImageUrl, formatRating } from '@/lib/utils';\n\ninterface ContentItem {\n  _id: string;\n  imdbId: string;\n  title: string;\n  year?: number;\n  startYear?: number;\n  posterUrl?: string;\n  imdbRating?: number;\n  language?: string;\n  country?: string;\n}\n\ninterface GlobalCinemaProps {\n  movies: ContentItem[];\n  series: ContentItem[];\n  className?: string;\n  style?: React.CSSProperties;\n}\n\nconst GlobalCinema: React.FC<GlobalCinemaProps> = ({\n  movies,\n  series,\n  className,\n  style\n}) => {\n  const sectionRef = React.useRef<HTMLDivElement>(null);\n  const isInView = useInView(sectionRef, { once: true, margin: \"-100px\" });\n\n  const allContent = [\n    ...movies.map(item => ({ ...item, type: 'movie' as const })),\n    ...series.map(item => ({ ...item, type: 'series' as const }))\n  ].slice(0, 8);\n\n  if (allContent.length === 0) return null;\n\n  return (\n    <motion.section\n      ref={sectionRef}\n      initial={{ opacity: 0, y: 50 }}\n      animate={isInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 50 }}\n      transition={{ duration: 0.8 }}\n      className={cn(\"relative\", className)}\n      style={style}\n    >\n      <div className=\"max-w-[2560px] mx-auto px-6 lg:px-12\">\n        <motion.div\n          initial={{ opacity: 0, y: 30 }}\n          animate={isInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 30 }}\n          transition={{ duration: 0.6, delay: 0.2 }}\n          className=\"text-center mb-12\"\n        >\n          <div className=\"flex items-center justify-center space-x-3 mb-4\">\n            <div className=\"p-3 bg-gradient-to-br from-green-500 to-teal-600 rounded-xl\">\n              <Globe className=\"w-6 h-6 text-white\" />\n            </div>\n            <h2 className=\"text-4xl lg:text-5xl font-black text-white\">\n              Global Cinema\n            </h2>\n          </div>\n          <p className=\"text-gray-400 text-lg max-w-2xl mx-auto\">\n            Discover stories from around the world\n          </p>\n        </motion.div>\n\n        <div className=\"grid grid-cols-2 md:grid-cols-4 gap-4\">\n          {allContent.map((item, index) => (\n            <motion.div\n              key={item._id}\n              initial={{ opacity: 0, scale: 0.9 }}\n              animate={isInView ? { opacity: 1, scale: 1 } : { opacity: 0, scale: 0.9 }}\n              transition={{ duration: 0.5, delay: 0.1 * index }}\n              className=\"group\"\n            >\n              <Link href={`/watch/${item.type}/${item.imdbId}`}>\n                <div className=\"relative w-full h-72 rounded-xl overflow-hidden bg-gray-900 group-hover:scale-105 transition-all duration-500 shadow-lg hover:shadow-2xl\">\n                  <Image\n                    src={getImageUrl(item.posterUrl)}\n                    alt={item.title}\n                    fill\n                    className=\"object-cover transition-transform duration-500 group-hover:scale-110\"\n                  />\n                  <div className=\"absolute inset-0 bg-gradient-to-t from-black via-transparent to-transparent opacity-60 group-hover:opacity-80 transition-opacity duration-300\" />\n                  \n                  <div className=\"absolute inset-0 flex items-center justify-center opacity-0 group-hover:opacity-100 transition-all duration-300\">\n                    <div className=\"p-3 bg-white/20 rounded-full backdrop-blur-sm border border-white/30\">\n                      <Play className=\"w-6 h-6 text-white fill-current\" />\n                    </div>\n                  </div>\n\n                  <div className=\"absolute bottom-0 left-0 right-0 p-3 space-y-1\">\n                    <h4 className=\"text-white font-bold text-sm leading-tight line-clamp-2\">\n                      {item.title}\n                    </h4>\n                    <div className=\"flex items-center justify-between\">\n                      <span className=\"text-gray-300 text-xs\">\n                        {item.country || item.language}\n                      </span>\n                      {item.imdbRating && (\n                        <div className=\"flex items-center space-x-1 px-1.5 py-0.5 bg-yellow-500/20 rounded-full\">\n                          <Star className=\"w-2.5 h-2.5 text-yellow-400 fill-current\" />\n                          <span className=\"text-yellow-400 text-xs font-bold\">\n                            {formatRating(item.imdbRating)}\n                          </span>\n                        </div>\n                      )}\n                    </div>\n                  </div>\n                </div>\n              </Link>\n            </motion.div>\n          ))}\n        </div>\n      </div>\n    </motion.section>\n  );\n};\n\nexport default GlobalCinema;\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAAA;AAAA;AACA;AAAA;AACA;;;AAPA;;;;;;;AA4BA,MAAM,eAA4C,CAAC,EACjD,MAAM,EACN,MAAM,EACN,SAAS,EACT,KAAK,EACN;;IACC,MAAM,aAAa,6JAAA,CAAA,UAAK,CAAC,MAAM,CAAiB;IAChD,MAAM,WAAW,CAAA,GAAA,gLAAA,CAAA,YAAS,AAAD,EAAE,YAAY;QAAE,MAAM;QAAM,QAAQ;IAAS;IAEtE,MAAM,aAAa;WACd,OAAO,GAAG,CAAC,CAAA,OAAQ,CAAC;gBAAE,GAAG,IAAI;gBAAE,MAAM;YAAiB,CAAC;WACvD,OAAO,GAAG,CAAC,CAAA,OAAQ,CAAC;gBAAE,GAAG,IAAI;gBAAE,MAAM;YAAkB,CAAC;KAC5D,CAAC,KAAK,CAAC,GAAG;IAEX,IAAI,WAAW,MAAM,KAAK,GAAG,OAAO;IAEpC,qBACE,6LAAC,6LAAA,CAAA,SAAM,CAAC,OAAO;QACb,KAAK;QACL,SAAS;YAAE,SAAS;YAAG,GAAG;QAAG;QAC7B,SAAS,WAAW;YAAE,SAAS;YAAG,GAAG;QAAE,IAAI;YAAE,SAAS;YAAG,GAAG;QAAG;QAC/D,YAAY;YAAE,UAAU;QAAI;QAC5B,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,YAAY;QAC1B,OAAO;kBAEP,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC7B,SAAS,WAAW;wBAAE,SAAS;wBAAG,GAAG;oBAAE,IAAI;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC/D,YAAY;wBAAE,UAAU;wBAAK,OAAO;oBAAI;oBACxC,WAAU;;sCAEV,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC,uMAAA,CAAA,QAAK;wCAAC,WAAU;;;;;;;;;;;8CAEnB,6LAAC;oCAAG,WAAU;8CAA6C;;;;;;;;;;;;sCAI7D,6LAAC;4BAAE,WAAU;sCAA0C;;;;;;;;;;;;8BAKzD,6LAAC;oBAAI,WAAU;8BACZ,WAAW,GAAG,CAAC,CAAC,MAAM,sBACrB,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;4BAET,SAAS;gCAAE,SAAS;gCAAG,OAAO;4BAAI;4BAClC,SAAS,WAAW;gCAAE,SAAS;gCAAG,OAAO;4BAAE,IAAI;gCAAE,SAAS;gCAAG,OAAO;4BAAI;4BACxE,YAAY;gCAAE,UAAU;gCAAK,OAAO,MAAM;4BAAM;4BAChD,WAAU;sCAEV,cAAA,6LAAC,+JAAA,CAAA,UAAI;gCAAC,MAAM,CAAC,OAAO,EAAE,KAAK,IAAI,CAAC,CAAC,EAAE,KAAK,MAAM,EAAE;0CAC9C,cAAA,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,gIAAA,CAAA,UAAK;4CACJ,KAAK,CAAA,GAAA,sHAAA,CAAA,cAAW,AAAD,EAAE,KAAK,SAAS;4CAC/B,KAAK,KAAK,KAAK;4CACf,IAAI;4CACJ,WAAU;;;;;;sDAEZ,6LAAC;4CAAI,WAAU;;;;;;sDAEf,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC,qMAAA,CAAA,OAAI;oDAAC,WAAU;;;;;;;;;;;;;;;;sDAIpB,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAG,WAAU;8DACX,KAAK,KAAK;;;;;;8DAEb,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAK,WAAU;sEACb,KAAK,OAAO,IAAI,KAAK,QAAQ;;;;;;wDAE/B,KAAK,UAAU,kBACd,6LAAC;4DAAI,WAAU;;8EACb,6LAAC,qMAAA,CAAA,OAAI;oEAAC,WAAU;;;;;;8EAChB,6LAAC;oEAAK,WAAU;8EACb,CAAA,GAAA,sHAAA,CAAA,eAAY,AAAD,EAAE,KAAK,UAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;2BAlCtC,KAAK,GAAG;;;;;;;;;;;;;;;;;;;;;AAgD3B;GAhGM;;QAOa,gLAAA,CAAA,YAAS;;;KAPtB;uCAkGS", "debugId": null}}, {"offset": {"line": 2082, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/New%20folder%20%2825%29/streamzen/src/components/ContentCard.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState } from 'react';\nimport Link from 'next/link';\nimport Image from 'next/image';\nimport { Play, Star, Calendar, Sparkles } from 'lucide-react';\nimport { motion } from 'framer-motion';\nimport { cn, getImageUrl, formatRating, truncateText } from '@/lib/utils';\n\ninterface ContentCardProps {\n  id: string;\n  imdbId: string;\n  title: string;\n  year?: number;\n  posterUrl?: string;\n  imdbRating?: number;\n  description?: string;\n  type: 'movie' | 'series' | 'episode';\n  season?: number;\n  episode?: number;\n  seriesTitle?: string;\n  seriesPosterUrl?: string;\n  className?: string;\n}\n\nconst ContentCard: React.FC<ContentCardProps> = ({\n  id,\n  imdbId,\n  title,\n  year,\n  posterUrl,\n  imdbRating,\n  description,\n  type,\n  season,\n  episode,\n  seriesTitle,\n  seriesPosterUrl,\n  className\n}) => {\n  const [isHovered, setIsHovered] = useState(false);\n  const [isLoaded, setIsLoaded] = useState(false);\n\n  const href = type === 'movie'\n    ? `/watch/movie/${imdbId}`\n    : type === 'series'\n    ? `/watch/series/${imdbId}`\n    : `/watch/series/${imdbId}?season=${season}&episode=${episode}`;\n\n  const displayTitle = type === 'episode' ? seriesTitle : title;\n  const subtitle = type === 'episode' ? `S${season}E${episode}: ${title}` : year ? `${year}` : '';\n  const displayPosterUrl = type === 'episode' ? (seriesPosterUrl || posterUrl) : posterUrl;\n\n  return (\n    <Link href={href} className=\"block\">\n      <motion.div\n        className={cn('group relative cursor-pointer', className)}\n        onMouseEnter={() => setIsHovered(true)}\n        onMouseLeave={() => setIsHovered(false)}\n        whileHover={{ y: -4, transition: { duration: 0.2, ease: \"easeOut\" } }}\n        whileTap={{ scale: 0.98 }}\n      >\n        {/* Main Card Container */}\n        <div className=\"relative aspect-[2/3] bg-gray-900 overflow-hidden rounded-2xl border border-gray-800 hover:border-gray-600 transition-all duration-300 shadow-lg hover:shadow-2xl\">\n\n          {/* Poster Image */}\n          <div className=\"relative w-full h-full overflow-hidden\">\n            <Image\n              src={getImageUrl(displayPosterUrl)}\n              alt={displayTitle || 'Content poster'}\n              fill\n              className={cn(\n                'object-cover transition-all duration-300',\n                isHovered ? 'scale-105' : 'scale-100'\n              )}\n              sizes=\"(max-width: 768px) 50vw, (max-width: 1200px) 33vw, 25vw\"\n              priority={false}\n              onLoad={() => setIsLoaded(true)}\n            />\n\n            {/* Loading shimmer */}\n            {!isLoaded && (\n              <div className=\"absolute inset-0 bg-gradient-to-r from-gray-800 via-gray-700 to-gray-800 animate-pulse\" />\n            )}\n          </div>\n\n          {/* Gradient Overlay */}\n          <div className=\"absolute inset-0 bg-gradient-to-t from-black/80 via-transparent to-transparent\" />\n\n          {/* Type Badge */}\n          <div className=\"absolute top-3 left-3 z-10\">\n            <div className=\"px-2 py-1 bg-black/70 backdrop-blur-sm rounded-lg border border-white/20\">\n              <span className=\"text-white text-xs font-semibold\">\n                {type === 'episode' ? 'EP' : type === 'movie' ? 'MOVIE' : 'SERIES'}\n              </span>\n            </div>\n          </div>\n\n          {/* Rating Badge */}\n          {imdbRating && (\n            <div className=\"absolute top-3 right-3 z-10\">\n              <div className=\"flex items-center space-x-1 px-2 py-1 bg-black/70 backdrop-blur-sm rounded-lg border border-white/20\">\n                <Star size={12} className=\"text-yellow-400 fill-current\" />\n                <span className=\"text-white text-xs font-semibold\">{formatRating(imdbRating)}</span>\n              </div>\n            </div>\n          )}\n\n          {/* High Rating Indicator */}\n          {imdbRating && imdbRating >= 8.5 && (\n            <div className=\"absolute top-12 right-3 z-10\">\n              <div className=\"p-1 bg-gradient-to-r from-yellow-400 to-orange-500 rounded-full\">\n                <Sparkles size={12} className=\"text-white\" />\n              </div>\n            </div>\n          )}\n\n          {/* Hover Play Button */}\n          <motion.div\n            className=\"absolute inset-0 flex items-center justify-center z-20\"\n            initial={{ opacity: 0, scale: 0.8 }}\n            animate={isHovered ? { opacity: 1, scale: 1 } : { opacity: 0, scale: 0.8 }}\n            transition={{ duration: 0.2 }}\n          >\n            <div className=\"w-16 h-16 bg-red-500 hover:bg-red-600 rounded-full flex items-center justify-center shadow-2xl transition-colors duration-200\">\n              <Play size={20} className=\"text-white fill-current ml-1\" />\n            </div>\n          </motion.div>\n\n          {/* Content Info */}\n          <div className=\"absolute bottom-0 left-0 right-0 p-4 z-10\">\n            <h3 className=\"text-white font-bold text-sm mb-1 line-clamp-2 leading-tight\">\n              {displayTitle}\n            </h3>\n            {subtitle && (\n              <p className=\"text-gray-300 text-xs mb-2 line-clamp-1\">\n                {subtitle}\n              </p>\n            )}\n\n            {/* Additional info on hover */}\n            {isHovered && description && (\n              <motion.p\n                className=\"text-gray-400 text-xs line-clamp-2 leading-relaxed\"\n                initial={{ opacity: 0, y: 10 }}\n                animate={{ opacity: 1, y: 0 }}\n                transition={{ delay: 0.1 }}\n              >\n                {truncateText(description, 80)}\n              </motion.p>\n            )}\n          </div>\n\n          {/* Hover Border Glow */}\n          <div className={cn(\n            'absolute inset-0 rounded-2xl border-2 transition-all duration-300 pointer-events-none',\n            isHovered ? 'border-white/30 shadow-lg' : 'border-transparent'\n          )} />\n        </div>\n      </motion.div>\n    </Link>\n  );\n};\n\nexport default ContentCard;\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAAA;AAAA;AACA;AACA;;;AAPA;;;;;;;AAyBA,MAAM,cAA0C,CAAC,EAC/C,EAAE,EACF,MAAM,EACN,KAAK,EACL,IAAI,EACJ,SAAS,EACT,UAAU,EACV,WAAW,EACX,IAAI,EACJ,MAAM,EACN,OAAO,EACP,WAAW,EACX,eAAe,EACf,SAAS,EACV;;IACC,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEzC,MAAM,OAAO,SAAS,UAClB,CAAC,aAAa,EAAE,QAAQ,GACxB,SAAS,WACT,CAAC,cAAc,EAAE,QAAQ,GACzB,CAAC,cAAc,EAAE,OAAO,QAAQ,EAAE,OAAO,SAAS,EAAE,SAAS;IAEjE,MAAM,eAAe,SAAS,YAAY,cAAc;IACxD,MAAM,WAAW,SAAS,YAAY,CAAC,CAAC,EAAE,OAAO,CAAC,EAAE,QAAQ,EAAE,EAAE,OAAO,GAAG,OAAO,GAAG,MAAM,GAAG;IAC7F,MAAM,mBAAmB,SAAS,YAAa,mBAAmB,YAAa;IAE/E,qBACE,6LAAC,+JAAA,CAAA,UAAI;QAAC,MAAM;QAAM,WAAU;kBAC1B,cAAA,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;YACT,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;YAC/C,cAAc,IAAM,aAAa;YACjC,cAAc,IAAM,aAAa;YACjC,YAAY;gBAAE,GAAG,CAAC;gBAAG,YAAY;oBAAE,UAAU;oBAAK,MAAM;gBAAU;YAAE;YACpE,UAAU;gBAAE,OAAO;YAAK;sBAGxB,cAAA,6LAAC;gBAAI,WAAU;;kCAGb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,gIAAA,CAAA,UAAK;gCACJ,KAAK,CAAA,GAAA,sHAAA,CAAA,cAAW,AAAD,EAAE;gCACjB,KAAK,gBAAgB;gCACrB,IAAI;gCACJ,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,4CACA,YAAY,cAAc;gCAE5B,OAAM;gCACN,UAAU;gCACV,QAAQ,IAAM,YAAY;;;;;;4BAI3B,CAAC,0BACA,6LAAC;gCAAI,WAAU;;;;;;;;;;;;kCAKnB,6LAAC;wBAAI,WAAU;;;;;;kCAGf,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAK,WAAU;0CACb,SAAS,YAAY,OAAO,SAAS,UAAU,UAAU;;;;;;;;;;;;;;;;oBAM/D,4BACC,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,qMAAA,CAAA,OAAI;oCAAC,MAAM;oCAAI,WAAU;;;;;;8CAC1B,6LAAC;oCAAK,WAAU;8CAAoC,CAAA,GAAA,sHAAA,CAAA,eAAY,AAAD,EAAE;;;;;;;;;;;;;;;;;oBAMtE,cAAc,cAAc,qBAC3B,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC,6MAAA,CAAA,WAAQ;gCAAC,MAAM;gCAAI,WAAU;;;;;;;;;;;;;;;;kCAMpC,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,WAAU;wBACV,SAAS;4BAAE,SAAS;4BAAG,OAAO;wBAAI;wBAClC,SAAS,YAAY;4BAAE,SAAS;4BAAG,OAAO;wBAAE,IAAI;4BAAE,SAAS;4BAAG,OAAO;wBAAI;wBACzE,YAAY;4BAAE,UAAU;wBAAI;kCAE5B,cAAA,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC,qMAAA,CAAA,OAAI;gCAAC,MAAM;gCAAI,WAAU;;;;;;;;;;;;;;;;kCAK9B,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAG,WAAU;0CACX;;;;;;4BAEF,0BACC,6LAAC;gCAAE,WAAU;0CACV;;;;;;4BAKJ,aAAa,6BACZ,6LAAC,6LAAA,CAAA,SAAM,CAAC,CAAC;gCACP,WAAU;gCACV,SAAS;oCAAE,SAAS;oCAAG,GAAG;gCAAG;gCAC7B,SAAS;oCAAE,SAAS;oCAAG,GAAG;gCAAE;gCAC5B,YAAY;oCAAE,OAAO;gCAAI;0CAExB,CAAA,GAAA,sHAAA,CAAA,eAAY,AAAD,EAAE,aAAa;;;;;;;;;;;;kCAMjC,6LAAC;wBAAI,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACf,yFACA,YAAY,8BAA8B;;;;;;;;;;;;;;;;;;;;;;AAMtD;GAzIM;KAAA;uCA2IS", "debugId": null}}, {"offset": {"line": 2361, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/New%20folder%20%2825%29/streamzen/src/components/ContentSection.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useRef, useState, useEffect } from 'react';\nimport Link from 'next/link';\nimport { ChevronLeft, ChevronRight, ArrowRight } from 'lucide-react';\nimport { cn } from '@/lib/utils';\nimport ContentCard from './ContentCard';\n\ninterface ContentItem {\n  id: string;\n  imdbId: string;\n  title: string;\n  year?: number;\n  posterUrl?: string;\n  seriesPosterUrl?: string; // For episodes to use their series poster\n  imdbRating?: number;\n  description?: string;\n  type: 'movie' | 'series' | 'episode';\n  season?: number;\n  episode?: number;\n  seriesTitle?: string;\n}\n\ninterface ContentSectionProps {\n  title: string;\n  items: ContentItem[];\n  viewAllHref?: string;\n  className?: string;\n}\n\nconst ContentSection: React.FC<ContentSectionProps> = ({\n  title,\n  items,\n  viewAllHref,\n  className\n}) => {\n  const scrollContainerRef = useRef<HTMLDivElement>(null);\n  const [canScrollLeft, setCanScrollLeft] = useState(false);\n  const [canScrollRight, setCanScrollRight] = useState(true);\n  const [isHovered, setIsHovered] = useState(false);\n\n  const checkScrollButtons = () => {\n    if (!scrollContainerRef.current) return;\n\n    const container = scrollContainerRef.current;\n    setCanScrollLeft(container.scrollLeft > 0);\n    setCanScrollRight(\n      container.scrollLeft < container.scrollWidth - container.clientWidth - 1\n    );\n  };\n\n  useEffect(() => {\n    checkScrollButtons();\n    const container = scrollContainerRef.current;\n    if (container) {\n      container.addEventListener('scroll', checkScrollButtons);\n      return () => container.removeEventListener('scroll', checkScrollButtons);\n    }\n  }, [items]);\n\n  const scroll = (direction: 'left' | 'right') => {\n    if (!scrollContainerRef.current) return;\n\n    const container = scrollContainerRef.current;\n    const cardWidth = 280; // Approximate card width including gap\n    const scrollAmount = cardWidth * 4; // Scroll 4 cards at a time\n\n    container.scrollBy({\n      left: direction === 'left' ? -scrollAmount : scrollAmount,\n      behavior: 'smooth'\n    });\n  };\n\n  if (!items.length) {\n    return null;\n  }\n\n  return (\n    <section\n      className={cn('py-12 animate-fade-in', className)}\n      onMouseEnter={() => setIsHovered(true)}\n      onMouseLeave={() => setIsHovered(false)}\n    >\n      <div className=\"max-w-[2560px] mx-auto px-6 lg:px-12\">\n        {/* Section Header */}\n        <div className=\"flex items-center justify-between mb-8\">\n          <h2 className=\"text-3xl lg:text-4xl font-bold text-white tracking-tight\">\n            {title}\n          </h2>\n          {viewAllHref && (\n            <Link\n              href={viewAllHref}\n              className=\"flex items-center space-x-2 text-gray-400 hover:text-red-500 transition-all duration-300 group focus-ring rounded-lg px-3 py-2\"\n            >\n              <span className=\"font-medium\">View All</span>\n              <ArrowRight size={18} className=\"group-hover:translate-x-1 transition-transform duration-300\" />\n            </Link>\n          )}\n        </div>\n\n        {/* Content Container */}\n        <div className=\"relative group/section\">\n          {/* Left Navigation Button */}\n          <button\n            onClick={() => scroll('left')}\n            disabled={!canScrollLeft}\n            className={cn(\n              'absolute left-0 top-1/2 -translate-y-1/2 -translate-x-16 z-20',\n              'w-14 h-14 glass-elevated rounded-full flex items-center justify-center',\n              'transition-all duration-300 focus-ring shadow-2xl',\n              'hover:scale-110 hover:bg-red-600/20',\n              isHovered && canScrollLeft ? 'opacity-100 -translate-x-8' : 'opacity-0 -translate-x-16',\n              !canScrollLeft && 'cursor-not-allowed opacity-30'\n            )}\n          >\n            <ChevronLeft size={26} className=\"text-white\" />\n          </button>\n\n          {/* Right Navigation Button */}\n          <button\n            onClick={() => scroll('right')}\n            disabled={!canScrollRight}\n            className={cn(\n              'absolute right-0 top-1/2 -translate-y-1/2 translate-x-16 z-20',\n              'w-14 h-14 glass-elevated rounded-full flex items-center justify-center',\n              'transition-all duration-300 focus-ring shadow-2xl',\n              'hover:scale-110 hover:bg-red-600/20',\n              isHovered && canScrollRight ? 'opacity-100 translate-x-8' : 'opacity-0 translate-x-16',\n              !canScrollRight && 'cursor-not-allowed opacity-30'\n            )}\n          >\n            <ChevronRight size={26} className=\"text-white\" />\n          </button>\n\n          {/* Scrollable Content */}\n          <div\n            ref={scrollContainerRef}\n            className=\"flex space-x-6 overflow-x-auto scrollbar-hide pb-6 scroll-smooth\"\n            style={{ scrollSnapType: 'x mandatory' }}\n          >\n            {items.map((item, index) => (\n              <div\n                key={`${item.type}-${item.id}`}\n                className=\"flex-none w-64 lg:w-72 animate-slide-up\"\n                style={{\n                  scrollSnapAlign: 'start',\n                  animationDelay: `${index * 0.1}s`\n                }}\n              >\n                <ContentCard\n                  id={item.id}\n                  imdbId={item.imdbId}\n                  title={item.title}\n                  year={item.year}\n                  posterUrl={item.posterUrl}\n                  seriesPosterUrl={item.seriesPosterUrl}\n                  imdbRating={item.imdbRating}\n                  description={item.description}\n                  type={item.type}\n                  season={item.season}\n                  episode={item.episode}\n                  seriesTitle={item.seriesTitle}\n                />\n              </div>\n            ))}\n          </div>\n\n          {/* Premium Fade Gradients */}\n          <div className=\"absolute left-0 top-0 bottom-6 w-16 bg-gradient-to-r from-black via-black/80 to-transparent pointer-events-none z-10\" />\n          <div className=\"absolute right-0 top-0 bottom-6 w-16 bg-gradient-to-l from-black via-black/80 to-transparent pointer-events-none z-10\" />\n        </div>\n\n        {/* Progress Indicator */}\n        <div className=\"flex justify-center mt-6\">\n          <div className=\"flex space-x-2\">\n            {Array.from({ length: Math.ceil(items.length / 4) }).map((_, index) => (\n              <div\n                key={index}\n                className=\"w-2 h-2 rounded-full bg-white/20 transition-all duration-300\"\n              />\n            ))}\n          </div>\n        </div>\n      </div>\n    </section>\n  );\n};\n\nexport default ContentSection;\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AAAA;AACA;AACA;;;AANA;;;;;;AA8BA,MAAM,iBAAgD,CAAC,EACrD,KAAK,EACL,KAAK,EACL,WAAW,EACX,SAAS,EACV;;IACC,MAAM,qBAAqB,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAkB;IAClD,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACnD,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACrD,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,MAAM,qBAAqB;QACzB,IAAI,CAAC,mBAAmB,OAAO,EAAE;QAEjC,MAAM,YAAY,mBAAmB,OAAO;QAC5C,iBAAiB,UAAU,UAAU,GAAG;QACxC,kBACE,UAAU,UAAU,GAAG,UAAU,WAAW,GAAG,UAAU,WAAW,GAAG;IAE3E;IAEA,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;oCAAE;YACR;YACA,MAAM,YAAY,mBAAmB,OAAO;YAC5C,IAAI,WAAW;gBACb,UAAU,gBAAgB,CAAC,UAAU;gBACrC;gDAAO,IAAM,UAAU,mBAAmB,CAAC,UAAU;;YACvD;QACF;mCAAG;QAAC;KAAM;IAEV,MAAM,SAAS,CAAC;QACd,IAAI,CAAC,mBAAmB,OAAO,EAAE;QAEjC,MAAM,YAAY,mBAAmB,OAAO;QAC5C,MAAM,YAAY,KAAK,uCAAuC;QAC9D,MAAM,eAAe,YAAY,GAAG,2BAA2B;QAE/D,UAAU,QAAQ,CAAC;YACjB,MAAM,cAAc,SAAS,CAAC,eAAe;YAC7C,UAAU;QACZ;IACF;IAEA,IAAI,CAAC,MAAM,MAAM,EAAE;QACjB,OAAO;IACT;IAEA,qBACE,6LAAC;QACC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,yBAAyB;QACvC,cAAc,IAAM,aAAa;QACjC,cAAc,IAAM,aAAa;kBAEjC,cAAA,6LAAC;YAAI,WAAU;;8BAEb,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAG,WAAU;sCACX;;;;;;wBAEF,6BACC,6LAAC,+JAAA,CAAA,UAAI;4BACH,MAAM;4BACN,WAAU;;8CAEV,6LAAC;oCAAK,WAAU;8CAAc;;;;;;8CAC9B,6LAAC,qNAAA,CAAA,aAAU;oCAAC,MAAM;oCAAI,WAAU;;;;;;;;;;;;;;;;;;8BAMtC,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC;4BACC,SAAS,IAAM,OAAO;4BACtB,UAAU,CAAC;4BACX,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,iEACA,0EACA,qDACA,uCACA,aAAa,gBAAgB,+BAA+B,6BAC5D,CAAC,iBAAiB;sCAGpB,cAAA,6LAAC,uNAAA,CAAA,cAAW;gCAAC,MAAM;gCAAI,WAAU;;;;;;;;;;;sCAInC,6LAAC;4BACC,SAAS,IAAM,OAAO;4BACtB,UAAU,CAAC;4BACX,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,iEACA,0EACA,qDACA,uCACA,aAAa,iBAAiB,8BAA8B,4BAC5D,CAAC,kBAAkB;sCAGrB,cAAA,6LAAC,yNAAA,CAAA,eAAY;gCAAC,MAAM;gCAAI,WAAU;;;;;;;;;;;sCAIpC,6LAAC;4BACC,KAAK;4BACL,WAAU;4BACV,OAAO;gCAAE,gBAAgB;4BAAc;sCAEtC,MAAM,GAAG,CAAC,CAAC,MAAM,sBAChB,6LAAC;oCAEC,WAAU;oCACV,OAAO;wCACL,iBAAiB;wCACjB,gBAAgB,GAAG,QAAQ,IAAI,CAAC,CAAC;oCACnC;8CAEA,cAAA,6LAAC,oIAAA,CAAA,UAAW;wCACV,IAAI,KAAK,EAAE;wCACX,QAAQ,KAAK,MAAM;wCACnB,OAAO,KAAK,KAAK;wCACjB,MAAM,KAAK,IAAI;wCACf,WAAW,KAAK,SAAS;wCACzB,iBAAiB,KAAK,eAAe;wCACrC,YAAY,KAAK,UAAU;wCAC3B,aAAa,KAAK,WAAW;wCAC7B,MAAM,KAAK,IAAI;wCACf,QAAQ,KAAK,MAAM;wCACnB,SAAS,KAAK,OAAO;wCACrB,aAAa,KAAK,WAAW;;;;;;mCAnB1B,GAAG,KAAK,IAAI,CAAC,CAAC,EAAE,KAAK,EAAE,EAAE;;;;;;;;;;sCA0BpC,6LAAC;4BAAI,WAAU;;;;;;sCACf,6LAAC;4BAAI,WAAU;;;;;;;;;;;;8BAIjB,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;kCACZ,MAAM,IAAI,CAAC;4BAAE,QAAQ,KAAK,IAAI,CAAC,MAAM,MAAM,GAAG;wBAAG,GAAG,GAAG,CAAC,CAAC,GAAG,sBAC3D,6LAAC;gCAEC,WAAU;+BADL;;;;;;;;;;;;;;;;;;;;;;;;;;AASrB;GA5JM;KAAA;uCA8JS", "debugId": null}}, {"offset": {"line": 2615, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/New%20folder%20%2825%29/streamzen/src/components/BingeWorthy.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useRef, useState, useEffect } from 'react';\nimport Link from 'next/link';\nimport Image from 'next/image';\nimport { ChevronLeft, ChevronRight, Star, Play, Tv, Clock } from 'lucide-react';\nimport { motion, useInView } from 'framer-motion';\nimport { cn, getImageUrl, formatRating } from '@/lib/utils';\n\ninterface SeriesItem {\n  _id: string;\n  imdbId: string;\n  title: string;\n  startYear?: number;\n  posterUrl?: string;\n  imdbRating?: number;\n  description?: string;\n  totalSeasons?: number;\n  status?: string;\n}\n\ninterface BingeWorthyProps {\n  series: SeriesItem[];\n  className?: string;\n  style?: React.CSSProperties;\n}\n\nconst BingeWorthy: React.FC<BingeWorthyProps> = ({\n  series,\n  className,\n  style\n}) => {\n  const scrollRef = useRef<HTMLDivElement>(null);\n  const sectionRef = useRef<HTMLDivElement>(null);\n  const isInView = useInView(sectionRef, { once: true, margin: \"-100px\" });\n  const [canScrollLeft, setCanScrollLeft] = useState(false);\n  const [canScrollRight, setCanScrollRight] = useState(true);\n\n  const checkScrollButtons = () => {\n    if (!scrollRef.current) return;\n    const container = scrollRef.current;\n    setCanScrollLeft(container.scrollLeft > 0);\n    setCanScrollRight(\n      container.scrollLeft < container.scrollWidth - container.clientWidth - 1\n    );\n  };\n\n  useEffect(() => {\n    checkScrollButtons();\n    const container = scrollRef.current;\n    if (container) {\n      container.addEventListener('scroll', checkScrollButtons);\n      return () => container.removeEventListener('scroll', checkScrollButtons);\n    }\n  }, [series]);\n\n  const scroll = (direction: 'left' | 'right') => {\n    if (!scrollRef.current) return;\n    const container = scrollRef.current;\n    const scrollAmount = container.clientWidth * 0.7;\n    const targetScroll = direction === 'left' \n      ? container.scrollLeft - scrollAmount\n      : container.scrollLeft + scrollAmount;\n\n    container.scrollTo({\n      left: targetScroll,\n      behavior: 'smooth'\n    });\n  };\n\n  if (series.length === 0) return null;\n\n  return (\n    <motion.section\n      ref={sectionRef}\n      initial={{ opacity: 0, y: 50 }}\n      animate={isInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 50 }}\n      transition={{ duration: 0.8 }}\n      className={cn(\"relative\", className)}\n      style={style}\n    >\n      <div className=\"max-w-[2560px] mx-auto px-6 lg:px-12\">\n        {/* Section Header */}\n        <motion.div\n          initial={{ opacity: 0, y: 30 }}\n          animate={isInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 30 }}\n          transition={{ duration: 0.6, delay: 0.2 }}\n          className=\"flex items-center justify-between mb-8\"\n        >\n          <div className=\"flex items-center space-x-4\">\n            <div className=\"p-3 bg-gradient-to-br from-indigo-500 to-purple-600 rounded-xl\">\n              <Tv className=\"w-6 h-6 text-white\" />\n            </div>\n            <div>\n              <h2 className=\"text-3xl lg:text-4xl font-black text-white\">\n                Binge-Worthy Series\n              </h2>\n              <p className=\"text-gray-400 text-sm\">Perfect for your next marathon</p>\n            </div>\n          </div>\n\n          <Link\n            href=\"/series\"\n            className=\"group px-4 py-2 bg-gradient-to-r from-indigo-500/20 to-purple-600/20 border border-indigo-500/30 text-indigo-400 rounded-xl transition-all duration-300 backdrop-blur-sm hover:scale-105\"\n          >\n            <div className=\"flex items-center space-x-2\">\n              <span className=\"font-semibold\">View All Series</span>\n              <ChevronRight className=\"w-4 h-4 group-hover:translate-x-1 transition-transform duration-300\" />\n            </div>\n          </Link>\n        </motion.div>\n\n        {/* Content Carousel */}\n        <div className=\"relative group\">\n          {/* Navigation Buttons */}\n          {canScrollLeft && (\n            <button\n              onClick={() => scroll('left')}\n              className=\"absolute left-0 top-1/2 -translate-y-1/2 z-10 p-2 bg-black/80 hover:bg-black text-white rounded-full backdrop-blur-sm transition-all duration-300 hover:scale-110 shadow-xl opacity-0 group-hover:opacity-100\"\n            >\n              <ChevronLeft className=\"w-5 h-5\" />\n            </button>\n          )}\n\n          {canScrollRight && (\n            <button\n              onClick={() => scroll('right')}\n              className=\"absolute right-0 top-1/2 -translate-y-1/2 z-10 p-2 bg-black/80 hover:bg-black text-white rounded-full backdrop-blur-sm transition-all duration-300 hover:scale-110 shadow-xl opacity-0 group-hover:opacity-100\"\n            >\n              <ChevronRight className=\"w-5 h-5\" />\n            </button>\n          )}\n\n          {/* Scrollable Content */}\n          <div\n            ref={scrollRef}\n            className=\"flex space-x-4 overflow-x-auto scrollbar-hide pb-2\"\n            style={{ scrollbarWidth: 'none', msOverflowStyle: 'none' }}\n          >\n            {series.slice(0, 12).map((item, index) => (\n              <motion.div\n                key={item._id}\n                initial={{ opacity: 0, scale: 0.9 }}\n                animate={isInView ? { opacity: 1, scale: 1 } : { opacity: 0, scale: 0.9 }}\n                transition={{ duration: 0.5, delay: 0.1 * index }}\n                className=\"flex-shrink-0 group\"\n              >\n                <Link href={`/watch/series/${item.imdbId}`}>\n                  <div className=\"relative w-52 h-78 rounded-xl overflow-hidden bg-gray-900 group-hover:scale-105 transition-all duration-500 shadow-lg hover:shadow-2xl\">\n                    {/* Status Badge */}\n                    {item.status && (\n                      <div className=\"absolute top-2 left-2 z-10 px-2 py-1 bg-green-500/80 rounded-full text-xs font-semibold text-white backdrop-blur-sm\">\n                        {item.status === 'ongoing' ? '🔴 Ongoing' : '✅ Complete'}\n                      </div>\n                    )}\n\n                    {/* Seasons Badge */}\n                    {item.totalSeasons && (\n                      <div className=\"absolute top-2 right-2 z-10 px-2 py-1 bg-black/70 rounded-full text-xs font-semibold text-white backdrop-blur-sm\">\n                        {item.totalSeasons} Season{item.totalSeasons > 1 ? 's' : ''}\n                      </div>\n                    )}\n\n                    {/* Poster Image */}\n                    <Image\n                      src={getImageUrl(item.posterUrl)}\n                      alt={item.title}\n                      fill\n                      className=\"object-cover transition-transform duration-500 group-hover:scale-110\"\n                    />\n\n                    {/* Gradient Overlay */}\n                    <div className=\"absolute inset-0 bg-gradient-to-t from-black via-transparent to-transparent opacity-60 group-hover:opacity-80 transition-opacity duration-300\" />\n\n                    {/* Play Button Overlay */}\n                    <div className=\"absolute inset-0 flex items-center justify-center opacity-0 group-hover:opacity-100 transition-all duration-300\">\n                      <div className=\"p-3 bg-white/20 rounded-full backdrop-blur-sm border border-white/30 group-hover:scale-110 transition-transform duration-300\">\n                        <Play className=\"w-6 h-6 text-white fill-current\" />\n                      </div>\n                    </div>\n\n                    {/* Content Info */}\n                    <div className=\"absolute bottom-0 left-0 right-0 p-3 space-y-2\">\n                      <h4 className=\"text-white font-bold text-sm leading-tight line-clamp-2\">\n                        {item.title}\n                      </h4>\n                      \n                      <div className=\"flex items-center justify-between\">\n                        <span className=\"text-gray-300 text-xs\">\n                          {item.startYear}\n                        </span>\n                        \n                        {item.imdbRating && (\n                          <div className=\"flex items-center space-x-1 px-1.5 py-0.5 bg-yellow-500/20 rounded-full\">\n                            <Star className=\"w-2.5 h-2.5 text-yellow-400 fill-current\" />\n                            <span className=\"text-yellow-400 text-xs font-bold\">\n                              {formatRating(item.imdbRating)}\n                            </span>\n                          </div>\n                        )}\n                      </div>\n\n                      {/* Binge indicator */}\n                      <div className=\"flex items-center space-x-1\">\n                        <Clock className=\"w-3 h-3 text-indigo-400\" />\n                        <span className=\"text-indigo-400 text-xs font-semibold\">\n                          Perfect for binging\n                        </span>\n                      </div>\n                    </div>\n\n                    {/* Accent */}\n                    <div className=\"absolute bottom-0 left-0 w-full h-1 bg-gradient-to-r from-indigo-500 to-purple-600\" />\n                  </div>\n                </Link>\n              </motion.div>\n            ))}\n          </div>\n        </div>\n      </div>\n    </motion.section>\n  );\n};\n\nexport default BingeWorthy;\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AAAA;AACA;;;AAPA;;;;;;;AA2BA,MAAM,cAA0C,CAAC,EAC/C,MAAM,EACN,SAAS,EACT,KAAK,EACN;;IACC,MAAM,YAAY,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAkB;IACzC,MAAM,aAAa,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAkB;IAC1C,MAAM,WAAW,CAAA,GAAA,gLAAA,CAAA,YAAS,AAAD,EAAE,YAAY;QAAE,MAAM;QAAM,QAAQ;IAAS;IACtE,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACnD,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAErD,MAAM,qBAAqB;QACzB,IAAI,CAAC,UAAU,OAAO,EAAE;QACxB,MAAM,YAAY,UAAU,OAAO;QACnC,iBAAiB,UAAU,UAAU,GAAG;QACxC,kBACE,UAAU,UAAU,GAAG,UAAU,WAAW,GAAG,UAAU,WAAW,GAAG;IAE3E;IAEA,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;iCAAE;YACR;YACA,MAAM,YAAY,UAAU,OAAO;YACnC,IAAI,WAAW;gBACb,UAAU,gBAAgB,CAAC,UAAU;gBACrC;6CAAO,IAAM,UAAU,mBAAmB,CAAC,UAAU;;YACvD;QACF;gCAAG;QAAC;KAAO;IAEX,MAAM,SAAS,CAAC;QACd,IAAI,CAAC,UAAU,OAAO,EAAE;QACxB,MAAM,YAAY,UAAU,OAAO;QACnC,MAAM,eAAe,UAAU,WAAW,GAAG;QAC7C,MAAM,eAAe,cAAc,SAC/B,UAAU,UAAU,GAAG,eACvB,UAAU,UAAU,GAAG;QAE3B,UAAU,QAAQ,CAAC;YACjB,MAAM;YACN,UAAU;QACZ;IACF;IAEA,IAAI,OAAO,MAAM,KAAK,GAAG,OAAO;IAEhC,qBACE,6LAAC,6LAAA,CAAA,SAAM,CAAC,OAAO;QACb,KAAK;QACL,SAAS;YAAE,SAAS;YAAG,GAAG;QAAG;QAC7B,SAAS,WAAW;YAAE,SAAS;YAAG,GAAG;QAAE,IAAI;YAAE,SAAS;YAAG,GAAG;QAAG;QAC/D,YAAY;YAAE,UAAU;QAAI;QAC5B,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,YAAY;QAC1B,OAAO;kBAEP,cAAA,6LAAC;YAAI,WAAU;;8BAEb,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC7B,SAAS,WAAW;wBAAE,SAAS;wBAAG,GAAG;oBAAE,IAAI;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC/D,YAAY;wBAAE,UAAU;wBAAK,OAAO;oBAAI;oBACxC,WAAU;;sCAEV,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC,iMAAA,CAAA,KAAE;wCAAC,WAAU;;;;;;;;;;;8CAEhB,6LAAC;;sDACC,6LAAC;4CAAG,WAAU;sDAA6C;;;;;;sDAG3D,6LAAC;4CAAE,WAAU;sDAAwB;;;;;;;;;;;;;;;;;;sCAIzC,6LAAC,+JAAA,CAAA,UAAI;4BACH,MAAK;4BACL,WAAU;sCAEV,cAAA,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAK,WAAU;kDAAgB;;;;;;kDAChC,6LAAC,yNAAA,CAAA,eAAY;wCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;8BAM9B,6LAAC;oBAAI,WAAU;;wBAEZ,+BACC,6LAAC;4BACC,SAAS,IAAM,OAAO;4BACtB,WAAU;sCAEV,cAAA,6LAAC,uNAAA,CAAA,cAAW;gCAAC,WAAU;;;;;;;;;;;wBAI1B,gCACC,6LAAC;4BACC,SAAS,IAAM,OAAO;4BACtB,WAAU;sCAEV,cAAA,6LAAC,yNAAA,CAAA,eAAY;gCAAC,WAAU;;;;;;;;;;;sCAK5B,6LAAC;4BACC,KAAK;4BACL,WAAU;4BACV,OAAO;gCAAE,gBAAgB;gCAAQ,iBAAiB;4BAAO;sCAExD,OAAO,KAAK,CAAC,GAAG,IAAI,GAAG,CAAC,CAAC,MAAM,sBAC9B,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oCAET,SAAS;wCAAE,SAAS;wCAAG,OAAO;oCAAI;oCAClC,SAAS,WAAW;wCAAE,SAAS;wCAAG,OAAO;oCAAE,IAAI;wCAAE,SAAS;wCAAG,OAAO;oCAAI;oCACxE,YAAY;wCAAE,UAAU;wCAAK,OAAO,MAAM;oCAAM;oCAChD,WAAU;8CAEV,cAAA,6LAAC,+JAAA,CAAA,UAAI;wCAAC,MAAM,CAAC,cAAc,EAAE,KAAK,MAAM,EAAE;kDACxC,cAAA,6LAAC;4CAAI,WAAU;;gDAEZ,KAAK,MAAM,kBACV,6LAAC;oDAAI,WAAU;8DACZ,KAAK,MAAM,KAAK,YAAY,eAAe;;;;;;gDAK/C,KAAK,YAAY,kBAChB,6LAAC;oDAAI,WAAU;;wDACZ,KAAK,YAAY;wDAAC;wDAAQ,KAAK,YAAY,GAAG,IAAI,MAAM;;;;;;;8DAK7D,6LAAC,gIAAA,CAAA,UAAK;oDACJ,KAAK,CAAA,GAAA,sHAAA,CAAA,cAAW,AAAD,EAAE,KAAK,SAAS;oDAC/B,KAAK,KAAK,KAAK;oDACf,IAAI;oDACJ,WAAU;;;;;;8DAIZ,6LAAC;oDAAI,WAAU;;;;;;8DAGf,6LAAC;oDAAI,WAAU;8DACb,cAAA,6LAAC;wDAAI,WAAU;kEACb,cAAA,6LAAC,qMAAA,CAAA,OAAI;4DAAC,WAAU;;;;;;;;;;;;;;;;8DAKpB,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAG,WAAU;sEACX,KAAK,KAAK;;;;;;sEAGb,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAK,WAAU;8EACb,KAAK,SAAS;;;;;;gEAGhB,KAAK,UAAU,kBACd,6LAAC;oEAAI,WAAU;;sFACb,6LAAC,qMAAA,CAAA,OAAI;4EAAC,WAAU;;;;;;sFAChB,6LAAC;4EAAK,WAAU;sFACb,CAAA,GAAA,sHAAA,CAAA,eAAY,AAAD,EAAE,KAAK,UAAU;;;;;;;;;;;;;;;;;;sEAOrC,6LAAC;4DAAI,WAAU;;8EACb,6LAAC,uMAAA,CAAA,QAAK;oEAAC,WAAU;;;;;;8EACjB,6LAAC;oEAAK,WAAU;8EAAwC;;;;;;;;;;;;;;;;;;8DAO5D,6LAAC;oDAAI,WAAU;;;;;;;;;;;;;;;;;mCAvEd,KAAK,GAAG;;;;;;;;;;;;;;;;;;;;;;;;;;;AAiF7B;GAnMM;;QAOa,gLAAA,CAAA,YAAS;;;KAPtB;uCAqMS", "debugId": null}}, {"offset": {"line": 3071, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/New%20folder%20%2825%29/streamzen/src/components/HiddenGems.tsx"], "sourcesContent": ["'use client';\n\nimport React from 'react';\nimport Link from 'next/link';\nimport Image from 'next/image';\nimport { Gem, Star, Play } from 'lucide-react';\nimport { motion, useInView } from 'framer-motion';\nimport { cn, getImageUrl, formatRating } from '@/lib/utils';\n\ninterface ContentItem {\n  _id: string;\n  imdbId: string;\n  title: string;\n  year?: number;\n  startYear?: number;\n  posterUrl?: string;\n  imdbRating?: number;\n}\n\ninterface HiddenGemsProps {\n  movies: ContentItem[];\n  series: ContentItem[];\n  className?: string;\n  style?: React.CSSProperties;\n}\n\nconst HiddenGems: React.FC<HiddenGemsProps> = ({\n  movies,\n  series,\n  className,\n  style\n}) => {\n  const sectionRef = React.useRef<HTMLDivElement>(null);\n  const isInView = useInView(sectionRef, { once: true, margin: \"-100px\" });\n\n  const allContent = [\n    ...movies.map(item => ({ ...item, type: 'movie' as const })),\n    ...series.map(item => ({ ...item, type: 'series' as const }))\n  ].slice(0, 6);\n\n  if (allContent.length === 0) return null;\n\n  return (\n    <motion.section\n      ref={sectionRef}\n      initial={{ opacity: 0, y: 50 }}\n      animate={isInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 50 }}\n      transition={{ duration: 0.8 }}\n      className={cn(\"relative\", className)}\n      style={style}\n    >\n      <div className=\"max-w-[2560px] mx-auto px-6 lg:px-12\">\n        <motion.div\n          initial={{ opacity: 0, y: 30 }}\n          animate={isInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 30 }}\n          transition={{ duration: 0.6, delay: 0.2 }}\n          className=\"text-center mb-12\"\n        >\n          <div className=\"flex items-center justify-center space-x-3 mb-4\">\n            <div className=\"p-3 bg-gradient-to-br from-emerald-500 to-teal-600 rounded-xl\">\n              <Gem className=\"w-6 h-6 text-white\" />\n            </div>\n            <h2 className=\"text-4xl lg:text-5xl font-black text-white\">\n              Hidden Gems\n            </h2>\n          </div>\n          <p className=\"text-gray-400 text-lg max-w-2xl mx-auto\">\n            Highly rated treasures waiting to be discovered\n          </p>\n        </motion.div>\n\n        <div className=\"grid grid-cols-2 md:grid-cols-3 gap-6\">\n          {allContent.map((item, index) => (\n            <motion.div\n              key={item._id}\n              initial={{ opacity: 0, scale: 0.9 }}\n              animate={isInView ? { opacity: 1, scale: 1 } : { opacity: 0, scale: 0.9 }}\n              transition={{ duration: 0.5, delay: 0.1 * index }}\n              className=\"group\"\n            >\n              <Link href={`/watch/${item.type}/${item.imdbId}`}>\n                <div className=\"relative w-full h-80 rounded-xl overflow-hidden bg-gray-900 group-hover:scale-105 transition-all duration-500 shadow-lg hover:shadow-2xl\">\n                  <div className=\"absolute top-2 left-2 z-10 px-2 py-1 bg-emerald-500/80 rounded-full text-xs font-semibold text-white backdrop-blur-sm\">\n                    💎 Hidden Gem\n                  </div>\n\n                  <Image\n                    src={getImageUrl(item.posterUrl)}\n                    alt={item.title}\n                    fill\n                    className=\"object-cover transition-transform duration-500 group-hover:scale-110\"\n                  />\n                  <div className=\"absolute inset-0 bg-gradient-to-t from-black via-transparent to-transparent opacity-60 group-hover:opacity-80 transition-opacity duration-300\" />\n                  \n                  <div className=\"absolute inset-0 flex items-center justify-center opacity-0 group-hover:opacity-100 transition-all duration-300\">\n                    <div className=\"p-3 bg-white/20 rounded-full backdrop-blur-sm border border-white/30\">\n                      <Play className=\"w-6 h-6 text-white fill-current\" />\n                    </div>\n                  </div>\n\n                  <div className=\"absolute bottom-0 left-0 right-0 p-3 space-y-2\">\n                    <h4 className=\"text-white font-bold text-sm leading-tight line-clamp-2\">\n                      {item.title}\n                    </h4>\n                    <div className=\"flex items-center justify-between\">\n                      <span className=\"text-gray-300 text-xs\">\n                        {item.year || item.startYear}\n                      </span>\n                      {item.imdbRating && (\n                        <div className=\"flex items-center space-x-1 px-2 py-1 bg-yellow-500/20 rounded-full\">\n                          <Star className=\"w-3 h-3 text-yellow-400 fill-current\" />\n                          <span className=\"text-yellow-400 text-xs font-bold\">\n                            {formatRating(item.imdbRating)}\n                          </span>\n                        </div>\n                      )}\n                    </div>\n                  </div>\n                </div>\n              </Link>\n            </motion.div>\n          ))}\n        </div>\n      </div>\n    </motion.section>\n  );\n};\n\nexport default HiddenGems;\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAAA;AAAA;AACA;AAAA;AACA;;;AAPA;;;;;;;AA0BA,MAAM,aAAwC,CAAC,EAC7C,MAAM,EACN,MAAM,EACN,SAAS,EACT,KAAK,EACN;;IACC,MAAM,aAAa,6JAAA,CAAA,UAAK,CAAC,MAAM,CAAiB;IAChD,MAAM,WAAW,CAAA,GAAA,gLAAA,CAAA,YAAS,AAAD,EAAE,YAAY;QAAE,MAAM;QAAM,QAAQ;IAAS;IAEtE,MAAM,aAAa;WACd,OAAO,GAAG,CAAC,CAAA,OAAQ,CAAC;gBAAE,GAAG,IAAI;gBAAE,MAAM;YAAiB,CAAC;WACvD,OAAO,GAAG,CAAC,CAAA,OAAQ,CAAC;gBAAE,GAAG,IAAI;gBAAE,MAAM;YAAkB,CAAC;KAC5D,CAAC,KAAK,CAAC,GAAG;IAEX,IAAI,WAAW,MAAM,KAAK,GAAG,OAAO;IAEpC,qBACE,6LAAC,6LAAA,CAAA,SAAM,CAAC,OAAO;QACb,KAAK;QACL,SAAS;YAAE,SAAS;YAAG,GAAG;QAAG;QAC7B,SAAS,WAAW;YAAE,SAAS;YAAG,GAAG;QAAE,IAAI;YAAE,SAAS;YAAG,GAAG;QAAG;QAC/D,YAAY;YAAE,UAAU;QAAI;QAC5B,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,YAAY;QAC1B,OAAO;kBAEP,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC7B,SAAS,WAAW;wBAAE,SAAS;wBAAG,GAAG;oBAAE,IAAI;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC/D,YAAY;wBAAE,UAAU;wBAAK,OAAO;oBAAI;oBACxC,WAAU;;sCAEV,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC,mMAAA,CAAA,MAAG;wCAAC,WAAU;;;;;;;;;;;8CAEjB,6LAAC;oCAAG,WAAU;8CAA6C;;;;;;;;;;;;sCAI7D,6LAAC;4BAAE,WAAU;sCAA0C;;;;;;;;;;;;8BAKzD,6LAAC;oBAAI,WAAU;8BACZ,WAAW,GAAG,CAAC,CAAC,MAAM,sBACrB,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;4BAET,SAAS;gCAAE,SAAS;gCAAG,OAAO;4BAAI;4BAClC,SAAS,WAAW;gCAAE,SAAS;gCAAG,OAAO;4BAAE,IAAI;gCAAE,SAAS;gCAAG,OAAO;4BAAI;4BACxE,YAAY;gCAAE,UAAU;gCAAK,OAAO,MAAM;4BAAM;4BAChD,WAAU;sCAEV,cAAA,6LAAC,+JAAA,CAAA,UAAI;gCAAC,MAAM,CAAC,OAAO,EAAE,KAAK,IAAI,CAAC,CAAC,EAAE,KAAK,MAAM,EAAE;0CAC9C,cAAA,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;sDAAwH;;;;;;sDAIvI,6LAAC,gIAAA,CAAA,UAAK;4CACJ,KAAK,CAAA,GAAA,sHAAA,CAAA,cAAW,AAAD,EAAE,KAAK,SAAS;4CAC/B,KAAK,KAAK,KAAK;4CACf,IAAI;4CACJ,WAAU;;;;;;sDAEZ,6LAAC;4CAAI,WAAU;;;;;;sDAEf,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC,qMAAA,CAAA,OAAI;oDAAC,WAAU;;;;;;;;;;;;;;;;sDAIpB,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAG,WAAU;8DACX,KAAK,KAAK;;;;;;8DAEb,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAK,WAAU;sEACb,KAAK,IAAI,IAAI,KAAK,SAAS;;;;;;wDAE7B,KAAK,UAAU,kBACd,6LAAC;4DAAI,WAAU;;8EACb,6LAAC,qMAAA,CAAA,OAAI;oEAAC,WAAU;;;;;;8EAChB,6LAAC;oEAAK,WAAU;8EACb,CAAA,GAAA,sHAAA,CAAA,eAAY,AAAD,EAAE,KAAK,UAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;2BAtCtC,KAAK,GAAG;;;;;;;;;;;;;;;;;;;;;AAoD3B;GApGM;;QAOa,gLAAA,CAAA,YAAS;;;KAPtB;uCAsGS", "debugId": null}}, {"offset": {"line": 3374, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/New%20folder%20%2825%29/streamzen/src/components/QuickPicks.tsx"], "sourcesContent": ["'use client';\n\nimport React from 'react';\nimport Link from 'next/link';\nimport Image from 'next/image';\nimport { Clock, Star, Play } from 'lucide-react';\nimport { motion, useInView } from 'framer-motion';\nimport { cn, getImageUrl, formatRating } from '@/lib/utils';\n\ninterface MovieItem {\n  _id: string;\n  imdbId: string;\n  title: string;\n  year?: number;\n  posterUrl?: string;\n  imdbRating?: number;\n  runtime?: string;\n}\n\ninterface QuickPicksProps {\n  movies: MovieItem[];\n  className?: string;\n  style?: React.CSSProperties;\n}\n\nconst QuickPicks: React.FC<QuickPicksProps> = ({\n  movies,\n  className,\n  style\n}) => {\n  const sectionRef = React.useRef<HTMLDivElement>(null);\n  const isInView = useInView(sectionRef, { once: true, margin: \"-100px\" });\n\n  if (movies.length === 0) return null;\n\n  return (\n    <motion.section\n      ref={sectionRef}\n      initial={{ opacity: 0, y: 50 }}\n      animate={isInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 50 }}\n      transition={{ duration: 0.8 }}\n      className={cn(\"relative\", className)}\n      style={style}\n    >\n      <div className=\"max-w-[2560px] mx-auto px-6 lg:px-12\">\n        <motion.div\n          initial={{ opacity: 0, y: 30 }}\n          animate={isInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 30 }}\n          transition={{ duration: 0.6, delay: 0.2 }}\n          className=\"text-center mb-12\"\n        >\n          <div className=\"flex items-center justify-center space-x-3 mb-4\">\n            <div className=\"p-3 bg-gradient-to-br from-cyan-500 to-blue-600 rounded-xl\">\n              <Clock className=\"w-6 h-6 text-white\" />\n            </div>\n            <h2 className=\"text-4xl lg:text-5xl font-black text-white\">\n              Quick Picks\n            </h2>\n          </div>\n          <p className=\"text-gray-400 text-lg max-w-2xl mx-auto\">\n            Perfect for a quick movie night - under 2 hours\n          </p>\n        </motion.div>\n\n        <div className=\"grid grid-cols-2 md:grid-cols-4 lg:grid-cols-5 gap-4\">\n          {movies.slice(0, 10).map((item, index) => (\n            <motion.div\n              key={item._id}\n              initial={{ opacity: 0, scale: 0.9 }}\n              animate={isInView ? { opacity: 1, scale: 1 } : { opacity: 0, scale: 0.9 }}\n              transition={{ duration: 0.5, delay: 0.1 * index }}\n              className=\"group\"\n            >\n              <Link href={`/watch/movie/${item.imdbId}`}>\n                <div className=\"relative w-full h-72 rounded-xl overflow-hidden bg-gray-900 group-hover:scale-105 transition-all duration-500 shadow-lg hover:shadow-2xl\">\n                  <div className=\"absolute top-2 left-2 z-10 px-2 py-1 bg-cyan-500/80 rounded-full text-xs font-semibold text-white backdrop-blur-sm\">\n                    ⚡ Quick\n                  </div>\n\n                  <Image\n                    src={getImageUrl(item.posterUrl)}\n                    alt={item.title}\n                    fill\n                    className=\"object-cover transition-transform duration-500 group-hover:scale-110\"\n                  />\n                  <div className=\"absolute inset-0 bg-gradient-to-t from-black via-transparent to-transparent opacity-60 group-hover:opacity-80 transition-opacity duration-300\" />\n                  \n                  <div className=\"absolute inset-0 flex items-center justify-center opacity-0 group-hover:opacity-100 transition-all duration-300\">\n                    <div className=\"p-3 bg-white/20 rounded-full backdrop-blur-sm border border-white/30\">\n                      <Play className=\"w-6 h-6 text-white fill-current\" />\n                    </div>\n                  </div>\n\n                  <div className=\"absolute bottom-0 left-0 right-0 p-3 space-y-1\">\n                    <h4 className=\"text-white font-bold text-sm leading-tight line-clamp-2\">\n                      {item.title}\n                    </h4>\n                    <div className=\"flex items-center justify-between\">\n                      <span className=\"text-gray-300 text-xs\">\n                        {item.runtime || `${item.year}`}\n                      </span>\n                      {item.imdbRating && (\n                        <div className=\"flex items-center space-x-1 px-1.5 py-0.5 bg-yellow-500/20 rounded-full\">\n                          <Star className=\"w-2.5 h-2.5 text-yellow-400 fill-current\" />\n                          <span className=\"text-yellow-400 text-xs font-bold\">\n                            {formatRating(item.imdbRating)}\n                          </span>\n                        </div>\n                      )}\n                    </div>\n                  </div>\n                </div>\n              </Link>\n            </motion.div>\n          ))}\n        </div>\n      </div>\n    </motion.section>\n  );\n};\n\nexport default QuickPicks;\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAAA;AAAA;AACA;AAAA;AACA;;;AAPA;;;;;;;AAyBA,MAAM,aAAwC,CAAC,EAC7C,MAAM,EACN,SAAS,EACT,KAAK,EACN;;IACC,MAAM,aAAa,6JAAA,CAAA,UAAK,CAAC,MAAM,CAAiB;IAChD,MAAM,WAAW,CAAA,GAAA,gLAAA,CAAA,YAAS,AAAD,EAAE,YAAY;QAAE,MAAM;QAAM,QAAQ;IAAS;IAEtE,IAAI,OAAO,MAAM,KAAK,GAAG,OAAO;IAEhC,qBACE,6LAAC,6LAAA,CAAA,SAAM,CAAC,OAAO;QACb,KAAK;QACL,SAAS;YAAE,SAAS;YAAG,GAAG;QAAG;QAC7B,SAAS,WAAW;YAAE,SAAS;YAAG,GAAG;QAAE,IAAI;YAAE,SAAS;YAAG,GAAG;QAAG;QAC/D,YAAY;YAAE,UAAU;QAAI;QAC5B,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,YAAY;QAC1B,OAAO;kBAEP,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC7B,SAAS,WAAW;wBAAE,SAAS;wBAAG,GAAG;oBAAE,IAAI;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC/D,YAAY;wBAAE,UAAU;wBAAK,OAAO;oBAAI;oBACxC,WAAU;;sCAEV,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC,uMAAA,CAAA,QAAK;wCAAC,WAAU;;;;;;;;;;;8CAEnB,6LAAC;oCAAG,WAAU;8CAA6C;;;;;;;;;;;;sCAI7D,6LAAC;4BAAE,WAAU;sCAA0C;;;;;;;;;;;;8BAKzD,6LAAC;oBAAI,WAAU;8BACZ,OAAO,KAAK,CAAC,GAAG,IAAI,GAAG,CAAC,CAAC,MAAM,sBAC9B,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;4BAET,SAAS;gCAAE,SAAS;gCAAG,OAAO;4BAAI;4BAClC,SAAS,WAAW;gCAAE,SAAS;gCAAG,OAAO;4BAAE,IAAI;gCAAE,SAAS;gCAAG,OAAO;4BAAI;4BACxE,YAAY;gCAAE,UAAU;gCAAK,OAAO,MAAM;4BAAM;4BAChD,WAAU;sCAEV,cAAA,6LAAC,+JAAA,CAAA,UAAI;gCAAC,MAAM,CAAC,aAAa,EAAE,KAAK,MAAM,EAAE;0CACvC,cAAA,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;sDAAqH;;;;;;sDAIpI,6LAAC,gIAAA,CAAA,UAAK;4CACJ,KAAK,CAAA,GAAA,sHAAA,CAAA,cAAW,AAAD,EAAE,KAAK,SAAS;4CAC/B,KAAK,KAAK,KAAK;4CACf,IAAI;4CACJ,WAAU;;;;;;sDAEZ,6LAAC;4CAAI,WAAU;;;;;;sDAEf,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC,qMAAA,CAAA,OAAI;oDAAC,WAAU;;;;;;;;;;;;;;;;sDAIpB,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAG,WAAU;8DACX,KAAK,KAAK;;;;;;8DAEb,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAK,WAAU;sEACb,KAAK,OAAO,IAAI,GAAG,KAAK,IAAI,EAAE;;;;;;wDAEhC,KAAK,UAAU,kBACd,6LAAC;4DAAI,WAAU;;8EACb,6LAAC,qMAAA,CAAA,OAAI;oEAAC,WAAU;;;;;;8EAChB,6LAAC;oEAAK,WAAU;8EACb,CAAA,GAAA,sHAAA,CAAA,eAAY,AAAD,EAAE,KAAK,UAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;2BAtCtC,KAAK,GAAG;;;;;;;;;;;;;;;;;;;;;AAoD3B;GA9FM;;QAMa,gLAAA,CAAA,YAAS;;;KANtB;uCAgGS", "debugId": null}}, {"offset": {"line": 3667, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/New%20folder%20%2825%29/streamzen/src/components/ModernLoader.tsx"], "sourcesContent": ["'use client';\n\nimport React from 'react';\nimport { motion } from 'framer-motion';\nimport { Play, Sparkles, Film, Tv } from 'lucide-react';\n\ninterface ModernLoaderProps {\n  message?: string;\n  type?: 'default' | 'content' | 'hero';\n  className?: string;\n}\n\nconst ModernLoader: React.FC<ModernLoaderProps> = ({ \n  message = \"Loading amazing content...\", \n  type = 'default',\n  className = \"\"\n}) => {\n  if (type === 'hero') {\n    return (\n      <div className={`h-screen bg-gradient-to-br from-gray-900 via-black to-gray-900 flex items-center justify-center ${className}`}>\n        <div className=\"text-center\">\n          {/* Animated Logo */}\n          <motion.div\n            className=\"relative mb-8\"\n            initial={{ scale: 0.8, opacity: 0 }}\n            animate={{ scale: 1, opacity: 1 }}\n            transition={{ duration: 0.8 }}\n          >\n            <motion.div\n              className=\"w-20 h-20 bg-gradient-to-br from-red-500 to-red-600 rounded-2xl flex items-center justify-center shadow-2xl mx-auto\"\n              animate={{ \n                rotate: [0, 360],\n                scale: [1, 1.1, 1]\n              }}\n              transition={{ \n                rotate: { duration: 3, repeat: Infinity, ease: \"linear\" },\n                scale: { duration: 2, repeat: Infinity, ease: \"easeInOut\" }\n              }}\n            >\n              <Play size={32} className=\"text-white fill-current ml-1\" />\n            </motion.div>\n            \n            {/* Floating sparkles */}\n            {[...Array(6)].map((_, i) => (\n              <motion.div\n                key={i}\n                className=\"absolute w-2 h-2 bg-yellow-400 rounded-full\"\n                style={{\n                  left: `${20 + Math.cos(i * 60 * Math.PI / 180) * 40}px`,\n                  top: `${20 + Math.sin(i * 60 * Math.PI / 180) * 40}px`,\n                }}\n                animate={{\n                  scale: [0, 1, 0],\n                  opacity: [0, 1, 0],\n                }}\n                transition={{\n                  duration: 2,\n                  repeat: Infinity,\n                  delay: i * 0.3,\n                }}\n              >\n                <Sparkles size={8} />\n              </motion.div>\n            ))}\n          </motion.div>\n\n          {/* Loading Text */}\n          <motion.div\n            initial={{ y: 20, opacity: 0 }}\n            animate={{ y: 0, opacity: 1 }}\n            transition={{ duration: 0.6, delay: 0.3 }}\n          >\n            <h2 className=\"text-3xl font-black text-white mb-2\">\n              free<span className=\"text-red-500\">Movies</span>WatchNow\n            </h2>\n            <p className=\"text-gray-400 text-lg\">{message}</p>\n          </motion.div>\n\n          {/* Loading Bar */}\n          <motion.div\n            className=\"w-64 h-1 bg-gray-800 rounded-full overflow-hidden mx-auto mt-6\"\n            initial={{ opacity: 0 }}\n            animate={{ opacity: 1 }}\n            transition={{ delay: 0.6 }}\n          >\n            <motion.div\n              className=\"h-full bg-gradient-to-r from-red-500 to-red-600 rounded-full\"\n              animate={{ x: ['-100%', '100%'] }}\n              transition={{ duration: 1.5, repeat: Infinity, ease: \"easeInOut\" }}\n            />\n          </motion.div>\n        </div>\n      </div>\n    );\n  }\n\n  if (type === 'content') {\n    return (\n      <div className={`h-80 bg-gradient-to-br from-gray-900/20 to-gray-800/20 rounded-3xl flex items-center justify-center border border-gray-800/50 backdrop-blur-sm ${className}`}>\n        <div className=\"text-center\">\n          <motion.div\n            className=\"flex items-center justify-center space-x-2 mb-4\"\n            initial={{ opacity: 0, y: 20 }}\n            animate={{ opacity: 1, y: 0 }}\n            transition={{ duration: 0.6 }}\n          >\n            <motion.div\n              animate={{ rotate: 360 }}\n              transition={{ duration: 2, repeat: Infinity, ease: \"linear\" }}\n            >\n              <Film className=\"w-8 h-8 text-red-500\" />\n            </motion.div>\n            <motion.div\n              animate={{ rotate: -360 }}\n              transition={{ duration: 2.5, repeat: Infinity, ease: \"linear\" }}\n            >\n              <Tv className=\"w-8 h-8 text-blue-500\" />\n            </motion.div>\n          </motion.div>\n          \n          <motion.p\n            className=\"text-gray-400 font-medium\"\n            initial={{ opacity: 0 }}\n            animate={{ opacity: 1 }}\n            transition={{ delay: 0.3 }}\n          >\n            {message}\n          </motion.p>\n        </div>\n      </div>\n    );\n  }\n\n  // Default loader\n  return (\n    <div className={`flex items-center justify-center p-8 ${className}`}>\n      <div className=\"text-center\">\n        <motion.div\n          className=\"w-12 h-12 border-4 border-gray-600 border-t-red-500 rounded-full mx-auto mb-4\"\n          animate={{ rotate: 360 }}\n          transition={{ duration: 1, repeat: Infinity, ease: \"linear\" }}\n        />\n        <p className=\"text-gray-400 text-sm\">{message}</p>\n      </div>\n    </div>\n  );\n};\n\nexport default ModernLoader;\n"], "names": [], "mappings": ";;;;AAGA;AACA;AAAA;AAAA;AAAA;AAJA;;;;AAYA,MAAM,eAA4C,CAAC,EACjD,UAAU,4BAA4B,EACtC,OAAO,SAAS,EAChB,YAAY,EAAE,EACf;IACC,IAAI,SAAS,QAAQ;QACnB,qBACE,6LAAC;YAAI,WAAW,CAAC,gGAAgG,EAAE,WAAW;sBAC5H,cAAA,6LAAC;gBAAI,WAAU;;kCAEb,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,WAAU;wBACV,SAAS;4BAAE,OAAO;4BAAK,SAAS;wBAAE;wBAClC,SAAS;4BAAE,OAAO;4BAAG,SAAS;wBAAE;wBAChC,YAAY;4BAAE,UAAU;wBAAI;;0CAE5B,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gCACT,WAAU;gCACV,SAAS;oCACP,QAAQ;wCAAC;wCAAG;qCAAI;oCAChB,OAAO;wCAAC;wCAAG;wCAAK;qCAAE;gCACpB;gCACA,YAAY;oCACV,QAAQ;wCAAE,UAAU;wCAAG,QAAQ;wCAAU,MAAM;oCAAS;oCACxD,OAAO;wCAAE,UAAU;wCAAG,QAAQ;wCAAU,MAAM;oCAAY;gCAC5D;0CAEA,cAAA,6LAAC,qMAAA,CAAA,OAAI;oCAAC,MAAM;oCAAI,WAAU;;;;;;;;;;;4BAI3B;mCAAI,MAAM;6BAAG,CAAC,GAAG,CAAC,CAAC,GAAG,kBACrB,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oCAET,WAAU;oCACV,OAAO;wCACL,MAAM,GAAG,KAAK,KAAK,GAAG,CAAC,IAAI,KAAK,KAAK,EAAE,GAAG,OAAO,GAAG,EAAE,CAAC;wCACvD,KAAK,GAAG,KAAK,KAAK,GAAG,CAAC,IAAI,KAAK,KAAK,EAAE,GAAG,OAAO,GAAG,EAAE,CAAC;oCACxD;oCACA,SAAS;wCACP,OAAO;4CAAC;4CAAG;4CAAG;yCAAE;wCAChB,SAAS;4CAAC;4CAAG;4CAAG;yCAAE;oCACpB;oCACA,YAAY;wCACV,UAAU;wCACV,QAAQ;wCACR,OAAO,IAAI;oCACb;8CAEA,cAAA,6LAAC,6MAAA,CAAA,WAAQ;wCAAC,MAAM;;;;;;mCAhBX;;;;;;;;;;;kCAsBX,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,SAAS;4BAAE,GAAG;4BAAI,SAAS;wBAAE;wBAC7B,SAAS;4BAAE,GAAG;4BAAG,SAAS;wBAAE;wBAC5B,YAAY;4BAAE,UAAU;4BAAK,OAAO;wBAAI;;0CAExC,6LAAC;gCAAG,WAAU;;oCAAsC;kDAC9C,6LAAC;wCAAK,WAAU;kDAAe;;;;;;oCAAa;;;;;;;0CAElD,6LAAC;gCAAE,WAAU;0CAAyB;;;;;;;;;;;;kCAIxC,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,WAAU;wBACV,SAAS;4BAAE,SAAS;wBAAE;wBACtB,SAAS;4BAAE,SAAS;wBAAE;wBACtB,YAAY;4BAAE,OAAO;wBAAI;kCAEzB,cAAA,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,WAAU;4BACV,SAAS;gCAAE,GAAG;oCAAC;oCAAS;iCAAO;4BAAC;4BAChC,YAAY;gCAAE,UAAU;gCAAK,QAAQ;gCAAU,MAAM;4BAAY;;;;;;;;;;;;;;;;;;;;;;IAM7E;IAEA,IAAI,SAAS,WAAW;QACtB,qBACE,6LAAC;YAAI,WAAW,CAAC,+IAA+I,EAAE,WAAW;sBAC3K,cAAA,6LAAC;gBAAI,WAAU;;kCACb,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,WAAU;wBACV,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAG;wBAC7B,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAE;wBAC5B,YAAY;4BAAE,UAAU;wBAAI;;0CAE5B,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gCACT,SAAS;oCAAE,QAAQ;gCAAI;gCACvB,YAAY;oCAAE,UAAU;oCAAG,QAAQ;oCAAU,MAAM;gCAAS;0CAE5D,cAAA,6LAAC,qMAAA,CAAA,OAAI;oCAAC,WAAU;;;;;;;;;;;0CAElB,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gCACT,SAAS;oCAAE,QAAQ,CAAC;gCAAI;gCACxB,YAAY;oCAAE,UAAU;oCAAK,QAAQ;oCAAU,MAAM;gCAAS;0CAE9D,cAAA,6LAAC,iMAAA,CAAA,KAAE;oCAAC,WAAU;;;;;;;;;;;;;;;;;kCAIlB,6LAAC,6LAAA,CAAA,SAAM,CAAC,CAAC;wBACP,WAAU;wBACV,SAAS;4BAAE,SAAS;wBAAE;wBACtB,SAAS;4BAAE,SAAS;wBAAE;wBACtB,YAAY;4BAAE,OAAO;wBAAI;kCAExB;;;;;;;;;;;;;;;;;IAKX;IAEA,iBAAiB;IACjB,qBACE,6LAAC;QAAI,WAAW,CAAC,qCAAqC,EAAE,WAAW;kBACjE,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,WAAU;oBACV,SAAS;wBAAE,QAAQ;oBAAI;oBACvB,YAAY;wBAAE,UAAU;wBAAG,QAAQ;wBAAU,MAAM;oBAAS;;;;;;8BAE9D,6LAAC;oBAAE,WAAU;8BAAyB;;;;;;;;;;;;;;;;;AAI9C;KAtIM;uCAwIS", "debugId": null}}, {"offset": {"line": 4026, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/New%20folder%20%2825%29/streamzen/src/components/ClientInitializeButton.tsx"], "sourcesContent": ["'use client';\n\nimport dynamic from 'next/dynamic';\n\nconst InitializeButton = dynamic(() => import('./InitializeButton'), {\n  ssr: false,\n  loading: () => (\n    <div className=\"animate-pulse bg-gray-700 h-12 w-48 rounded-lg mx-auto\"></div>\n  )\n});\n\nexport default InitializeButton;\n"], "names": [], "mappings": ";;;;AAEA;;AAFA;;;AAIA,MAAM,mBAAmB,CAAA,GAAA,kKAAA,CAAA,UAAO,AAAD,OAAE;;;;;;IAC/B,KAAK;IACL,SAAS,kBACP,6LAAC;YAAI,WAAU;;;;;;;;uCAIJ", "debugId": null}}]}