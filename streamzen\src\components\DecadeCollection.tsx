'use client';

import React, { useRef, useState, useEffect } from 'react';
import Link from 'next/link';
import Image from 'next/image';
import { ChevronLeft, ChevronRight, Star, Play, Calendar, Clock } from 'lucide-react';
import { motion, useInView } from 'framer-motion';
import { cn, getImageUrl, formatRating } from '@/lib/utils';

interface ContentItem {
  _id: string;
  imdbId: string;
  title: string;
  year?: number;
  startYear?: number;
  posterUrl?: string;
  imdbRating?: number;
  description?: string;
}

interface DecadeCollectionProps {
  modernContent: ContentItem[];
  classicContent: ContentItem[];
  className?: string;
  style?: React.CSSProperties;
}

const DecadeCollection: React.FC<DecadeCollectionProps> = ({
  modernContent,
  classicContent,
  className,
  style
}) => {
  const sectionRef = useRef<HTMLDivElement>(null);
  const isInView = useInView(sectionRef, { once: true, margin: "-100px" });

  const decades = [
    {
      title: 'Modern Masterpieces',
      subtitle: '2020s - Latest & Greatest',
      period: '2020s',
      icon: Clock,
      color: 'from-blue-500 to-cyan-600',
      bgColor: 'from-blue-500/20 to-cyan-600/20',
      borderColor: 'border-blue-500/30',
      textColor: 'text-blue-400',
      content: modernContent.map(item => ({ ...item, type: 'movie' as const })),
      href: '/movies?year=2020'
    },
    {
      title: 'Timeless Classics',
      subtitle: '2000s-2010s - Golden Era',
      period: '2000s-2010s',
      icon: Calendar,
      color: 'from-amber-500 to-orange-600',
      bgColor: 'from-amber-500/20 to-orange-600/20',
      borderColor: 'border-amber-500/30',
      textColor: 'text-amber-400',
      content: classicContent.map(item => ({ ...item, type: 'movie' as const })),
      href: '/movies?year=2010'
    }
  ];

  const DecadeCarousel = ({ decade, index }: { decade: typeof decades[0], index: number }) => {
    const scrollRef = useRef<HTMLDivElement>(null);
    const [canScrollLeft, setCanScrollLeft] = useState(false);
    const [canScrollRight, setCanScrollRight] = useState(true);

    const checkScrollButtons = () => {
      if (!scrollRef.current) return;
      const container = scrollRef.current;
      setCanScrollLeft(container.scrollLeft > 0);
      setCanScrollRight(
        container.scrollLeft < container.scrollWidth - container.clientWidth - 1
      );
    };

    useEffect(() => {
      checkScrollButtons();
      const container = scrollRef.current;
      if (container) {
        container.addEventListener('scroll', checkScrollButtons);
        return () => container.removeEventListener('scroll', checkScrollButtons);
      }
    }, [decade.content]);

    const scroll = (direction: 'left' | 'right') => {
      if (!scrollRef.current) return;
      const container = scrollRef.current;
      const scrollAmount = container.clientWidth * 0.7;
      const targetScroll = direction === 'left' 
        ? container.scrollLeft - scrollAmount
        : container.scrollLeft + scrollAmount;

      container.scrollTo({
        left: targetScroll,
        behavior: 'smooth'
      });
    };

    if (decade.content.length === 0) return null;

    return (
      <motion.div
        initial={{ opacity: 0, y: 50 }}
        animate={isInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 50 }}
        transition={{ duration: 0.8, delay: 0.2 * index }}
        className="space-y-6"
      >
        {/* Decade Header */}
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <div className={cn("p-3 bg-gradient-to-br rounded-xl", decade.color)}>
              <decade.icon className="w-6 h-6 text-white" />
            </div>
            <div>
              <h3 className="text-2xl lg:text-3xl font-black text-white">
                {decade.title}
              </h3>
              <p className="text-gray-400 text-sm">{decade.subtitle}</p>
            </div>
          </div>

          <div className="flex items-center space-x-3">
            <div className={cn(
              "px-3 py-1 bg-gradient-to-r border rounded-full text-sm font-semibold",
              decade.bgColor,
              decade.borderColor,
              decade.textColor
            )}>
              {decade.period}
            </div>
            <Link
              href={decade.href}
              className={cn(
                "group px-4 py-2 bg-gradient-to-r border rounded-xl transition-all duration-300 backdrop-blur-sm hover:scale-105",
                decade.bgColor,
                decade.borderColor,
                decade.textColor
              )}
            >
              <div className="flex items-center space-x-2">
                <span className="font-semibold">View All</span>
                <ChevronRight className="w-4 h-4 group-hover:translate-x-1 transition-transform duration-300" />
              </div>
            </Link>
          </div>
        </div>

        {/* Content Carousel */}
        <div className="relative group">
          {/* Navigation Buttons */}
          {canScrollLeft && (
            <button
              onClick={() => scroll('left')}
              className="absolute left-0 top-1/2 -translate-y-1/2 z-10 p-2 bg-black/80 hover:bg-black text-white rounded-full backdrop-blur-sm transition-all duration-300 hover:scale-110 shadow-xl opacity-0 group-hover:opacity-100"
            >
              <ChevronLeft className="w-5 h-5" />
            </button>
          )}

          {canScrollRight && (
            <button
              onClick={() => scroll('right')}
              className="absolute right-0 top-1/2 -translate-y-1/2 z-10 p-2 bg-black/80 hover:bg-black text-white rounded-full backdrop-blur-sm transition-all duration-300 hover:scale-110 shadow-xl opacity-0 group-hover:opacity-100"
            >
              <ChevronRight className="w-5 h-5" />
            </button>
          )}

          {/* Scrollable Content */}
          <div
            ref={scrollRef}
            className="flex space-x-4 overflow-x-auto scrollbar-hide pb-2"
            style={{ scrollbarWidth: 'none', msOverflowStyle: 'none' }}
          >
            {decade.content.slice(0, 15).map((item, itemIndex) => (
              <motion.div
                key={`${decade.title}-${item._id}`}
                initial={{ opacity: 0, scale: 0.9 }}
                animate={isInView ? { opacity: 1, scale: 1 } : { opacity: 0, scale: 0.9 }}
                transition={{ duration: 0.5, delay: 0.1 * itemIndex }}
                className="flex-shrink-0 group"
              >
                <Link href={`/watch/${item.type}/${item.imdbId}`}>
                  <div className="relative w-52 h-78 rounded-xl overflow-hidden bg-gray-900 group-hover:scale-105 transition-all duration-500 shadow-lg hover:shadow-2xl">
                    {/* Year Badge */}
                    <div className="absolute top-2 left-2 z-10 px-2 py-1 bg-black/70 rounded-full text-xs font-semibold text-white backdrop-blur-sm">
                      {item.year || item.startYear}
                    </div>

                    {/* Type Badge */}
                    <div className="absolute top-2 right-2 z-10 px-2 py-1 bg-black/70 rounded-full text-xs font-semibold text-white backdrop-blur-sm">
                      🎬
                    </div>

                    {/* Poster Image */}
                    <Image
                      src={getImageUrl(item.posterUrl)}
                      alt={item.title}
                      fill
                      className="object-cover transition-transform duration-500 group-hover:scale-110"
                    />

                    {/* Gradient Overlay */}
                    <div className="absolute inset-0 bg-gradient-to-t from-black via-transparent to-transparent opacity-60 group-hover:opacity-80 transition-opacity duration-300" />

                    {/* Play Button Overlay */}
                    <div className="absolute inset-0 flex items-center justify-center opacity-0 group-hover:opacity-100 transition-all duration-300">
                      <div className="p-3 bg-white/20 rounded-full backdrop-blur-sm border border-white/30 group-hover:scale-110 transition-transform duration-300">
                        <Play className="w-6 h-6 text-white fill-current" />
                      </div>
                    </div>

                    {/* Content Info */}
                    <div className="absolute bottom-0 left-0 right-0 p-3 space-y-2">
                      <h4 className="text-white font-bold text-sm leading-tight line-clamp-2">
                        {item.title}
                      </h4>
                      
                      <div className="flex items-center justify-between">
                        <span className={cn("text-xs font-semibold", decade.textColor)}>
                          {decade.period}
                        </span>
                        
                        {item.imdbRating && (
                          <div className="flex items-center space-x-1 px-1.5 py-0.5 bg-yellow-500/20 rounded-full">
                            <Star className="w-2.5 h-2.5 text-yellow-400 fill-current" />
                            <span className="text-yellow-400 text-xs font-bold">
                              {formatRating(item.imdbRating)}
                            </span>
                          </div>
                        )}
                      </div>
                    </div>

                    {/* Decade-specific accent */}
                    <div className={cn(
                      "absolute bottom-0 left-0 w-full h-1 bg-gradient-to-r",
                      decade.color
                    )} />
                  </div>
                </Link>
              </motion.div>
            ))}
          </div>
        </div>
      </motion.div>
    );
  };

  return (
    <motion.section
      ref={sectionRef}
      initial={{ opacity: 0, y: 50 }}
      animate={isInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 50 }}
      transition={{ duration: 0.8 }}
      className={cn("relative", className)}
      style={style}
    >
      <div className="max-w-[2560px] mx-auto px-6 lg:px-12">
        {/* Section Header */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          animate={isInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 30 }}
          transition={{ duration: 0.6, delay: 0.2 }}
          className="text-center mb-12"
        >
          <h2 className="text-4xl lg:text-5xl font-black text-white mb-4">
            Through the Decades
          </h2>
          <p className="text-gray-400 text-lg max-w-2xl mx-auto">
            Journey through cinema history with our curated decade collections
          </p>
        </motion.div>

        {/* Decade Carousels */}
        <div className="space-y-16">
          {decades.map((decade, index) => (
            <DecadeCarousel key={decade.title} decade={decade} index={index} />
          ))}
        </div>

        {/* Timeline Visual */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={isInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 20 }}
          transition={{ duration: 0.6, delay: 0.8 }}
          className="flex justify-center mt-16"
        >
          <div className="flex items-center space-x-4 px-8 py-4 bg-gray-900/50 rounded-xl border border-gray-800 backdrop-blur-sm">
            <Calendar className="w-5 h-5 text-gray-400" />
            <span className="text-gray-300 font-semibold">Explore Cinema Through Time</span>
          </div>
        </motion.div>
      </div>
    </motion.section>
  );
};

export default DecadeCollection;
