'use client';

import React, { useRef, useState, useEffect } from 'react';
import Link from 'next/link';
import Image from 'next/image';
import { ChevronLeft, ChevronRight, TrendingUp, Star, Play, Flame } from 'lucide-react';
import { motion, useInView } from 'framer-motion';
import { cn, getImageUrl, formatRating } from '@/lib/utils';

interface ContentItem {
  _id: string;
  imdbId: string;
  title: string;
  year?: number;
  startYear?: number;
  posterUrl?: string;
  imdbRating?: number;
  description?: string;
  popularity?: number;
}

interface TrendingSectionProps {
  movies: ContentItem[];
  series: ContentItem[];
  className?: string;
  style?: React.CSSProperties;
}

const TrendingSection: React.FC<TrendingSectionProps> = ({
  movies,
  series,
  className,
  style
}) => {
  const scrollContainerRef = useRef<HTMLDivElement>(null);
  const sectionRef = useRef<HTMLDivElement>(null);
  const isInView = useInView(sectionRef, { once: true, margin: "-100px" });
  const [canScrollLeft, setCanScrollLeft] = useState(false);
  const [canScrollRight, setCanScrollRight] = useState(true);
  const [activeTab, setActiveTab] = useState<'all' | 'movies' | 'series'>('all');

  // Combine and sort content by popularity
  const allContent = [
    ...movies.map(item => ({ ...item, type: 'movie' as const })),
    ...series.map(item => ({ ...item, type: 'series' as const }))
  ].sort((a, b) => (b.popularity || 0) - (a.popularity || 0));

  const getFilteredContent = () => {
    switch (activeTab) {
      case 'movies':
        return movies.map(item => ({ ...item, type: 'movie' as const }));
      case 'series':
        return series.map(item => ({ ...item, type: 'series' as const }));
      default:
        return allContent;
    }
  };

  const filteredContent = getFilteredContent();

  const checkScrollButtons = () => {
    if (!scrollContainerRef.current) return;

    const container = scrollContainerRef.current;
    setCanScrollLeft(container.scrollLeft > 0);
    setCanScrollRight(
      container.scrollLeft < container.scrollWidth - container.clientWidth - 1
    );
  };

  useEffect(() => {
    checkScrollButtons();
    const container = scrollContainerRef.current;
    if (container) {
      container.addEventListener('scroll', checkScrollButtons);
      return () => container.removeEventListener('scroll', checkScrollButtons);
    }
  }, [filteredContent]);

  const scroll = (direction: 'left' | 'right') => {
    if (!scrollContainerRef.current) return;

    const container = scrollContainerRef.current;
    const scrollAmount = container.clientWidth * 0.8;
    const targetScroll = direction === 'left' 
      ? container.scrollLeft - scrollAmount
      : container.scrollLeft + scrollAmount;

    container.scrollTo({
      left: targetScroll,
      behavior: 'smooth'
    });
  };

  if (filteredContent.length === 0) {
    return null;
  }

  return (
    <motion.section
      ref={sectionRef}
      initial={{ opacity: 0, y: 50 }}
      animate={isInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 50 }}
      transition={{ duration: 0.8 }}
      className={cn("relative", className)}
      style={style}
    >
      <div className="max-w-[2560px] mx-auto px-6 lg:px-12">
        {/* Section Header */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          animate={isInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 30 }}
          transition={{ duration: 0.6, delay: 0.2 }}
          className="flex items-center justify-between mb-8"
        >
          <div className="flex items-center space-x-4">
            <div className="flex items-center space-x-3">
              <div className="p-3 bg-gradient-to-br from-orange-500 to-red-600 rounded-xl">
                <Flame className="w-6 h-6 text-white" />
              </div>
              <div>
                <h2 className="text-3xl lg:text-4xl font-black text-white">
                  Trending Now
                </h2>
                <p className="text-gray-400 text-sm">What everyone's watching</p>
              </div>
            </div>
            <div className="hidden sm:flex items-center space-x-1 px-3 py-1 bg-gradient-to-r from-orange-500/20 to-red-600/20 border border-orange-500/30 rounded-full">
              <TrendingUp className="w-4 h-4 text-orange-400" />
              <span className="text-orange-400 text-sm font-semibold">Hot</span>
            </div>
          </div>

          {/* Tab Filters */}
          <div className="flex items-center space-x-2 bg-gray-900/50 rounded-xl p-1 backdrop-blur-sm border border-gray-800">
            {[
              { key: 'all', label: 'All', count: allContent.length },
              { key: 'movies', label: 'Movies', count: movies.length },
              { key: 'series', label: 'Series', count: series.length }
            ].map(({ key, label, count }) => (
              <button
                key={key}
                onClick={() => setActiveTab(key as any)}
                className={cn(
                  "px-4 py-2 rounded-lg text-sm font-semibold transition-all duration-300",
                  activeTab === key
                    ? "bg-gradient-to-r from-orange-500 to-red-600 text-white shadow-lg"
                    : "text-gray-400 hover:text-white hover:bg-gray-800/50"
                )}
              >
                {label} ({count})
              </button>
            ))}
          </div>
        </motion.div>

        {/* Content Carousel */}
        <div className="relative group">
          {/* Navigation Buttons */}
          {canScrollLeft && (
            <motion.button
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              onClick={() => scroll('left')}
              className="absolute left-0 top-1/2 -translate-y-1/2 z-10 p-3 bg-black/80 hover:bg-black text-white rounded-full backdrop-blur-sm transition-all duration-300 hover:scale-110 shadow-xl opacity-0 group-hover:opacity-100"
            >
              <ChevronLeft className="w-6 h-6" />
            </motion.button>
          )}

          {canScrollRight && (
            <motion.button
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              onClick={() => scroll('right')}
              className="absolute right-0 top-1/2 -translate-y-1/2 z-10 p-3 bg-black/80 hover:bg-black text-white rounded-full backdrop-blur-sm transition-all duration-300 hover:scale-110 shadow-xl opacity-0 group-hover:opacity-100"
            >
              <ChevronRight className="w-6 h-6" />
            </motion.button>
          )}

          {/* Scrollable Content */}
          <div
            ref={scrollContainerRef}
            className="flex space-x-6 overflow-x-auto scrollbar-hide pb-4"
            style={{ scrollbarWidth: 'none', msOverflowStyle: 'none' }}
          >
            {filteredContent.map((item, index) => (
              <motion.div
                key={`${item.type}-${item._id}`}
                initial={{ opacity: 0, y: 30 }}
                animate={isInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 30 }}
                transition={{ duration: 0.6, delay: 0.1 * index }}
                className="flex-shrink-0 group"
              >
                <Link href={`/watch/${item.type}/${item.imdbId}`}>
                  <div className="relative w-64 h-96 rounded-2xl overflow-hidden bg-gray-900 group-hover:scale-105 transition-all duration-500 shadow-xl hover:shadow-2xl">
                    {/* Trending Badge */}
                    <div className="absolute top-3 left-3 z-10 flex items-center space-x-1 px-2 py-1 bg-gradient-to-r from-orange-500 to-red-600 rounded-full text-xs font-bold text-white">
                      <Flame className="w-3 h-3" />
                      <span>#{index + 1}</span>
                    </div>

                    {/* Type Badge */}
                    <div className="absolute top-3 right-3 z-10 px-2 py-1 bg-black/70 rounded-full text-xs font-semibold text-white backdrop-blur-sm">
                      {item.type === 'movie' ? '🎬' : '📺'}
                    </div>

                    {/* Poster Image */}
                    <Image
                      src={getImageUrl(item.posterUrl)}
                      alt={item.title}
                      fill
                      className="object-cover transition-transform duration-500 group-hover:scale-110"
                    />

                    {/* Gradient Overlay */}
                    <div className="absolute inset-0 bg-gradient-to-t from-black via-transparent to-transparent opacity-60 group-hover:opacity-80 transition-opacity duration-300" />

                    {/* Play Button Overlay */}
                    <div className="absolute inset-0 flex items-center justify-center opacity-0 group-hover:opacity-100 transition-all duration-300">
                      <div className="p-4 bg-white/20 rounded-full backdrop-blur-sm border border-white/30 group-hover:scale-110 transition-transform duration-300">
                        <Play className="w-8 h-8 text-white fill-current" />
                      </div>
                    </div>

                    {/* Content Info */}
                    <div className="absolute bottom-0 left-0 right-0 p-4 space-y-2">
                      <h3 className="text-white font-bold text-lg leading-tight line-clamp-2">
                        {item.title}
                      </h3>
                      
                      <div className="flex items-center justify-between">
                        <span className="text-gray-300 text-sm">
                          {item.year || item.startYear}
                        </span>
                        
                        {item.imdbRating && (
                          <div className="flex items-center space-x-1 px-2 py-1 bg-yellow-500/20 rounded-full">
                            <Star className="w-3 h-3 text-yellow-400 fill-current" />
                            <span className="text-yellow-400 text-xs font-bold">
                              {formatRating(item.imdbRating)}
                            </span>
                          </div>
                        )}
                      </div>

                      {/* Popularity Indicator */}
                      {item.popularity && (
                        <div className="flex items-center space-x-1">
                          <TrendingUp className="w-3 h-3 text-green-400" />
                          <span className="text-green-400 text-xs font-semibold">
                            {Math.round(item.popularity)} views
                          </span>
                        </div>
                      )}
                    </div>
                  </div>
                </Link>
              </motion.div>
            ))}
          </div>
        </div>

        {/* View All Button */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={isInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 20 }}
          transition={{ duration: 0.6, delay: 0.4 }}
          className="flex justify-center mt-8"
        >
          <Link
            href="/movies"
            className="group px-8 py-3 bg-gradient-to-r from-orange-500/20 to-red-600/20 hover:from-orange-500/30 hover:to-red-600/30 border border-orange-500/30 hover:border-orange-500/50 text-orange-400 hover:text-orange-300 font-semibold rounded-xl transition-all duration-300 backdrop-blur-sm"
          >
            <div className="flex items-center space-x-2">
              <span>Explore All Trending</span>
              <ChevronRight className="w-4 h-4 group-hover:translate-x-1 transition-transform duration-300" />
            </div>
          </Link>
        </motion.div>
      </div>
    </motion.section>
  );
};

export default TrendingSection;
