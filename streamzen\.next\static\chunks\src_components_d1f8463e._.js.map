{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/New%20folder%20%2825%29/streamzen/src/components/ui/Button.tsx"], "sourcesContent": ["import React from 'react';\nimport { cn } from '@/lib/utils';\n\ninterface ButtonProps extends React.ButtonHTMLAttributes<HTMLButtonElement> {\n  variant?: 'primary' | 'secondary' | 'ghost';\n  size?: 'sm' | 'md' | 'lg';\n  children: React.ReactNode;\n}\n\nconst Button: React.FC<ButtonProps> = ({\n  variant = 'primary',\n  size = 'md',\n  className,\n  children,\n  ...props\n}) => {\n  const baseClasses = 'inline-flex items-center justify-center rounded-lg font-medium transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-white/20';\n  \n  const variantClasses = {\n    primary: 'bg-white text-black hover:bg-gray-200 active:bg-gray-300',\n    secondary: 'bg-white/10 text-white hover:bg-white/20 active:bg-white/30 backdrop-blur-sm',\n    ghost: 'text-white hover:bg-white/10 active:bg-white/20'\n  };\n  \n  const sizeClasses = {\n    sm: 'px-3 py-1.5 text-sm',\n    md: 'px-4 py-2 text-base',\n    lg: 'px-6 py-3 text-lg'\n  };\n\n  return (\n    <button\n      className={cn(\n        baseClasses,\n        variantClasses[variant],\n        sizeClasses[size],\n        className\n      )}\n      {...props}\n    >\n      {children}\n    </button>\n  );\n};\n\nexport default Button;\n"], "names": [], "mappings": ";;;;AACA;;;AAQA,MAAM,SAAgC,CAAC,EACrC,UAAU,SAAS,EACnB,OAAO,IAAI,EACX,SAAS,EACT,QAAQ,EACR,GAAG,OACJ;IACC,MAAM,cAAc;IAEpB,MAAM,iBAAiB;QACrB,SAAS;QACT,WAAW;QACX,OAAO;IACT;IAEA,MAAM,cAAc;QAClB,IAAI;QACJ,IAAI;QACJ,IAAI;IACN;IAEA,qBACE,6LAAC;QACC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,aACA,cAAc,CAAC,QAAQ,EACvB,WAAW,CAAC,KAAK,EACjB;QAED,GAAG,KAAK;kBAER;;;;;;AAGP;KAlCM;uCAoCS", "debugId": null}}, {"offset": {"line": 49, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/New%20folder%20%2825%29/streamzen/src/components/VideoPlayer.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState, useEffect, useRef } from 'react';\nimport { ArrowLeft, Play, RotateCcw, Settings, Maximize, Volume2, VolumeX, SkipBack, Skip<PERSON>or<PERSON>, Loader, Wifi, WifiOff, Star, Share2, Download, Bookmark } from 'lucide-react';\nimport { useRouter } from 'next/navigation';\nimport { motion, AnimatePresence } from 'framer-motion';\nimport Button from './ui/Button';\nimport { cn } from '@/lib/utils';\n\ninterface StreamingSource {\n  source: string;\n  name: string;\n  url: string;\n  quality: string;\n  priority: number;\n}\n\ninterface VideoPlayerProps {\n  streamingSources: StreamingSource[];\n  title: string;\n  type: 'movie' | 'series' | 'episode';\n}\n\nconst VideoPlayer: React.FC<VideoPlayerProps> = ({ streamingSources = [], title, type }) => {\n  const router = useRouter();\n  const [currentSourceIndex, setCurrentSourceIndex] = useState(0);\n  const [isLoading, setIsLoading] = useState(true);\n  const [showControls, setShowControls] = useState(true);\n  const [isFullscreen, setIsFullscreen] = useState(false);\n  const [isMuted, setIsMuted] = useState(false);\n  const [showSourceSelector, setShowSourceSelector] = useState(false);\n  const [connectionStatus, setConnectionStatus] = useState<'online' | 'offline' | 'slow'>('online');\n  const [loadingProgress, setLoadingProgress] = useState(0);\n  const [showShareMenu, setShowShareMenu] = useState(false);\n\n  const playerRef = useRef<HTMLDivElement>(null);\n  const iframeRef = useRef<HTMLIFrameElement>(null);\n  const controlsTimeoutRef = useRef<NodeJS.Timeout>();\n\n  const currentSource = streamingSources[currentSourceIndex];\n\n  // If no streaming sources available, show error\n  if (!streamingSources || streamingSources.length === 0) {\n    return (\n      <div className=\"relative bg-black h-[70vh] flex items-center justify-center\">\n        <div className=\"text-center text-white\">\n          <h2 className=\"text-2xl font-bold mb-4\">No Streaming Sources Available</h2>\n          <p className=\"text-gray-400 mb-6\">Unable to load streaming sources for this content.</p>\n          <Button onClick={() => router.back()} variant=\"primary\">\n            Go Back\n          </Button>\n        </div>\n      </div>\n    );\n  }\n\n  const goBack = () => {\n    router.back();\n  };\n\n  const switchSource = (index: number) => {\n    setIsLoading(true);\n    setCurrentSourceIndex(index);\n    // Reset loading state after iframe loads\n    setTimeout(() => setIsLoading(false), 2000);\n  };\n\n  const reloadCurrentSource = () => {\n    setIsLoading(true);\n    setLoadingProgress(0);\n    // Force iframe reload by changing key\n    const iframe = iframeRef.current;\n    if (iframe) {\n      iframe.src = iframe.src;\n    }\n    setTimeout(() => setIsLoading(false), 2000);\n  };\n\n  // Enhanced functionality\n  useEffect(() => {\n    // Auto-hide controls after 3 seconds of inactivity\n    const resetControlsTimeout = () => {\n      if (controlsTimeoutRef.current) {\n        clearTimeout(controlsTimeoutRef.current);\n      }\n      setShowControls(true);\n      controlsTimeoutRef.current = setTimeout(() => {\n        setShowControls(false);\n      }, 3000);\n    };\n\n    resetControlsTimeout();\n    return () => {\n      if (controlsTimeoutRef.current) {\n        clearTimeout(controlsTimeoutRef.current);\n      }\n    };\n  }, []);\n\n  // Loading progress simulation\n  useEffect(() => {\n    if (isLoading) {\n      setLoadingProgress(0);\n      const interval = setInterval(() => {\n        setLoadingProgress(prev => {\n          if (prev >= 90) {\n            clearInterval(interval);\n            return prev;\n          }\n          return prev + Math.random() * 15;\n        });\n      }, 200);\n\n      return () => clearInterval(interval);\n    }\n  }, [isLoading]);\n\n  const toggleFullscreen = () => {\n    if (!document.fullscreenElement) {\n      playerRef.current?.requestFullscreen();\n      setIsFullscreen(true);\n    } else {\n      document.exitFullscreen();\n      setIsFullscreen(false);\n    }\n  };\n\n  const handleMouseMove = () => {\n    setShowControls(true);\n    if (controlsTimeoutRef.current) {\n      clearTimeout(controlsTimeoutRef.current);\n    }\n    controlsTimeoutRef.current = setTimeout(() => {\n      setShowControls(false);\n    }, 3000);\n  };\n\n  const shareContent = () => {\n    if (navigator.share) {\n      navigator.share({\n        title: title,\n        url: window.location.href\n      });\n    } else {\n      setShowShareMenu(true);\n    }\n  };\n\n  return (\n    <div\n      ref={playerRef}\n      className=\"relative bg-gradient-to-br from-black via-gray-900 to-black min-h-screen overflow-hidden\"\n      onMouseMove={handleMouseMove}\n    >\n      {/* Animated Background Effects */}\n      <div className=\"absolute inset-0 overflow-hidden pointer-events-none\">\n        <motion.div\n          className=\"absolute -top-40 -left-40 w-80 h-80 bg-red-600/5 rounded-full blur-3xl\"\n          animate={{\n            scale: [1, 1.2, 1],\n            opacity: [0.05, 0.1, 0.05]\n          }}\n          transition={{ duration: 8, repeat: Infinity, ease: \"easeInOut\" }}\n        />\n        <motion.div\n          className=\"absolute -bottom-40 -right-40 w-80 h-80 bg-blue-600/5 rounded-full blur-3xl\"\n          animate={{\n            scale: [1.2, 1, 1.2],\n            opacity: [0.1, 0.05, 0.1]\n          }}\n          transition={{ duration: 10, repeat: Infinity, ease: \"easeInOut\" }}\n        />\n      </div>\n\n      {/* Enhanced Header Controls */}\n      <AnimatePresence>\n        {showControls && (\n          <motion.div\n            initial={{ opacity: 0, y: -20 }}\n            animate={{ opacity: 1, y: 0 }}\n            exit={{ opacity: 0, y: -20 }}\n            transition={{ duration: 0.3 }}\n            className=\"absolute top-0 left-0 right-0 z-50 bg-gradient-to-b from-black/80 via-black/40 to-transparent backdrop-blur-sm\"\n          >\n            <div className=\"max-w-[2560px] mx-auto px-6 lg:px-12 py-6\">\n              <div className=\"flex items-center justify-between\">\n                <div className=\"flex items-center space-x-6\">\n                  <motion.button\n                    onClick={goBack}\n                    className=\"flex items-center space-x-3 px-4 py-2 bg-black/50 backdrop-blur-sm border border-white/20 rounded-xl text-white hover:bg-white/20 transition-all duration-300 focus-ring\"\n                    whileHover={{ scale: 1.05 }}\n                    whileTap={{ scale: 0.95 }}\n                  >\n                    <ArrowLeft size={20} />\n                    <span className=\"font-medium\">Back</span>\n                  </motion.button>\n                  <div className=\"flex flex-col\">\n                    <h1 className=\"text-white text-2xl font-bold truncate max-w-2xl\">\n                      {title}\n                    </h1>\n                    <div className=\"flex items-center space-x-3 text-sm\">\n                      <p className=\"text-gray-400\">Now Playing</p>\n                      <div className=\"flex items-center space-x-1\">\n                        {connectionStatus === 'online' ? (\n                          <Wifi size={14} className=\"text-green-400\" />\n                        ) : connectionStatus === 'slow' ? (\n                          <Wifi size={14} className=\"text-yellow-400\" />\n                        ) : (\n                          <WifiOff size={14} className=\"text-red-400\" />\n                        )}\n                        <span className={cn(\n                          \"text-xs font-medium\",\n                          connectionStatus === 'online' ? 'text-green-400' :\n                          connectionStatus === 'slow' ? 'text-yellow-400' : 'text-red-400'\n                        )}>\n                          {connectionStatus === 'online' ? 'HD' : connectionStatus === 'slow' ? 'SD' : 'Offline'}\n                        </span>\n                      </div>\n                    </div>\n                  </div>\n                </div>\n\n                <div className=\"flex items-center space-x-3\">\n                  {/* Source Selector */}\n                  <div className=\"relative\">\n                    <motion.button\n                      onClick={() => setShowSourceSelector(!showSourceSelector)}\n                      className=\"flex items-center space-x-2 px-4 py-2 bg-black/50 backdrop-blur-sm border border-white/20 rounded-xl text-white hover:bg-white/20 transition-all duration-300 focus-ring\"\n                      whileHover={{ scale: 1.05 }}\n                      whileTap={{ scale: 0.95 }}\n                    >\n                      <Settings size={18} />\n                      <span className=\"font-medium\">Source {currentSourceIndex + 1}</span>\n                    </motion.button>\n\n                    <AnimatePresence>\n                      {showSourceSelector && (\n                        <motion.div\n                          initial={{ opacity: 0, y: 10, scale: 0.95 }}\n                          animate={{ opacity: 1, y: 0, scale: 1 }}\n                          exit={{ opacity: 0, y: 10, scale: 0.95 }}\n                          className=\"absolute top-full right-0 mt-2 w-64 bg-black/90 backdrop-blur-xl border border-white/20 rounded-xl shadow-2xl overflow-hidden z-60\"\n                        >\n                          <div className=\"p-3 border-b border-white/10\">\n                            <h3 className=\"text-white font-semibold\">Streaming Sources</h3>\n                          </div>\n                          <div className=\"max-h-64 overflow-y-auto\">\n                            {streamingSources.map((source, index) => (\n                              <motion.button\n                                key={index}\n                                onClick={() => switchSource(index)}\n                                className={cn(\n                                  \"w-full flex items-center justify-between px-4 py-3 text-left transition-all duration-200\",\n                                  index === currentSourceIndex\n                                    ? \"bg-red-500/20 text-white border-l-2 border-red-500\"\n                                    : \"text-gray-300 hover:text-white hover:bg-white/10\"\n                                )}\n                                whileHover={{ x: 4 }}\n                              >\n                                <div>\n                                  <span className=\"font-medium\">{source.name}</span>\n                                  <p className=\"text-xs text-gray-400\">{source.quality}</p>\n                                </div>\n                                {index === currentSourceIndex && (\n                                  <div className=\"w-2 h-2 bg-red-500 rounded-full animate-pulse\" />\n                                )}\n                              </motion.button>\n                            ))}\n                          </div>\n                        </motion.div>\n                      )}\n                    </AnimatePresence>\n                  </div>\n\n                  {/* Action Buttons */}\n                  <motion.button\n                    onClick={shareContent}\n                    className=\"p-2 bg-black/50 backdrop-blur-sm border border-white/20 rounded-xl text-white hover:bg-white/20 transition-all duration-300 focus-ring\"\n                    whileHover={{ scale: 1.05 }}\n                    whileTap={{ scale: 0.95 }}\n                  >\n                    <Share2 size={18} />\n                  </motion.button>\n\n                  <motion.button\n                    onClick={reloadCurrentSource}\n                    className=\"flex items-center space-x-2 px-4 py-2 bg-black/50 backdrop-blur-sm border border-white/20 rounded-xl text-white hover:bg-white/20 transition-all duration-300 focus-ring\"\n                    whileHover={{ scale: 1.05 }}\n                    whileTap={{ scale: 0.95 }}\n                  >\n                    <RotateCcw size={18} />\n                    <span className=\"font-medium\">Reload</span>\n                  </motion.button>\n                </div>\n              </div>\n            </div>\n          </motion.div>\n        )}\n      </AnimatePresence>\n\n      {/* Enhanced Video Player Container */}\n      <div className=\"relative w-full h-[75vh] lg:h-[85vh] mx-auto max-w-[2560px] px-6 lg:px-12\">\n        <motion.div\n          className=\"relative w-full h-full bg-black rounded-3xl overflow-hidden shadow-2xl border border-white/20\"\n          initial={{ scale: 0.95, opacity: 0 }}\n          animate={{ scale: 1, opacity: 1 }}\n          transition={{ duration: 0.5 }}\n        >\n          {/* Enhanced Loading Overlay */}\n          <AnimatePresence>\n            {isLoading && (\n              <motion.div\n                initial={{ opacity: 0 }}\n                animate={{ opacity: 1 }}\n                exit={{ opacity: 0 }}\n                className=\"absolute inset-0 bg-gradient-to-br from-black/90 via-black/80 to-black/90 backdrop-blur-sm flex items-center justify-center z-30\"\n              >\n                <div className=\"text-center text-white\">\n                  <motion.div\n                    animate={{ rotate: 360 }}\n                    transition={{ duration: 2, repeat: Infinity, ease: \"linear\" }}\n                    className=\"w-20 h-20 border-4 border-gray-600 border-t-red-500 rounded-full mx-auto mb-6\"\n                  />\n                  <motion.div\n                    initial={{ y: 20, opacity: 0 }}\n                    animate={{ y: 0, opacity: 1 }}\n                    transition={{ delay: 0.2 }}\n                  >\n                    <p className=\"text-xl font-semibold mb-2\">Loading {currentSource?.name}...</p>\n                    <p className=\"text-sm text-gray-400 mb-4\">Quality: {currentSource?.quality}</p>\n\n                    {/* Loading Progress Bar */}\n                    <div className=\"w-64 h-2 bg-gray-700 rounded-full overflow-hidden mx-auto\">\n                      <motion.div\n                        className=\"h-full bg-gradient-to-r from-red-500 to-red-600 rounded-full\"\n                        initial={{ width: 0 }}\n                        animate={{ width: `${loadingProgress}%` }}\n                        transition={{ duration: 0.3 }}\n                      />\n                    </div>\n                    <p className=\"text-xs text-gray-500 mt-2\">{Math.round(loadingProgress)}% loaded</p>\n                  </motion.div>\n                </div>\n              </motion.div>\n            )}\n          </AnimatePresence>\n\n          {/* Video iframe */}\n          {currentSource && (\n            <iframe\n              ref={iframeRef}\n              key={`${currentSource.source}-${currentSourceIndex}`}\n              src={currentSource.url}\n              className=\"w-full h-full border-0\"\n              allowFullScreen\n              allow=\"accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share\"\n              title={title}\n              onLoad={() => {\n                setIsLoading(false);\n                setLoadingProgress(100);\n              }}\n            />\n          )}\n\n          {/* Enhanced Bottom Controls */}\n          <AnimatePresence>\n            {showControls && (\n              <motion.div\n                initial={{ opacity: 0, y: 20 }}\n                animate={{ opacity: 1, y: 0 }}\n                exit={{ opacity: 0, y: 20 }}\n                transition={{ duration: 0.3 }}\n                className=\"absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black/90 via-black/60 to-transparent backdrop-blur-sm p-6\"\n              >\n                <div className=\"flex items-center justify-between\">\n                  <div className=\"flex items-center space-x-4\">\n                    <motion.button\n                      className=\"p-3 bg-white/20 backdrop-blur-sm rounded-full text-white hover:bg-white/30 transition-all duration-300\"\n                      whileHover={{ scale: 1.1 }}\n                      whileTap={{ scale: 0.9 }}\n                    >\n                      <SkipBack size={20} />\n                    </motion.button>\n\n                    <motion.button\n                      className=\"p-4 bg-red-500 hover:bg-red-600 rounded-full text-white transition-all duration-300 shadow-lg\"\n                      whileHover={{ scale: 1.1 }}\n                      whileTap={{ scale: 0.9 }}\n                    >\n                      <Play size={24} />\n                    </motion.button>\n\n                    <motion.button\n                      className=\"p-3 bg-white/20 backdrop-blur-sm rounded-full text-white hover:bg-white/30 transition-all duration-300\"\n                      whileHover={{ scale: 1.1 }}\n                      whileTap={{ scale: 0.9 }}\n                    >\n                      <SkipForward size={20} />\n                    </motion.button>\n\n                    <motion.button\n                      onClick={() => setIsMuted(!isMuted)}\n                      className=\"p-3 bg-white/20 backdrop-blur-sm rounded-full text-white hover:bg-white/30 transition-all duration-300\"\n                      whileHover={{ scale: 1.1 }}\n                      whileTap={{ scale: 0.9 }}\n                    >\n                      {isMuted ? <VolumeX size={20} /> : <Volume2 size={20} />}\n                    </motion.button>\n                  </div>\n\n                  <div className=\"flex items-center space-x-4\">\n                    <motion.button\n                      className=\"p-3 bg-white/20 backdrop-blur-sm rounded-full text-white hover:bg-white/30 transition-all duration-300\"\n                      whileHover={{ scale: 1.1 }}\n                      whileTap={{ scale: 0.9 }}\n                    >\n                      <Bookmark size={20} />\n                    </motion.button>\n\n                    <motion.button\n                      onClick={toggleFullscreen}\n                      className=\"p-3 bg-white/20 backdrop-blur-sm rounded-full text-white hover:bg-white/30 transition-all duration-300\"\n                      whileHover={{ scale: 1.1 }}\n                      whileTap={{ scale: 0.9 }}\n                    >\n                      <Maximize size={20} />\n                    </motion.button>\n                  </div>\n                </div>\n              </motion.div>\n            )}\n          </AnimatePresence>\n        </motion.div>\n      </div>\n\n      {/* Premium Source Selector */}\n      <div className=\"glass border-t border-white/5\">\n        <div className=\"max-w-[1920px] mx-auto px-6 lg:px-12 py-8\">\n          <div className=\"flex items-center justify-between mb-8\">\n            <div className=\"flex items-center space-x-4\">\n              <h3 className=\"text-white text-2xl font-bold\">Streaming Sources</h3>\n              <div className=\"glass px-4 py-2 rounded-full\">\n                <span className=\"text-gray-300 text-sm font-medium\">\n                  {streamingSources.length} Available\n                </span>\n              </div>\n            </div>\n            <div className=\"glass-elevated px-6 py-3 rounded-xl\">\n              <div className=\"flex items-center space-x-3\">\n                <div className=\"w-3 h-3 bg-green-400 rounded-full animate-pulse\" />\n                <span className=\"text-white font-medium\">\n                  {currentSource?.name} • {currentSource?.quality}\n                </span>\n              </div>\n            </div>\n          </div>\n\n          <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-6 gap-4\">\n            {streamingSources.map((source, index) => (\n              <button\n                key={source.source}\n                onClick={() => switchSource(index)}\n                className={cn(\n                  'group relative p-6 rounded-2xl border transition-all duration-300 text-left hover:scale-105 focus-ring',\n                  index === currentSourceIndex\n                    ? 'bg-gradient-to-br from-blue-600 to-blue-700 border-blue-500 text-white shadow-2xl'\n                    : 'glass-elevated border-white/10 text-gray-300 hover:bg-white/10 hover:border-white/20'\n                )}\n              >\n                <div className=\"flex items-center space-x-3 mb-3\">\n                  <div className={cn(\n                    'w-10 h-10 rounded-full flex items-center justify-center transition-all duration-300',\n                    index === currentSourceIndex\n                      ? 'bg-white/20'\n                      : 'bg-white/10 group-hover:bg-white/20'\n                  )}>\n                    <Play size={16} className={index === currentSourceIndex ? 'text-white' : 'text-gray-400'} />\n                  </div>\n                  <div className=\"flex-1\">\n                    <h4 className=\"font-bold text-sm\">{source.name}</h4>\n                    <p className=\"text-xs opacity-75\">{source.quality}</p>\n                  </div>\n                </div>\n\n                {/* Priority indicator */}\n                <div className=\"flex items-center justify-between\">\n                  <div className=\"flex space-x-1\">\n                    {Array.from({ length: 5 }).map((_, i) => (\n                      <div\n                        key={i}\n                        className={cn(\n                          'w-1.5 h-1.5 rounded-full',\n                          i < (6 - source.priority)\n                            ? (index === currentSourceIndex ? 'bg-white/60' : 'bg-blue-400/60')\n                            : 'bg-white/20'\n                        )}\n                      />\n                    ))}\n                  </div>\n                  {index === currentSourceIndex && (\n                    <div className=\"text-xs font-medium bg-white/20 px-2 py-1 rounded-full\">\n                      Active\n                    </div>\n                  )}\n                </div>\n\n                {/* Glow effect for active source */}\n                {index === currentSourceIndex && (\n                  <div className=\"absolute -inset-1 bg-gradient-to-r from-blue-500/50 to-purple-500/50 rounded-2xl blur-xl -z-10\" />\n                )}\n              </button>\n            ))}\n          </div>\n\n          <div className=\"mt-8 text-center\">\n            <div className=\"glass-elevated inline-flex items-center space-x-3 px-6 py-4 rounded-2xl\">\n              <div className=\"w-2 h-2 bg-blue-400 rounded-full animate-pulse\" />\n              <p className=\"text-gray-300 text-sm font-medium\">\n                Premium sources are automatically optimized for the best viewing experience\n              </p>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default VideoPlayer;\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AAAA;AACA;AACA;;;AAPA;;;;;;;AAuBA,MAAM,cAA0C,CAAC,EAAE,mBAAmB,EAAE,EAAE,KAAK,EAAE,IAAI,EAAE;;IACrF,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,CAAC,oBAAoB,sBAAsB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7D,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,oBAAoB,sBAAsB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7D,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiC;IACxF,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvD,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEnD,MAAM,YAAY,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAkB;IACzC,MAAM,YAAY,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAqB;IAC5C,MAAM,qBAAqB,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD;IAEhC,MAAM,gBAAgB,gBAAgB,CAAC,mBAAmB;IAE1D,gDAAgD;IAChD,IAAI,CAAC,oBAAoB,iBAAiB,MAAM,KAAK,GAAG;QACtD,qBACE,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAG,WAAU;kCAA0B;;;;;;kCACxC,6LAAC;wBAAE,WAAU;kCAAqB;;;;;;kCAClC,6LAAC,qIAAA,CAAA,UAAM;wBAAC,SAAS,IAAM,OAAO,IAAI;wBAAI,SAAQ;kCAAU;;;;;;;;;;;;;;;;;IAMhE;IAEA,MAAM,SAAS;QACb,OAAO,IAAI;IACb;IAEA,MAAM,eAAe,CAAC;QACpB,aAAa;QACb,sBAAsB;QACtB,yCAAyC;QACzC,WAAW,IAAM,aAAa,QAAQ;IACxC;IAEA,MAAM,sBAAsB;QAC1B,aAAa;QACb,mBAAmB;QACnB,sCAAsC;QACtC,MAAM,SAAS,UAAU,OAAO;QAChC,IAAI,QAAQ;YACV,OAAO,GAAG,GAAG,OAAO,GAAG;QACzB;QACA,WAAW,IAAM,aAAa,QAAQ;IACxC;IAEA,yBAAyB;IACzB,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;iCAAE;YACR,mDAAmD;YACnD,MAAM;8DAAuB;oBAC3B,IAAI,mBAAmB,OAAO,EAAE;wBAC9B,aAAa,mBAAmB,OAAO;oBACzC;oBACA,gBAAgB;oBAChB,mBAAmB,OAAO,GAAG;sEAAW;4BACtC,gBAAgB;wBAClB;qEAAG;gBACL;;YAEA;YACA;yCAAO;oBACL,IAAI,mBAAmB,OAAO,EAAE;wBAC9B,aAAa,mBAAmB,OAAO;oBACzC;gBACF;;QACF;gCAAG,EAAE;IAEL,8BAA8B;IAC9B,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;iCAAE;YACR,IAAI,WAAW;gBACb,mBAAmB;gBACnB,MAAM,WAAW;sDAAY;wBAC3B;8DAAmB,CAAA;gCACjB,IAAI,QAAQ,IAAI;oCACd,cAAc;oCACd,OAAO;gCACT;gCACA,OAAO,OAAO,KAAK,MAAM,KAAK;4BAChC;;oBACF;qDAAG;gBAEH;6CAAO,IAAM,cAAc;;YAC7B;QACF;gCAAG;QAAC;KAAU;IAEd,MAAM,mBAAmB;QACvB,IAAI,CAAC,SAAS,iBAAiB,EAAE;YAC/B,UAAU,OAAO,EAAE;YACnB,gBAAgB;QAClB,OAAO;YACL,SAAS,cAAc;YACvB,gBAAgB;QAClB;IACF;IAEA,MAAM,kBAAkB;QACtB,gBAAgB;QAChB,IAAI,mBAAmB,OAAO,EAAE;YAC9B,aAAa,mBAAmB,OAAO;QACzC;QACA,mBAAmB,OAAO,GAAG,WAAW;YACtC,gBAAgB;QAClB,GAAG;IACL;IAEA,MAAM,eAAe;QACnB,IAAI,UAAU,KAAK,EAAE;YACnB,UAAU,KAAK,CAAC;gBACd,OAAO;gBACP,KAAK,OAAO,QAAQ,CAAC,IAAI;YAC3B;QACF,OAAO;YACL,iBAAiB;QACnB;IACF;IAEA,qBACE,6LAAC;QACC,KAAK;QACL,WAAU;QACV,aAAa;;0BAGb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,WAAU;wBACV,SAAS;4BACP,OAAO;gCAAC;gCAAG;gCAAK;6BAAE;4BAClB,SAAS;gCAAC;gCAAM;gCAAK;6BAAK;wBAC5B;wBACA,YAAY;4BAAE,UAAU;4BAAG,QAAQ;4BAAU,MAAM;wBAAY;;;;;;kCAEjE,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,WAAU;wBACV,SAAS;4BACP,OAAO;gCAAC;gCAAK;gCAAG;6BAAI;4BACpB,SAAS;gCAAC;gCAAK;gCAAM;6BAAI;wBAC3B;wBACA,YAAY;4BAAE,UAAU;4BAAI,QAAQ;4BAAU,MAAM;wBAAY;;;;;;;;;;;;0BAKpE,6LAAC,4LAAA,CAAA,kBAAe;0BACb,8BACC,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,SAAS;wBAAG,GAAG,CAAC;oBAAG;oBAC9B,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAE;oBAC5B,MAAM;wBAAE,SAAS;wBAAG,GAAG,CAAC;oBAAG;oBAC3B,YAAY;wBAAE,UAAU;oBAAI;oBAC5B,WAAU;8BAEV,cAAA,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,6LAAA,CAAA,SAAM,CAAC,MAAM;4CACZ,SAAS;4CACT,WAAU;4CACV,YAAY;gDAAE,OAAO;4CAAK;4CAC1B,UAAU;gDAAE,OAAO;4CAAK;;8DAExB,6LAAC,mNAAA,CAAA,YAAS;oDAAC,MAAM;;;;;;8DACjB,6LAAC;oDAAK,WAAU;8DAAc;;;;;;;;;;;;sDAEhC,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAG,WAAU;8DACX;;;;;;8DAEH,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAE,WAAU;sEAAgB;;;;;;sEAC7B,6LAAC;4DAAI,WAAU;;gEACZ,qBAAqB,yBACpB,6LAAC,qMAAA,CAAA,OAAI;oEAAC,MAAM;oEAAI,WAAU;;;;;2EACxB,qBAAqB,uBACvB,6LAAC,qMAAA,CAAA,OAAI;oEAAC,MAAM;oEAAI,WAAU;;;;;yFAE1B,6LAAC,+MAAA,CAAA,UAAO;oEAAC,MAAM;oEAAI,WAAU;;;;;;8EAE/B,6LAAC;oEAAK,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAChB,uBACA,qBAAqB,WAAW,mBAChC,qBAAqB,SAAS,oBAAoB;8EAEjD,qBAAqB,WAAW,OAAO,qBAAqB,SAAS,OAAO;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8CAOvF,6LAAC;oCAAI,WAAU;;sDAEb,6LAAC;4CAAI,WAAU;;8DACb,6LAAC,6LAAA,CAAA,SAAM,CAAC,MAAM;oDACZ,SAAS,IAAM,sBAAsB,CAAC;oDACtC,WAAU;oDACV,YAAY;wDAAE,OAAO;oDAAK;oDAC1B,UAAU;wDAAE,OAAO;oDAAK;;sEAExB,6LAAC,6MAAA,CAAA,WAAQ;4DAAC,MAAM;;;;;;sEAChB,6LAAC;4DAAK,WAAU;;gEAAc;gEAAQ,qBAAqB;;;;;;;;;;;;;8DAG7D,6LAAC,4LAAA,CAAA,kBAAe;8DACb,oCACC,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wDACT,SAAS;4DAAE,SAAS;4DAAG,GAAG;4DAAI,OAAO;wDAAK;wDAC1C,SAAS;4DAAE,SAAS;4DAAG,GAAG;4DAAG,OAAO;wDAAE;wDACtC,MAAM;4DAAE,SAAS;4DAAG,GAAG;4DAAI,OAAO;wDAAK;wDACvC,WAAU;;0EAEV,6LAAC;gEAAI,WAAU;0EACb,cAAA,6LAAC;oEAAG,WAAU;8EAA2B;;;;;;;;;;;0EAE3C,6LAAC;gEAAI,WAAU;0EACZ,iBAAiB,GAAG,CAAC,CAAC,QAAQ,sBAC7B,6LAAC,6LAAA,CAAA,SAAM,CAAC,MAAM;wEAEZ,SAAS,IAAM,aAAa;wEAC5B,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,4FACA,UAAU,qBACN,uDACA;wEAEN,YAAY;4EAAE,GAAG;wEAAE;;0FAEnB,6LAAC;;kGACC,6LAAC;wFAAK,WAAU;kGAAe,OAAO,IAAI;;;;;;kGAC1C,6LAAC;wFAAE,WAAU;kGAAyB,OAAO,OAAO;;;;;;;;;;;;4EAErD,UAAU,oCACT,6LAAC;gFAAI,WAAU;;;;;;;uEAfZ;;;;;;;;;;;;;;;;;;;;;;;;;;;sDA0BnB,6LAAC,6LAAA,CAAA,SAAM,CAAC,MAAM;4CACZ,SAAS;4CACT,WAAU;4CACV,YAAY;gDAAE,OAAO;4CAAK;4CAC1B,UAAU;gDAAE,OAAO;4CAAK;sDAExB,cAAA,6LAAC,6MAAA,CAAA,SAAM;gDAAC,MAAM;;;;;;;;;;;sDAGhB,6LAAC,6LAAA,CAAA,SAAM,CAAC,MAAM;4CACZ,SAAS;4CACT,WAAU;4CACV,YAAY;gDAAE,OAAO;4CAAK;4CAC1B,UAAU;gDAAE,OAAO;4CAAK;;8DAExB,6LAAC,mNAAA,CAAA,YAAS;oDAAC,MAAM;;;;;;8DACjB,6LAAC;oDAAK,WAAU;8DAAc;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAU5C,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,WAAU;oBACV,SAAS;wBAAE,OAAO;wBAAM,SAAS;oBAAE;oBACnC,SAAS;wBAAE,OAAO;wBAAG,SAAS;oBAAE;oBAChC,YAAY;wBAAE,UAAU;oBAAI;;sCAG5B,6LAAC,4LAAA,CAAA,kBAAe;sCACb,2BACC,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gCACT,SAAS;oCAAE,SAAS;gCAAE;gCACtB,SAAS;oCAAE,SAAS;gCAAE;gCACtB,MAAM;oCAAE,SAAS;gCAAE;gCACnB,WAAU;0CAEV,cAAA,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;4CACT,SAAS;gDAAE,QAAQ;4CAAI;4CACvB,YAAY;gDAAE,UAAU;gDAAG,QAAQ;gDAAU,MAAM;4CAAS;4CAC5D,WAAU;;;;;;sDAEZ,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;4CACT,SAAS;gDAAE,GAAG;gDAAI,SAAS;4CAAE;4CAC7B,SAAS;gDAAE,GAAG;gDAAG,SAAS;4CAAE;4CAC5B,YAAY;gDAAE,OAAO;4CAAI;;8DAEzB,6LAAC;oDAAE,WAAU;;wDAA6B;wDAAS,eAAe;wDAAK;;;;;;;8DACvE,6LAAC;oDAAE,WAAU;;wDAA6B;wDAAU,eAAe;;;;;;;8DAGnE,6LAAC;oDAAI,WAAU;8DACb,cAAA,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wDACT,WAAU;wDACV,SAAS;4DAAE,OAAO;wDAAE;wDACpB,SAAS;4DAAE,OAAO,GAAG,gBAAgB,CAAC,CAAC;wDAAC;wDACxC,YAAY;4DAAE,UAAU;wDAAI;;;;;;;;;;;8DAGhC,6LAAC;oDAAE,WAAU;;wDAA8B,KAAK,KAAK,CAAC;wDAAiB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;wBAQhF,+BACC,6LAAC;4BACC,KAAK;4BAEL,KAAK,cAAc,GAAG;4BACtB,WAAU;4BACV,eAAe;4BACf,OAAM;4BACN,OAAO;4BACP,QAAQ;gCACN,aAAa;gCACb,mBAAmB;4BACrB;2BATK,GAAG,cAAc,MAAM,CAAC,CAAC,EAAE,oBAAoB;;;;;sCAcxD,6LAAC,4LAAA,CAAA,kBAAe;sCACb,8BACC,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gCACT,SAAS;oCAAE,SAAS;oCAAG,GAAG;gCAAG;gCAC7B,SAAS;oCAAE,SAAS;oCAAG,GAAG;gCAAE;gCAC5B,MAAM;oCAAE,SAAS;oCAAG,GAAG;gCAAG;gCAC1B,YAAY;oCAAE,UAAU;gCAAI;gCAC5B,WAAU;0CAEV,cAAA,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;;8DACb,6LAAC,6LAAA,CAAA,SAAM,CAAC,MAAM;oDACZ,WAAU;oDACV,YAAY;wDAAE,OAAO;oDAAI;oDACzB,UAAU;wDAAE,OAAO;oDAAI;8DAEvB,cAAA,6LAAC,iNAAA,CAAA,WAAQ;wDAAC,MAAM;;;;;;;;;;;8DAGlB,6LAAC,6LAAA,CAAA,SAAM,CAAC,MAAM;oDACZ,WAAU;oDACV,YAAY;wDAAE,OAAO;oDAAI;oDACzB,UAAU;wDAAE,OAAO;oDAAI;8DAEvB,cAAA,6LAAC,qMAAA,CAAA,OAAI;wDAAC,MAAM;;;;;;;;;;;8DAGd,6LAAC,6LAAA,CAAA,SAAM,CAAC,MAAM;oDACZ,WAAU;oDACV,YAAY;wDAAE,OAAO;oDAAI;oDACzB,UAAU;wDAAE,OAAO;oDAAI;8DAEvB,cAAA,6LAAC,uNAAA,CAAA,cAAW;wDAAC,MAAM;;;;;;;;;;;8DAGrB,6LAAC,6LAAA,CAAA,SAAM,CAAC,MAAM;oDACZ,SAAS,IAAM,WAAW,CAAC;oDAC3B,WAAU;oDACV,YAAY;wDAAE,OAAO;oDAAI;oDACzB,UAAU;wDAAE,OAAO;oDAAI;8DAEtB,wBAAU,6LAAC,+MAAA,CAAA,UAAO;wDAAC,MAAM;;;;;6EAAS,6LAAC,+MAAA,CAAA,UAAO;wDAAC,MAAM;;;;;;;;;;;;;;;;;sDAItD,6LAAC;4CAAI,WAAU;;8DACb,6LAAC,6LAAA,CAAA,SAAM,CAAC,MAAM;oDACZ,WAAU;oDACV,YAAY;wDAAE,OAAO;oDAAI;oDACzB,UAAU;wDAAE,OAAO;oDAAI;8DAEvB,cAAA,6LAAC,6MAAA,CAAA,WAAQ;wDAAC,MAAM;;;;;;;;;;;8DAGlB,6LAAC,6LAAA,CAAA,SAAM,CAAC,MAAM;oDACZ,SAAS;oDACT,WAAU;oDACV,YAAY;wDAAE,OAAO;oDAAI;oDACzB,UAAU;wDAAE,OAAO;oDAAI;8DAEvB,cAAA,6LAAC,6MAAA,CAAA,WAAQ;wDAAC,MAAM;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAWhC,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAG,WAAU;sDAAgC;;;;;;sDAC9C,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC;gDAAK,WAAU;;oDACb,iBAAiB,MAAM;oDAAC;;;;;;;;;;;;;;;;;;8CAI/B,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;;;;;;0DACf,6LAAC;gDAAK,WAAU;;oDACb,eAAe;oDAAK;oDAAI,eAAe;;;;;;;;;;;;;;;;;;;;;;;;sCAMhD,6LAAC;4BAAI,WAAU;sCACZ,iBAAiB,GAAG,CAAC,CAAC,QAAQ,sBAC7B,6LAAC;oCAEC,SAAS,IAAM,aAAa;oCAC5B,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,0GACA,UAAU,qBACN,sFACA;;sDAGN,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACf,uFACA,UAAU,qBACN,gBACA;8DAEJ,cAAA,6LAAC,qMAAA,CAAA,OAAI;wDAAC,MAAM;wDAAI,WAAW,UAAU,qBAAqB,eAAe;;;;;;;;;;;8DAE3E,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAG,WAAU;sEAAqB,OAAO,IAAI;;;;;;sEAC9C,6LAAC;4DAAE,WAAU;sEAAsB,OAAO,OAAO;;;;;;;;;;;;;;;;;;sDAKrD,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;8DACZ,MAAM,IAAI,CAAC;wDAAE,QAAQ;oDAAE,GAAG,GAAG,CAAC,CAAC,GAAG,kBACjC,6LAAC;4DAEC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,4BACA,IAAK,IAAI,OAAO,QAAQ,GACnB,UAAU,qBAAqB,gBAAgB,mBAChD;2DALD;;;;;;;;;;gDAUV,UAAU,oCACT,6LAAC;oDAAI,WAAU;8DAAyD;;;;;;;;;;;;wCAO3E,UAAU,oCACT,6LAAC;4CAAI,WAAU;;;;;;;mCAhDZ,OAAO,MAAM;;;;;;;;;;sCAsDxB,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;;;;;kDACf,6LAAC;wCAAE,WAAU;kDAAoC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAS/D;GAvfM;;QACW,qIAAA,CAAA,YAAS;;;KADpB;uCAyfS", "debugId": null}}]}