'use client';

import React, { useRef, useState, useEffect } from 'react';
import Link from 'next/link';
import Image from 'next/image';
import { ChevronLeft, ChevronRight, Star, Play, Zap, Heart, Smile } from 'lucide-react';
import { motion, useInView } from 'framer-motion';
import { cn, getImageUrl, formatRating } from '@/lib/utils';

interface ContentItem {
  _id: string;
  imdbId: string;
  title: string;
  year?: number;
  startYear?: number;
  posterUrl?: string;
  imdbRating?: number;
  description?: string;
}

interface GenreSpotlightProps {
  actionMovies: ContentItem[];
  dramaSeries: ContentItem[];
  comedyMovies: ContentItem[];
  className?: string;
  style?: React.CSSProperties;
}

const GenreSpotlight: React.FC<GenreSpotlightProps> = ({
  actionMovies,
  dramaSeries,
  comedyMovies,
  className,
  style
}) => {
  const sectionRef = useRef<HTMLDivElement>(null);
  const isInView = useInView(sectionRef, { once: true, margin: "-100px" });

  const genres = [
    {
      title: 'Action & Adventure',
      subtitle: 'Heart-pounding thrills',
      icon: Zap,
      color: 'from-red-500 to-orange-600',
      bgColor: 'from-red-500/20 to-orange-600/20',
      borderColor: 'border-red-500/30',
      textColor: 'text-red-400',
      content: actionMovies.map(item => ({ ...item, type: 'movie' as const })),
      href: '/movies?genre=Action'
    },
    {
      title: 'Drama Series',
      subtitle: 'Compelling stories',
      icon: Heart,
      color: 'from-purple-500 to-pink-600',
      bgColor: 'from-purple-500/20 to-pink-600/20',
      borderColor: 'border-purple-500/30',
      textColor: 'text-purple-400',
      content: dramaSeries.map(item => ({ ...item, type: 'series' as const })),
      href: '/series?genre=Drama'
    },
    {
      title: 'Comedy Gold',
      subtitle: 'Laugh out loud',
      icon: Smile,
      color: 'from-yellow-500 to-green-600',
      bgColor: 'from-yellow-500/20 to-green-600/20',
      borderColor: 'border-yellow-500/30',
      textColor: 'text-yellow-400',
      content: comedyMovies.map(item => ({ ...item, type: 'movie' as const })),
      href: '/movies?genre=Comedy'
    }
  ];

  const GenreCarousel = ({ genre, index }: { genre: typeof genres[0], index: number }) => {
    const scrollRef = useRef<HTMLDivElement>(null);
    const [canScrollLeft, setCanScrollLeft] = useState(false);
    const [canScrollRight, setCanScrollRight] = useState(true);

    const checkScrollButtons = () => {
      if (!scrollRef.current) return;
      const container = scrollRef.current;
      setCanScrollLeft(container.scrollLeft > 0);
      setCanScrollRight(
        container.scrollLeft < container.scrollWidth - container.clientWidth - 1
      );
    };

    useEffect(() => {
      checkScrollButtons();
      const container = scrollRef.current;
      if (container) {
        container.addEventListener('scroll', checkScrollButtons);
        return () => container.removeEventListener('scroll', checkScrollButtons);
      }
    }, [genre.content]);

    const scroll = (direction: 'left' | 'right') => {
      if (!scrollRef.current) return;
      const container = scrollRef.current;
      const scrollAmount = container.clientWidth * 0.7;
      const targetScroll = direction === 'left' 
        ? container.scrollLeft - scrollAmount
        : container.scrollLeft + scrollAmount;

      container.scrollTo({
        left: targetScroll,
        behavior: 'smooth'
      });
    };

    if (genre.content.length === 0) return null;

    return (
      <motion.div
        initial={{ opacity: 0, y: 50 }}
        animate={isInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 50 }}
        transition={{ duration: 0.8, delay: 0.2 * index }}
        className="space-y-6"
      >
        {/* Genre Header */}
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <div className={cn("p-3 bg-gradient-to-br rounded-xl", genre.color)}>
              <genre.icon className="w-6 h-6 text-white" />
            </div>
            <div>
              <h3 className="text-2xl lg:text-3xl font-black text-white">
                {genre.title}
              </h3>
              <p className="text-gray-400 text-sm">{genre.subtitle}</p>
            </div>
          </div>

          <Link
            href={genre.href}
            className={cn(
              "group px-4 py-2 bg-gradient-to-r border rounded-xl transition-all duration-300 backdrop-blur-sm hover:scale-105",
              genre.bgColor,
              genre.borderColor,
              genre.textColor
            )}
          >
            <div className="flex items-center space-x-2">
              <span className="font-semibold">View All</span>
              <ChevronRight className="w-4 h-4 group-hover:translate-x-1 transition-transform duration-300" />
            </div>
          </Link>
        </div>

        {/* Content Carousel */}
        <div className="relative group">
          {/* Navigation Buttons */}
          {canScrollLeft && (
            <button
              onClick={() => scroll('left')}
              className="absolute left-0 top-1/2 -translate-y-1/2 z-10 p-2 bg-black/80 hover:bg-black text-white rounded-full backdrop-blur-sm transition-all duration-300 hover:scale-110 shadow-xl opacity-0 group-hover:opacity-100"
            >
              <ChevronLeft className="w-5 h-5" />
            </button>
          )}

          {canScrollRight && (
            <button
              onClick={() => scroll('right')}
              className="absolute right-0 top-1/2 -translate-y-1/2 z-10 p-2 bg-black/80 hover:bg-black text-white rounded-full backdrop-blur-sm transition-all duration-300 hover:scale-110 shadow-xl opacity-0 group-hover:opacity-100"
            >
              <ChevronRight className="w-5 h-5" />
            </button>
          )}

          {/* Scrollable Content */}
          <div
            ref={scrollRef}
            className="flex space-x-4 overflow-x-auto scrollbar-hide pb-2"
            style={{ scrollbarWidth: 'none', msOverflowStyle: 'none' }}
          >
            {genre.content.slice(0, 12).map((item, itemIndex) => (
              <motion.div
                key={`${genre.title}-${item._id}`}
                initial={{ opacity: 0, scale: 0.9 }}
                animate={isInView ? { opacity: 1, scale: 1 } : { opacity: 0, scale: 0.9 }}
                transition={{ duration: 0.5, delay: 0.1 * itemIndex }}
                className="flex-shrink-0 group"
              >
                <Link href={`/watch/${item.type}/${item.imdbId}`}>
                  <div className="relative w-48 h-72 rounded-xl overflow-hidden bg-gray-900 group-hover:scale-105 transition-all duration-500 shadow-lg hover:shadow-2xl">
                    {/* Type Badge */}
                    <div className="absolute top-2 right-2 z-10 px-2 py-1 bg-black/70 rounded-full text-xs font-semibold text-white backdrop-blur-sm">
                      {item.type === 'movie' ? '🎬' : '📺'}
                    </div>

                    {/* Poster Image */}
                    <Image
                      src={getImageUrl(item.posterUrl)}
                      alt={item.title}
                      fill
                      className="object-cover transition-transform duration-500 group-hover:scale-110"
                    />

                    {/* Gradient Overlay */}
                    <div className="absolute inset-0 bg-gradient-to-t from-black via-transparent to-transparent opacity-60 group-hover:opacity-80 transition-opacity duration-300" />

                    {/* Play Button Overlay */}
                    <div className="absolute inset-0 flex items-center justify-center opacity-0 group-hover:opacity-100 transition-all duration-300">
                      <div className="p-3 bg-white/20 rounded-full backdrop-blur-sm border border-white/30 group-hover:scale-110 transition-transform duration-300">
                        <Play className="w-6 h-6 text-white fill-current" />
                      </div>
                    </div>

                    {/* Content Info */}
                    <div className="absolute bottom-0 left-0 right-0 p-3 space-y-1">
                      <h4 className="text-white font-bold text-sm leading-tight line-clamp-2">
                        {item.title}
                      </h4>
                      
                      <div className="flex items-center justify-between">
                        <span className="text-gray-300 text-xs">
                          {item.year || item.startYear}
                        </span>
                        
                        {item.imdbRating && (
                          <div className="flex items-center space-x-1 px-1.5 py-0.5 bg-yellow-500/20 rounded-full">
                            <Star className="w-2.5 h-2.5 text-yellow-400 fill-current" />
                            <span className="text-yellow-400 text-xs font-bold">
                              {formatRating(item.imdbRating)}
                            </span>
                          </div>
                        )}
                      </div>
                    </div>

                    {/* Genre-specific accent */}
                    <div className={cn(
                      "absolute top-0 left-0 w-full h-1 bg-gradient-to-r",
                      genre.color
                    )} />
                  </div>
                </Link>
              </motion.div>
            ))}
          </div>
        </div>
      </motion.div>
    );
  };

  return (
    <motion.section
      ref={sectionRef}
      initial={{ opacity: 0, y: 50 }}
      animate={isInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 50 }}
      transition={{ duration: 0.8 }}
      className={cn("relative", className)}
      style={style}
    >
      <div className="max-w-[2560px] mx-auto px-6 lg:px-12">
        {/* Section Header */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          animate={isInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 30 }}
          transition={{ duration: 0.6, delay: 0.2 }}
          className="text-center mb-12"
        >
          <h2 className="text-4xl lg:text-5xl font-black text-white mb-4">
            Genre Spotlights
          </h2>
          <p className="text-gray-400 text-lg max-w-2xl mx-auto">
            Discover your next favorite from our curated genre collections
          </p>
        </motion.div>

        {/* Genre Carousels */}
        <div className="space-y-16">
          {genres.map((genre, index) => (
            <GenreCarousel key={genre.title} genre={genre} index={index} />
          ))}
        </div>

        {/* Explore All Genres */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={isInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 20 }}
          transition={{ duration: 0.6, delay: 0.8 }}
          className="flex justify-center mt-16"
        >
          <Link
            href="/movies"
            className="group px-8 py-4 bg-gradient-to-r from-gray-800/50 to-gray-700/50 hover:from-gray-700/50 hover:to-gray-600/50 border border-gray-700 hover:border-gray-600 text-white font-semibold rounded-xl transition-all duration-300 backdrop-blur-sm hover:scale-105"
          >
            <div className="flex items-center space-x-3">
              <span className="text-lg">Explore All Genres</span>
              <ChevronRight className="w-5 h-5 group-hover:translate-x-1 transition-transform duration-300" />
            </div>
          </Link>
        </motion.div>
      </div>
    </motion.section>
  );
};

export default GenreSpotlight;
