module.exports = {

"[project]/src/models/SyncStatus.ts [app-ssr] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "server/chunks/ssr/src_models_SyncStatus_ts_4c3ccdce._.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/src/models/SyncStatus.ts [app-ssr] (ecmascript)");
    });
});
}}),

};