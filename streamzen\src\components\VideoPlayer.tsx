'use client';

import React, { useState } from 'react';
import { ArrowLeft, Play, RotateCcw } from 'lucide-react';
import { useRouter } from 'next/navigation';
import Button from './ui/Button';
import { cn } from '@/lib/utils';

interface StreamingSource {
  source: string;
  name: string;
  url: string;
  quality: string;
  priority: number;
}

interface VideoPlayerProps {
  streamingSources: StreamingSource[];
  title: string;
  type: 'movie' | 'series' | 'episode';
}

const VideoPlayer: React.FC<VideoPlayerProps> = ({ streamingSources = [], title, type }) => {
  const router = useRouter();
  const [currentSourceIndex, setCurrentSourceIndex] = useState(0);
  const [isLoading, setIsLoading] = useState(false);

  const currentSource = streamingSources[currentSourceIndex];

  // If no streaming sources available, show error
  if (!streamingSources || streamingSources.length === 0) {
    return (
      <div className="relative bg-black h-[70vh] flex items-center justify-center">
        <div className="text-center text-white">
          <h2 className="text-2xl font-bold mb-4">No Streaming Sources Available</h2>
          <p className="text-gray-400 mb-6">Unable to load streaming sources for this content.</p>
          <Button onClick={() => router.back()} variant="primary">
            Go Back
          </Button>
        </div>
      </div>
    );
  }

  const goBack = () => {
    router.back();
  };

  const switchSource = (index: number) => {
    setIsLoading(true);
    setCurrentSourceIndex(index);
    // Reset loading state after iframe loads
    setTimeout(() => setIsLoading(false), 2000);
  };

  const reloadCurrentSource = () => {
    setIsLoading(true);
    // Force iframe reload by changing key
    const iframe = document.querySelector('#video-iframe') as HTMLIFrameElement;
    if (iframe) {
      iframe.src = iframe.src;
    }
    setTimeout(() => setIsLoading(false), 2000);
  };

  return (
    <div className="relative bg-black min-h-screen">
      {/* Premium Header Controls - Positioned to not overlap with player */}
      <div className="relative w-full glass border-b border-white/5 mb-4">
        <div className="max-w-[1920px] mx-auto px-6 lg:px-12 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-6">
              <button
                onClick={goBack}
                className="flex items-center space-x-3 px-4 py-2 glass-elevated rounded-xl text-white hover:bg-white/20 transition-all duration-300 focus-ring"
              >
                <ArrowLeft size={20} />
                <span className="font-medium">Back</span>
              </button>
              <div className="flex flex-col">
                <h1 className="text-white text-xl font-bold truncate max-w-2xl">
                  {title}
                </h1>
                <p className="text-gray-400 text-sm">Now Playing</p>
              </div>
            </div>
            <button
              onClick={reloadCurrentSource}
              className="flex items-center space-x-2 px-4 py-2 glass-elevated rounded-xl text-white hover:bg-white/20 transition-all duration-300 focus-ring"
            >
              <RotateCcw size={18} />
              <span className="font-medium">Reload</span>
            </button>
          </div>
        </div>
      </div>

      {/* Premium Video Player - No overlap with header */}
      <div className="relative h-[80vh] bg-black rounded-t-3xl overflow-hidden">
        {isLoading && (
          <div className="absolute inset-0 flex items-center justify-center bg-black z-40">
            <div className="text-center text-white">
              <div className="relative">
                <div className="w-16 h-16 border-4 border-white/20 border-t-white rounded-full animate-spin mx-auto mb-6"></div>
                <div className="absolute inset-0 w-16 h-16 border-4 border-transparent border-t-blue-500 rounded-full animate-spin mx-auto"></div>
              </div>
              <h3 className="text-xl font-semibold mb-2">Loading {currentSource?.name}</h3>
              <p className="text-gray-400">Preparing your premium viewing experience...</p>
            </div>
          </div>
        )}

        {currentSource && (
          <iframe
            id="video-iframe"
            key={`${currentSource.source}-${currentSourceIndex}`}
            src={currentSource.url}
            className="w-full h-full border-0 rounded-t-3xl"
            allowFullScreen
            allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share"
            title={title}
            onLoad={() => setIsLoading(false)}
          />
        )}
      </div>

      {/* Premium Source Selector */}
      <div className="glass border-t border-white/5">
        <div className="max-w-[1920px] mx-auto px-6 lg:px-12 py-8">
          <div className="flex items-center justify-between mb-8">
            <div className="flex items-center space-x-4">
              <h3 className="text-white text-2xl font-bold">Streaming Sources</h3>
              <div className="glass px-4 py-2 rounded-full">
                <span className="text-gray-300 text-sm font-medium">
                  {streamingSources.length} Available
                </span>
              </div>
            </div>
            <div className="glass-elevated px-6 py-3 rounded-xl">
              <div className="flex items-center space-x-3">
                <div className="w-3 h-3 bg-green-400 rounded-full animate-pulse" />
                <span className="text-white font-medium">
                  {currentSource?.name} • {currentSource?.quality}
                </span>
              </div>
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-6 gap-4">
            {streamingSources.map((source, index) => (
              <button
                key={source.source}
                onClick={() => switchSource(index)}
                className={cn(
                  'group relative p-6 rounded-2xl border transition-all duration-300 text-left hover:scale-105 focus-ring',
                  index === currentSourceIndex
                    ? 'bg-gradient-to-br from-blue-600 to-blue-700 border-blue-500 text-white shadow-2xl'
                    : 'glass-elevated border-white/10 text-gray-300 hover:bg-white/10 hover:border-white/20'
                )}
              >
                <div className="flex items-center space-x-3 mb-3">
                  <div className={cn(
                    'w-10 h-10 rounded-full flex items-center justify-center transition-all duration-300',
                    index === currentSourceIndex
                      ? 'bg-white/20'
                      : 'bg-white/10 group-hover:bg-white/20'
                  )}>
                    <Play size={16} className={index === currentSourceIndex ? 'text-white' : 'text-gray-400'} />
                  </div>
                  <div className="flex-1">
                    <h4 className="font-bold text-sm">{source.name}</h4>
                    <p className="text-xs opacity-75">{source.quality}</p>
                  </div>
                </div>

                {/* Priority indicator */}
                <div className="flex items-center justify-between">
                  <div className="flex space-x-1">
                    {Array.from({ length: 5 }).map((_, i) => (
                      <div
                        key={i}
                        className={cn(
                          'w-1.5 h-1.5 rounded-full',
                          i < (6 - source.priority)
                            ? (index === currentSourceIndex ? 'bg-white/60' : 'bg-blue-400/60')
                            : 'bg-white/20'
                        )}
                      />
                    ))}
                  </div>
                  {index === currentSourceIndex && (
                    <div className="text-xs font-medium bg-white/20 px-2 py-1 rounded-full">
                      Active
                    </div>
                  )}
                </div>

                {/* Glow effect for active source */}
                {index === currentSourceIndex && (
                  <div className="absolute -inset-1 bg-gradient-to-r from-blue-500/50 to-purple-500/50 rounded-2xl blur-xl -z-10" />
                )}
              </button>
            ))}
          </div>

          <div className="mt-8 text-center">
            <div className="glass-elevated inline-flex items-center space-x-3 px-6 py-4 rounded-2xl">
              <div className="w-2 h-2 bg-blue-400 rounded-full animate-pulse" />
              <p className="text-gray-300 text-sm font-medium">
                Premium sources are automatically optimized for the best viewing experience
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default VideoPlayer;
