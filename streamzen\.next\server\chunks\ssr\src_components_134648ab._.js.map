{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/New%20folder%20%2825%29/streamzen/src/components/ui/Button.tsx"], "sourcesContent": ["import React from 'react';\nimport { cn } from '@/lib/utils';\n\ninterface ButtonProps extends React.ButtonHTMLAttributes<HTMLButtonElement> {\n  variant?: 'primary' | 'secondary' | 'ghost';\n  size?: 'sm' | 'md' | 'lg';\n  children: React.ReactNode;\n}\n\nconst Button: React.FC<ButtonProps> = ({\n  variant = 'primary',\n  size = 'md',\n  className,\n  children,\n  ...props\n}) => {\n  const baseClasses = 'inline-flex items-center justify-center rounded-lg font-medium transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-white/20';\n  \n  const variantClasses = {\n    primary: 'bg-white text-black hover:bg-gray-200 active:bg-gray-300',\n    secondary: 'bg-white/10 text-white hover:bg-white/20 active:bg-white/30 backdrop-blur-sm',\n    ghost: 'text-white hover:bg-white/10 active:bg-white/20'\n  };\n  \n  const sizeClasses = {\n    sm: 'px-3 py-1.5 text-sm',\n    md: 'px-4 py-2 text-base',\n    lg: 'px-6 py-3 text-lg'\n  };\n\n  return (\n    <button\n      className={cn(\n        baseClasses,\n        variantClasses[variant],\n        sizeClasses[size],\n        className\n      )}\n      {...props}\n    >\n      {children}\n    </button>\n  );\n};\n\nexport default Button;\n"], "names": [], "mappings": ";;;;AACA;;;AAQA,MAAM,SAAgC,CAAC,EACrC,UAAU,SAAS,EACnB,OAAO,IAAI,EACX,SAAS,EACT,QAAQ,EACR,GAAG,OACJ;IACC,MAAM,cAAc;IAEpB,MAAM,iBAAiB;QACrB,SAAS;QACT,WAAW;QACX,OAAO;IACT;IAEA,MAAM,cAAc;QAClB,IAAI;QACJ,IAAI;QACJ,IAAI;IACN;IAEA,qBACE,8OAAC;QACC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,aACA,cAAc,CAAC,QAAQ,EACvB,WAAW,CAAC,KAAK,EACjB;QAED,GAAG,KAAK;kBAER;;;;;;AAGP;uCAEe", "debugId": null}}, {"offset": {"line": 43, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/New%20folder%20%2825%29/streamzen/src/components/VideoPlayer.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState, useEffect, useRef } from 'react';\nimport { ArrowLeft, Play, RotateCcw, Settings, Maximize, Volume2, VolumeX, SkipBack, Skip<PERSON>or<PERSON>, Loader, Wifi, WifiOff, Star, Share2, Download, Bookmark } from 'lucide-react';\nimport { useRouter } from 'next/navigation';\nimport { motion, AnimatePresence } from 'framer-motion';\nimport Button from './ui/Button';\nimport { cn } from '@/lib/utils';\n\ninterface StreamingSource {\n  source: string;\n  name: string;\n  url: string;\n  quality: string;\n  priority: number;\n}\n\ninterface VideoPlayerProps {\n  streamingSources: StreamingSource[];\n  title: string;\n  type: 'movie' | 'series' | 'episode';\n}\n\nconst VideoPlayer: React.FC<VideoPlayerProps> = ({ streamingSources = [], title, type }) => {\n  const router = useRouter();\n  const [currentSourceIndex, setCurrentSourceIndex] = useState(0);\n  const [isLoading, setIsLoading] = useState(true);\n  const [showControls, setShowControls] = useState(true);\n  const [isFullscreen, setIsFullscreen] = useState(false);\n  const [isMuted, setIsMuted] = useState(false);\n  const [showSourceSelector, setShowSourceSelector] = useState(false);\n  const [connectionStatus, setConnectionStatus] = useState<'online' | 'offline' | 'slow'>('online');\n  const [loadingProgress, setLoadingProgress] = useState(0);\n  const [showShareMenu, setShowShareMenu] = useState(false);\n\n  const playerRef = useRef<HTMLDivElement>(null);\n  const iframeRef = useRef<HTMLIFrameElement>(null);\n  const controlsTimeoutRef = useRef<NodeJS.Timeout>();\n\n  const currentSource = streamingSources[currentSourceIndex];\n\n  // If no streaming sources available, show error\n  if (!streamingSources || streamingSources.length === 0) {\n    return (\n      <div className=\"relative bg-black h-[70vh] flex items-center justify-center\">\n        <div className=\"text-center text-white\">\n          <h2 className=\"text-2xl font-bold mb-4\">No Streaming Sources Available</h2>\n          <p className=\"text-gray-400 mb-6\">Unable to load streaming sources for this content.</p>\n          <Button onClick={() => router.back()} variant=\"primary\">\n            Go Back\n          </Button>\n        </div>\n      </div>\n    );\n  }\n\n  const goBack = () => {\n    router.back();\n  };\n\n  const switchSource = (index: number) => {\n    setIsLoading(true);\n    setCurrentSourceIndex(index);\n    // Reset loading state after iframe loads\n    setTimeout(() => setIsLoading(false), 2000);\n  };\n\n  const reloadCurrentSource = () => {\n    setIsLoading(true);\n    setLoadingProgress(0);\n    // Force iframe reload by changing key\n    const iframe = iframeRef.current;\n    if (iframe) {\n      iframe.src = iframe.src;\n    }\n    setTimeout(() => setIsLoading(false), 2000);\n  };\n\n  // Enhanced functionality\n  useEffect(() => {\n    // Auto-hide controls after 3 seconds of inactivity\n    const resetControlsTimeout = () => {\n      if (controlsTimeoutRef.current) {\n        clearTimeout(controlsTimeoutRef.current);\n      }\n      setShowControls(true);\n      controlsTimeoutRef.current = setTimeout(() => {\n        setShowControls(false);\n      }, 3000);\n    };\n\n    resetControlsTimeout();\n    return () => {\n      if (controlsTimeoutRef.current) {\n        clearTimeout(controlsTimeoutRef.current);\n      }\n    };\n  }, []);\n\n  // Loading progress simulation\n  useEffect(() => {\n    if (isLoading) {\n      setLoadingProgress(0);\n      const interval = setInterval(() => {\n        setLoadingProgress(prev => {\n          if (prev >= 90) {\n            clearInterval(interval);\n            return prev;\n          }\n          return prev + Math.random() * 15;\n        });\n      }, 200);\n\n      return () => clearInterval(interval);\n    }\n  }, [isLoading]);\n\n  const toggleFullscreen = () => {\n    if (!document.fullscreenElement) {\n      playerRef.current?.requestFullscreen();\n      setIsFullscreen(true);\n    } else {\n      document.exitFullscreen();\n      setIsFullscreen(false);\n    }\n  };\n\n  const handleMouseMove = () => {\n    setShowControls(true);\n    if (controlsTimeoutRef.current) {\n      clearTimeout(controlsTimeoutRef.current);\n    }\n    controlsTimeoutRef.current = setTimeout(() => {\n      setShowControls(false);\n    }, 3000);\n  };\n\n  const shareContent = () => {\n    if (navigator.share) {\n      navigator.share({\n        title: title,\n        url: window.location.href\n      });\n    } else {\n      setShowShareMenu(true);\n    }\n  };\n\n  return (\n    <div\n      ref={playerRef}\n      className=\"relative bg-gradient-to-br from-black via-gray-900 to-black min-h-screen overflow-hidden\"\n      onMouseMove={handleMouseMove}\n    >\n      {/* Animated Background Effects */}\n      <div className=\"absolute inset-0 overflow-hidden pointer-events-none\">\n        <motion.div\n          className=\"absolute -top-40 -left-40 w-80 h-80 bg-red-600/5 rounded-full blur-3xl\"\n          animate={{\n            scale: [1, 1.2, 1],\n            opacity: [0.05, 0.1, 0.05]\n          }}\n          transition={{ duration: 8, repeat: Infinity, ease: \"easeInOut\" }}\n        />\n        <motion.div\n          className=\"absolute -bottom-40 -right-40 w-80 h-80 bg-blue-600/5 rounded-full blur-3xl\"\n          animate={{\n            scale: [1.2, 1, 1.2],\n            opacity: [0.1, 0.05, 0.1]\n          }}\n          transition={{ duration: 10, repeat: Infinity, ease: \"easeInOut\" }}\n        />\n      </div>\n\n      {/* Enhanced Header Controls - Fixed positioning */}\n      <div className=\"relative w-full bg-gradient-to-b from-black/80 via-black/40 to-transparent backdrop-blur-sm\">\n            <div className=\"max-w-[2560px] mx-auto px-6 lg:px-12 py-6\">\n              <div className=\"flex items-center justify-between\">\n                <div className=\"flex items-center space-x-6\">\n                  <motion.button\n                    onClick={goBack}\n                    className=\"flex items-center space-x-3 px-4 py-2 bg-black/50 backdrop-blur-sm border border-white/20 rounded-xl text-white hover:bg-white/20 transition-all duration-300 focus-ring\"\n                    whileHover={{ scale: 1.05 }}\n                    whileTap={{ scale: 0.95 }}\n                  >\n                    <ArrowLeft size={20} />\n                    <span className=\"font-medium\">Back</span>\n                  </motion.button>\n                  <div className=\"flex flex-col\">\n                    <h1 className=\"text-white text-2xl font-bold truncate max-w-2xl\">\n                      {title}\n                    </h1>\n                    <div className=\"flex items-center space-x-3 text-sm\">\n                      <p className=\"text-gray-400\">Now Playing</p>\n                      <div className=\"flex items-center space-x-1\">\n                        {connectionStatus === 'online' ? (\n                          <Wifi size={14} className=\"text-green-400\" />\n                        ) : connectionStatus === 'slow' ? (\n                          <Wifi size={14} className=\"text-yellow-400\" />\n                        ) : (\n                          <WifiOff size={14} className=\"text-red-400\" />\n                        )}\n                        <span className={cn(\n                          \"text-xs font-medium\",\n                          connectionStatus === 'online' ? 'text-green-400' :\n                          connectionStatus === 'slow' ? 'text-yellow-400' : 'text-red-400'\n                        )}>\n                          {connectionStatus === 'online' ? 'HD' : connectionStatus === 'slow' ? 'SD' : 'Offline'}\n                        </span>\n                      </div>\n                    </div>\n                  </div>\n                </div>\n\n                <div className=\"flex items-center space-x-3\">\n                  {/* Source Selector */}\n                  <div className=\"relative\">\n                    <motion.button\n                      onClick={() => setShowSourceSelector(!showSourceSelector)}\n                      className=\"flex items-center space-x-2 px-4 py-2 bg-black/50 backdrop-blur-sm border border-white/20 rounded-xl text-white hover:bg-white/20 transition-all duration-300 focus-ring\"\n                      whileHover={{ scale: 1.05 }}\n                      whileTap={{ scale: 0.95 }}\n                    >\n                      <Settings size={18} />\n                      <span className=\"font-medium\">Source {currentSourceIndex + 1}</span>\n                    </motion.button>\n\n                    <AnimatePresence>\n                      {showSourceSelector && (\n                        <motion.div\n                          initial={{ opacity: 0, y: 10, scale: 0.95 }}\n                          animate={{ opacity: 1, y: 0, scale: 1 }}\n                          exit={{ opacity: 0, y: 10, scale: 0.95 }}\n                          className=\"absolute top-full right-0 mt-2 w-64 bg-black/90 backdrop-blur-xl border border-white/20 rounded-xl shadow-2xl overflow-hidden z-60\"\n                        >\n                          <div className=\"p-3 border-b border-white/10\">\n                            <h3 className=\"text-white font-semibold\">Streaming Sources</h3>\n                          </div>\n                          <div className=\"max-h-64 overflow-y-auto\">\n                            {streamingSources.map((source, index) => (\n                              <motion.button\n                                key={index}\n                                onClick={() => switchSource(index)}\n                                className={cn(\n                                  \"w-full flex items-center justify-between px-4 py-3 text-left transition-all duration-200\",\n                                  index === currentSourceIndex\n                                    ? \"bg-red-500/20 text-white border-l-2 border-red-500\"\n                                    : \"text-gray-300 hover:text-white hover:bg-white/10\"\n                                )}\n                                whileHover={{ x: 4 }}\n                              >\n                                <div>\n                                  <span className=\"font-medium\">{source.name}</span>\n                                  <p className=\"text-xs text-gray-400\">{source.quality}</p>\n                                </div>\n                                {index === currentSourceIndex && (\n                                  <div className=\"w-2 h-2 bg-red-500 rounded-full animate-pulse\" />\n                                )}\n                              </motion.button>\n                            ))}\n                          </div>\n                        </motion.div>\n                      )}\n                    </AnimatePresence>\n                  </div>\n\n                  {/* Action Buttons */}\n                  <motion.button\n                    onClick={shareContent}\n                    className=\"p-2 bg-black/50 backdrop-blur-sm border border-white/20 rounded-xl text-white hover:bg-white/20 transition-all duration-300 focus-ring\"\n                    whileHover={{ scale: 1.05 }}\n                    whileTap={{ scale: 0.95 }}\n                  >\n                    <Share2 size={18} />\n                  </motion.button>\n\n                  <motion.button\n                    onClick={reloadCurrentSource}\n                    className=\"flex items-center space-x-2 px-4 py-2 bg-black/50 backdrop-blur-sm border border-white/20 rounded-xl text-white hover:bg-white/20 transition-all duration-300 focus-ring\"\n                    whileHover={{ scale: 1.05 }}\n                    whileTap={{ scale: 0.95 }}\n                  >\n                    <RotateCcw size={18} />\n                    <span className=\"font-medium\">Reload</span>\n                  </motion.button>\n                </div>\n              </div>\n        </div>\n      </div>\n\n      {/* Enhanced Video Player Container */}\n      <div className=\"relative w-full h-[75vh] lg:h-[85vh] mx-auto max-w-[2560px] px-6 lg:px-12\">\n        <motion.div\n          className=\"relative w-full h-full bg-black rounded-3xl overflow-hidden shadow-2xl border border-white/20\"\n          initial={{ scale: 0.95, opacity: 0 }}\n          animate={{ scale: 1, opacity: 1 }}\n          transition={{ duration: 0.5 }}\n        >\n          {/* Enhanced Loading Overlay */}\n          <AnimatePresence>\n            {isLoading && (\n              <motion.div\n                initial={{ opacity: 0 }}\n                animate={{ opacity: 1 }}\n                exit={{ opacity: 0 }}\n                className=\"absolute inset-0 bg-gradient-to-br from-black/90 via-black/80 to-black/90 backdrop-blur-sm flex items-center justify-center z-30\"\n              >\n                <div className=\"text-center text-white\">\n                  <motion.div\n                    animate={{ rotate: 360 }}\n                    transition={{ duration: 2, repeat: Infinity, ease: \"linear\" }}\n                    className=\"w-20 h-20 border-4 border-gray-600 border-t-red-500 rounded-full mx-auto mb-6\"\n                  />\n                  <motion.div\n                    initial={{ y: 20, opacity: 0 }}\n                    animate={{ y: 0, opacity: 1 }}\n                    transition={{ delay: 0.2 }}\n                  >\n                    <p className=\"text-xl font-semibold mb-2\">Loading {currentSource?.name}...</p>\n                    <p className=\"text-sm text-gray-400 mb-4\">Quality: {currentSource?.quality}</p>\n\n                    {/* Loading Progress Bar */}\n                    <div className=\"w-64 h-2 bg-gray-700 rounded-full overflow-hidden mx-auto\">\n                      <motion.div\n                        className=\"h-full bg-gradient-to-r from-red-500 to-red-600 rounded-full\"\n                        initial={{ width: 0 }}\n                        animate={{ width: `${loadingProgress}%` }}\n                        transition={{ duration: 0.3 }}\n                      />\n                    </div>\n                    <p className=\"text-xs text-gray-500 mt-2\">{Math.round(loadingProgress)}% loaded</p>\n                  </motion.div>\n                </div>\n              </motion.div>\n            )}\n          </AnimatePresence>\n\n          {/* Video iframe */}\n          {currentSource && (\n            <iframe\n              ref={iframeRef}\n              key={`${currentSource.source}-${currentSourceIndex}`}\n              src={currentSource.url}\n              className=\"w-full h-full border-0\"\n              allowFullScreen\n              allow=\"accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share\"\n              title={title}\n              onLoad={() => {\n                setIsLoading(false);\n                setLoadingProgress(100);\n              }}\n            />\n          )}\n\n\n        </motion.div>\n      </div>\n\n      {/* Premium Source Selector */}\n      <div className=\"glass border-t border-white/5\">\n        <div className=\"max-w-[1920px] mx-auto px-6 lg:px-12 py-8\">\n          <div className=\"flex items-center justify-between mb-8\">\n            <div className=\"flex items-center space-x-4\">\n              <h3 className=\"text-white text-2xl font-bold\">Streaming Sources</h3>\n              <div className=\"glass px-4 py-2 rounded-full\">\n                <span className=\"text-gray-300 text-sm font-medium\">\n                  {streamingSources.length} Available\n                </span>\n              </div>\n            </div>\n            <div className=\"glass-elevated px-6 py-3 rounded-xl\">\n              <div className=\"flex items-center space-x-3\">\n                <div className=\"w-3 h-3 bg-green-400 rounded-full animate-pulse\" />\n                <span className=\"text-white font-medium\">\n                  {currentSource?.name} • {currentSource?.quality}\n                </span>\n              </div>\n            </div>\n          </div>\n\n          <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-6 gap-4\">\n            {streamingSources.map((source, index) => (\n              <button\n                key={source.source}\n                onClick={() => switchSource(index)}\n                className={cn(\n                  'group relative p-6 rounded-2xl border transition-all duration-300 text-left hover:scale-105 focus-ring',\n                  index === currentSourceIndex\n                    ? 'bg-gradient-to-br from-blue-600 to-blue-700 border-blue-500 text-white shadow-2xl'\n                    : 'glass-elevated border-white/10 text-gray-300 hover:bg-white/10 hover:border-white/20'\n                )}\n              >\n                <div className=\"flex items-center space-x-3 mb-3\">\n                  <div className={cn(\n                    'w-10 h-10 rounded-full flex items-center justify-center transition-all duration-300',\n                    index === currentSourceIndex\n                      ? 'bg-white/20'\n                      : 'bg-white/10 group-hover:bg-white/20'\n                  )}>\n                    <Play size={16} className={index === currentSourceIndex ? 'text-white' : 'text-gray-400'} />\n                  </div>\n                  <div className=\"flex-1\">\n                    <h4 className=\"font-bold text-sm\">{source.name}</h4>\n                    <p className=\"text-xs opacity-75\">{source.quality}</p>\n                  </div>\n                </div>\n\n                {/* Priority indicator */}\n                <div className=\"flex items-center justify-between\">\n                  <div className=\"flex space-x-1\">\n                    {Array.from({ length: 5 }).map((_, i) => (\n                      <div\n                        key={i}\n                        className={cn(\n                          'w-1.5 h-1.5 rounded-full',\n                          i < (6 - source.priority)\n                            ? (index === currentSourceIndex ? 'bg-white/60' : 'bg-blue-400/60')\n                            : 'bg-white/20'\n                        )}\n                      />\n                    ))}\n                  </div>\n                  {index === currentSourceIndex && (\n                    <div className=\"text-xs font-medium bg-white/20 px-2 py-1 rounded-full\">\n                      Active\n                    </div>\n                  )}\n                </div>\n\n                {/* Glow effect for active source */}\n                {index === currentSourceIndex && (\n                  <div className=\"absolute -inset-1 bg-gradient-to-r from-blue-500/50 to-purple-500/50 rounded-2xl blur-xl -z-10\" />\n                )}\n              </button>\n            ))}\n          </div>\n\n          <div className=\"mt-8 text-center\">\n            <div className=\"glass-elevated inline-flex items-center space-x-3 px-6 py-4 rounded-2xl\">\n              <div className=\"w-2 h-2 bg-blue-400 rounded-full animate-pulse\" />\n              <p className=\"text-gray-300 text-sm font-medium\">\n                Premium sources are automatically optimized for the best viewing experience\n              </p>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default VideoPlayer;\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AAAA;AACA;AACA;AAPA;;;;;;;;AAuBA,MAAM,cAA0C,CAAC,EAAE,mBAAmB,EAAE,EAAE,KAAK,EAAE,IAAI,EAAE;IACrF,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,CAAC,oBAAoB,sBAAsB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7D,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,oBAAoB,sBAAsB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7D,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiC;IACxF,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvD,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEnD,MAAM,YAAY,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAkB;IACzC,MAAM,YAAY,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAqB;IAC5C,MAAM,qBAAqB,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD;IAEhC,MAAM,gBAAgB,gBAAgB,CAAC,mBAAmB;IAE1D,gDAAgD;IAChD,IAAI,CAAC,oBAAoB,iBAAiB,MAAM,KAAK,GAAG;QACtD,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;kCAA0B;;;;;;kCACxC,8OAAC;wBAAE,WAAU;kCAAqB;;;;;;kCAClC,8OAAC,kIAAA,CAAA,UAAM;wBAAC,SAAS,IAAM,OAAO,IAAI;wBAAI,SAAQ;kCAAU;;;;;;;;;;;;;;;;;IAMhE;IAEA,MAAM,SAAS;QACb,OAAO,IAAI;IACb;IAEA,MAAM,eAAe,CAAC;QACpB,aAAa;QACb,sBAAsB;QACtB,yCAAyC;QACzC,WAAW,IAAM,aAAa,QAAQ;IACxC;IAEA,MAAM,sBAAsB;QAC1B,aAAa;QACb,mBAAmB;QACnB,sCAAsC;QACtC,MAAM,SAAS,UAAU,OAAO;QAChC,IAAI,QAAQ;YACV,OAAO,GAAG,GAAG,OAAO,GAAG;QACzB;QACA,WAAW,IAAM,aAAa,QAAQ;IACxC;IAEA,yBAAyB;IACzB,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,mDAAmD;QACnD,MAAM,uBAAuB;YAC3B,IAAI,mBAAmB,OAAO,EAAE;gBAC9B,aAAa,mBAAmB,OAAO;YACzC;YACA,gBAAgB;YAChB,mBAAmB,OAAO,GAAG,WAAW;gBACtC,gBAAgB;YAClB,GAAG;QACL;QAEA;QACA,OAAO;YACL,IAAI,mBAAmB,OAAO,EAAE;gBAC9B,aAAa,mBAAmB,OAAO;YACzC;QACF;IACF,GAAG,EAAE;IAEL,8BAA8B;IAC9B,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,WAAW;YACb,mBAAmB;YACnB,MAAM,WAAW,YAAY;gBAC3B,mBAAmB,CAAA;oBACjB,IAAI,QAAQ,IAAI;wBACd,cAAc;wBACd,OAAO;oBACT;oBACA,OAAO,OAAO,KAAK,MAAM,KAAK;gBAChC;YACF,GAAG;YAEH,OAAO,IAAM,cAAc;QAC7B;IACF,GAAG;QAAC;KAAU;IAEd,MAAM,mBAAmB;QACvB,IAAI,CAAC,SAAS,iBAAiB,EAAE;YAC/B,UAAU,OAAO,EAAE;YACnB,gBAAgB;QAClB,OAAO;YACL,SAAS,cAAc;YACvB,gBAAgB;QAClB;IACF;IAEA,MAAM,kBAAkB;QACtB,gBAAgB;QAChB,IAAI,mBAAmB,OAAO,EAAE;YAC9B,aAAa,mBAAmB,OAAO;QACzC;QACA,mBAAmB,OAAO,GAAG,WAAW;YACtC,gBAAgB;QAClB,GAAG;IACL;IAEA,MAAM,eAAe;QACnB,IAAI,UAAU,KAAK,EAAE;YACnB,UAAU,KAAK,CAAC;gBACd,OAAO;gBACP,KAAK,OAAO,QAAQ,CAAC,IAAI;YAC3B;QACF,OAAO;YACL,iBAAiB;QACnB;IACF;IAEA,qBACE,8OAAC;QACC,KAAK;QACL,WAAU;QACV,aAAa;;0BAGb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,WAAU;wBACV,SAAS;4BACP,OAAO;gCAAC;gCAAG;gCAAK;6BAAE;4BAClB,SAAS;gCAAC;gCAAM;gCAAK;6BAAK;wBAC5B;wBACA,YAAY;4BAAE,UAAU;4BAAG,QAAQ;4BAAU,MAAM;wBAAY;;;;;;kCAEjE,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,WAAU;wBACV,SAAS;4BACP,OAAO;gCAAC;gCAAK;gCAAG;6BAAI;4BACpB,SAAS;gCAAC;gCAAK;gCAAM;6BAAI;wBAC3B;wBACA,YAAY;4BAAE,UAAU;4BAAI,QAAQ;4BAAU,MAAM;wBAAY;;;;;;;;;;;;0BAKpE,8OAAC;gBAAI,WAAU;0BACT,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,0LAAA,CAAA,SAAM,CAAC,MAAM;wCACZ,SAAS;wCACT,WAAU;wCACV,YAAY;4CAAE,OAAO;wCAAK;wCAC1B,UAAU;4CAAE,OAAO;wCAAK;;0DAExB,8OAAC,gNAAA,CAAA,YAAS;gDAAC,MAAM;;;;;;0DACjB,8OAAC;gDAAK,WAAU;0DAAc;;;;;;;;;;;;kDAEhC,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAG,WAAU;0DACX;;;;;;0DAEH,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAE,WAAU;kEAAgB;;;;;;kEAC7B,8OAAC;wDAAI,WAAU;;4DACZ,qBAAqB,yBACpB,8OAAC,kMAAA,CAAA,OAAI;gEAAC,MAAM;gEAAI,WAAU;;;;;uEACxB,qBAAqB,uBACvB,8OAAC,kMAAA,CAAA,OAAI;gEAAC,MAAM;gEAAI,WAAU;;;;;qFAE1B,8OAAC,4MAAA,CAAA,UAAO;gEAAC,MAAM;gEAAI,WAAU;;;;;;0EAE/B,8OAAC;gEAAK,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAChB,uBACA,qBAAqB,WAAW,mBAChC,qBAAqB,SAAS,oBAAoB;0EAEjD,qBAAqB,WAAW,OAAO,qBAAqB,SAAS,OAAO;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0CAOvF,8OAAC;gCAAI,WAAU;;kDAEb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,0LAAA,CAAA,SAAM,CAAC,MAAM;gDACZ,SAAS,IAAM,sBAAsB,CAAC;gDACtC,WAAU;gDACV,YAAY;oDAAE,OAAO;gDAAK;gDAC1B,UAAU;oDAAE,OAAO;gDAAK;;kEAExB,8OAAC,0MAAA,CAAA,WAAQ;wDAAC,MAAM;;;;;;kEAChB,8OAAC;wDAAK,WAAU;;4DAAc;4DAAQ,qBAAqB;;;;;;;;;;;;;0DAG7D,8OAAC,yLAAA,CAAA,kBAAe;0DACb,oCACC,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oDACT,SAAS;wDAAE,SAAS;wDAAG,GAAG;wDAAI,OAAO;oDAAK;oDAC1C,SAAS;wDAAE,SAAS;wDAAG,GAAG;wDAAG,OAAO;oDAAE;oDACtC,MAAM;wDAAE,SAAS;wDAAG,GAAG;wDAAI,OAAO;oDAAK;oDACvC,WAAU;;sEAEV,8OAAC;4DAAI,WAAU;sEACb,cAAA,8OAAC;gEAAG,WAAU;0EAA2B;;;;;;;;;;;sEAE3C,8OAAC;4DAAI,WAAU;sEACZ,iBAAiB,GAAG,CAAC,CAAC,QAAQ,sBAC7B,8OAAC,0LAAA,CAAA,SAAM,CAAC,MAAM;oEAEZ,SAAS,IAAM,aAAa;oEAC5B,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,4FACA,UAAU,qBACN,uDACA;oEAEN,YAAY;wEAAE,GAAG;oEAAE;;sFAEnB,8OAAC;;8FACC,8OAAC;oFAAK,WAAU;8FAAe,OAAO,IAAI;;;;;;8FAC1C,8OAAC;oFAAE,WAAU;8FAAyB,OAAO,OAAO;;;;;;;;;;;;wEAErD,UAAU,oCACT,8OAAC;4EAAI,WAAU;;;;;;;mEAfZ;;;;;;;;;;;;;;;;;;;;;;;;;;;kDA0BnB,8OAAC,0LAAA,CAAA,SAAM,CAAC,MAAM;wCACZ,SAAS;wCACT,WAAU;wCACV,YAAY;4CAAE,OAAO;wCAAK;wCAC1B,UAAU;4CAAE,OAAO;wCAAK;kDAExB,cAAA,8OAAC,0MAAA,CAAA,SAAM;4CAAC,MAAM;;;;;;;;;;;kDAGhB,8OAAC,0LAAA,CAAA,SAAM,CAAC,MAAM;wCACZ,SAAS;wCACT,WAAU;wCACV,YAAY;4CAAE,OAAO;wCAAK;wCAC1B,UAAU;4CAAE,OAAO;wCAAK;;0DAExB,8OAAC,gNAAA,CAAA,YAAS;gDAAC,MAAM;;;;;;0DACjB,8OAAC;gDAAK,WAAU;0DAAc;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAQ5C,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,WAAU;oBACV,SAAS;wBAAE,OAAO;wBAAM,SAAS;oBAAE;oBACnC,SAAS;wBAAE,OAAO;wBAAG,SAAS;oBAAE;oBAChC,YAAY;wBAAE,UAAU;oBAAI;;sCAG5B,8OAAC,yLAAA,CAAA,kBAAe;sCACb,2BACC,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gCACT,SAAS;oCAAE,SAAS;gCAAE;gCACtB,SAAS;oCAAE,SAAS;gCAAE;gCACtB,MAAM;oCAAE,SAAS;gCAAE;gCACnB,WAAU;0CAEV,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4CACT,SAAS;gDAAE,QAAQ;4CAAI;4CACvB,YAAY;gDAAE,UAAU;gDAAG,QAAQ;gDAAU,MAAM;4CAAS;4CAC5D,WAAU;;;;;;sDAEZ,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4CACT,SAAS;gDAAE,GAAG;gDAAI,SAAS;4CAAE;4CAC7B,SAAS;gDAAE,GAAG;gDAAG,SAAS;4CAAE;4CAC5B,YAAY;gDAAE,OAAO;4CAAI;;8DAEzB,8OAAC;oDAAE,WAAU;;wDAA6B;wDAAS,eAAe;wDAAK;;;;;;;8DACvE,8OAAC;oDAAE,WAAU;;wDAA6B;wDAAU,eAAe;;;;;;;8DAGnE,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wDACT,WAAU;wDACV,SAAS;4DAAE,OAAO;wDAAE;wDACpB,SAAS;4DAAE,OAAO,GAAG,gBAAgB,CAAC,CAAC;wDAAC;wDACxC,YAAY;4DAAE,UAAU;wDAAI;;;;;;;;;;;8DAGhC,8OAAC;oDAAE,WAAU;;wDAA8B,KAAK,KAAK,CAAC;wDAAiB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;wBAQhF,+BACC,8OAAC;4BACC,KAAK;4BAEL,KAAK,cAAc,GAAG;4BACtB,WAAU;4BACV,eAAe;4BACf,OAAM;4BACN,OAAO;4BACP,QAAQ;gCACN,aAAa;gCACb,mBAAmB;4BACrB;2BATK,GAAG,cAAc,MAAM,CAAC,CAAC,EAAE,oBAAoB;;;;;;;;;;;;;;;;0BAkB5D,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAG,WAAU;sDAAgC;;;;;;sDAC9C,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAK,WAAU;;oDACb,iBAAiB,MAAM;oDAAC;;;;;;;;;;;;;;;;;;8CAI/B,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;;;;;;0DACf,8OAAC;gDAAK,WAAU;;oDACb,eAAe;oDAAK;oDAAI,eAAe;;;;;;;;;;;;;;;;;;;;;;;;sCAMhD,8OAAC;4BAAI,WAAU;sCACZ,iBAAiB,GAAG,CAAC,CAAC,QAAQ,sBAC7B,8OAAC;oCAEC,SAAS,IAAM,aAAa;oCAC5B,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,0GACA,UAAU,qBACN,sFACA;;sDAGN,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACf,uFACA,UAAU,qBACN,gBACA;8DAEJ,cAAA,8OAAC,kMAAA,CAAA,OAAI;wDAAC,MAAM;wDAAI,WAAW,UAAU,qBAAqB,eAAe;;;;;;;;;;;8DAE3E,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAG,WAAU;sEAAqB,OAAO,IAAI;;;;;;sEAC9C,8OAAC;4DAAE,WAAU;sEAAsB,OAAO,OAAO;;;;;;;;;;;;;;;;;;sDAKrD,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;8DACZ,MAAM,IAAI,CAAC;wDAAE,QAAQ;oDAAE,GAAG,GAAG,CAAC,CAAC,GAAG,kBACjC,8OAAC;4DAEC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,4BACA,IAAK,IAAI,OAAO,QAAQ,GACnB,UAAU,qBAAqB,gBAAgB,mBAChD;2DALD;;;;;;;;;;gDAUV,UAAU,oCACT,8OAAC;oDAAI,WAAU;8DAAyD;;;;;;;;;;;;wCAO3E,UAAU,oCACT,8OAAC;4CAAI,WAAU;;;;;;;mCAhDZ,OAAO,MAAM;;;;;;;;;;sCAsDxB,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;;;;;kDACf,8OAAC;wCAAE,WAAU;kDAAoC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAS/D;uCAEe", "debugId": null}}, {"offset": {"line": 1020, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/New%20folder%20%2825%29/streamzen/src/components/ContentInfo.tsx"], "sourcesContent": ["'use client';\n\nimport React from 'react';\nimport Image from 'next/image';\nimport { Star, Calendar, Clock, User, Users, Play, Heart, Share2, Bookmark, Award, Globe, Sparkles, TrendingUp } from 'lucide-react';\nimport { motion } from 'framer-motion';\nimport { getImageUrl, formatRating, cn } from '@/lib/utils';\n\ninterface ContentInfoProps {\n  content: {\n    title: string;\n    year?: number;\n    rating?: string;\n    runtime?: string;\n    imdbRating?: number;\n    description?: string;\n    genres?: string[];\n    director?: string;\n    cast?: string[];\n    language?: string;\n    country?: string;\n    posterUrl?: string;\n    imdbVotes?: string;\n    popularity?: number;\n  };\n}\n\nconst ContentInfo: React.FC<ContentInfoProps> = ({ content }) => {\n  return (\n    <div className=\"bg-gradient-to-br from-gray-900/50 via-black/50 to-gray-900/50 backdrop-blur-xl rounded-3xl overflow-hidden border border-white/10 shadow-2xl\">\n      <motion.div\n        initial={{ opacity: 0, y: 20 }}\n        animate={{ opacity: 1, y: 0 }}\n        transition={{ duration: 0.6 }}\n        className=\"grid grid-cols-1 lg:grid-cols-4 gap-8 p-8\"\n      >\n        {/* Enhanced Poster */}\n        <motion.div\n          className=\"lg:col-span-1\"\n          initial={{ opacity: 0, x: -20 }}\n          animate={{ opacity: 1, x: 0 }}\n          transition={{ duration: 0.6, delay: 0.2 }}\n        >\n          <div className=\"relative aspect-[2/3] rounded-2xl overflow-hidden bg-gradient-to-br from-gray-800 to-gray-900 border border-white/20 shadow-2xl group\">\n            <Image\n              src={getImageUrl(content.posterUrl)}\n              alt={content.title}\n              fill\n              className=\"object-cover transition-transform duration-500 group-hover:scale-105\"\n              sizes=\"(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 25vw\"\n            />\n            \n            {/* Gradient overlay */}\n            <div className=\"absolute inset-0 bg-gradient-to-t from-black/60 via-transparent to-transparent\" />\n            \n            {/* Play button overlay */}\n            <div className=\"absolute inset-0 flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity duration-300\">\n              <div className=\"w-16 h-16 bg-red-500 rounded-full flex items-center justify-center shadow-2xl\">\n                <Play className=\"w-6 h-6 text-white fill-current ml-1\" />\n              </div>\n            </div>\n\n            {/* Rating badge */}\n            {content.imdbRating && (\n              <div className=\"absolute top-4 right-4 flex items-center space-x-1 px-3 py-1.5 bg-black/70 backdrop-blur-sm rounded-full\">\n                <Star className=\"w-4 h-4 text-yellow-400 fill-current\" />\n                <span className=\"text-white font-bold text-sm\">{formatRating(content.imdbRating)}</span>\n              </div>\n            )}\n          </div>\n        </motion.div>\n\n        {/* Content Details */}\n        <motion.div\n          className=\"lg:col-span-3 space-y-6\"\n          initial={{ opacity: 0, x: 20 }}\n          animate={{ opacity: 1, x: 0 }}\n          transition={{ duration: 0.6, delay: 0.3 }}\n        >\n          {/* Title and Year */}\n          <div className=\"space-y-2\">\n            <h1 className=\"text-4xl lg:text-5xl font-black text-white leading-tight\">\n              {content.title}\n            </h1>\n            {content.year && (\n              <p className=\"text-xl text-gray-300 font-medium\">{content.year}</p>\n            )}\n          </div>\n\n          {/* Meta Information */}\n          <div className=\"flex flex-wrap items-center gap-4 text-gray-300\">\n            {content.rating && (\n              <span className=\"px-3 py-1 bg-white/10 rounded-full text-sm font-semibold\">\n                {content.rating}\n              </span>\n            )}\n            {content.runtime && (\n              <div className=\"flex items-center space-x-1\">\n                <Clock className=\"w-4 h-4\" />\n                <span className=\"text-sm font-medium\">{content.runtime}</span>\n              </div>\n            )}\n            {content.language && (\n              <div className=\"flex items-center space-x-1\">\n                <Globe className=\"w-4 h-4\" />\n                <span className=\"text-sm font-medium\">{content.language}</span>\n              </div>\n            )}\n          </div>\n\n          {/* Genres */}\n          {content.genres && content.genres.length > 0 && (\n            <div className=\"flex flex-wrap gap-2\">\n              {content.genres.slice(0, 5).map((genre, index) => (\n                <span\n                  key={index}\n                  className=\"px-3 py-1 bg-gradient-to-r from-blue-500/20 to-purple-500/20 border border-blue-500/30 rounded-full text-sm font-medium text-blue-300\"\n                >\n                  {genre}\n                </span>\n              ))}\n            </div>\n          )}\n\n          {/* Description */}\n          {content.description && (\n            <div className=\"space-y-2\">\n              <h3 className=\"text-lg font-bold text-white\">Overview</h3>\n              <p className=\"text-gray-300 leading-relaxed text-base\">\n                {content.description}\n              </p>\n            </div>\n          )}\n\n          {/* Cast and Crew */}\n          <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\n            {content.director && (\n              <div className=\"space-y-2\">\n                <h4 className=\"text-sm font-bold text-white uppercase tracking-wider\">Director</h4>\n                <div className=\"flex items-center space-x-2\">\n                  <User className=\"w-4 h-4 text-gray-400\" />\n                  <span className=\"text-gray-300 font-medium\">{content.director}</span>\n                </div>\n              </div>\n            )}\n\n            {content.cast && content.cast.length > 0 && (\n              <div className=\"space-y-2\">\n                <h4 className=\"text-sm font-bold text-white uppercase tracking-wider\">Cast</h4>\n                <div className=\"flex items-start space-x-2\">\n                  <Users className=\"w-4 h-4 text-gray-400 mt-0.5\" />\n                  <div className=\"flex flex-wrap gap-1\">\n                    {content.cast.slice(0, 4).map((actor, index) => (\n                      <span key={index} className=\"text-gray-300 font-medium\">\n                        {actor}{index < Math.min(content.cast!.length - 1, 3) ? ',' : ''}\n                      </span>\n                    ))}\n                    {content.cast.length > 4 && (\n                      <span className=\"text-gray-400\">+{content.cast.length - 4} more</span>\n                    )}\n                  </div>\n                </div>\n              </div>\n            )}\n          </div>\n\n          {/* Additional Stats */}\n          <div className=\"flex flex-wrap items-center gap-6 pt-4 border-t border-white/10\">\n            {content.imdbRating && (\n              <div className=\"flex items-center space-x-2\">\n                <div className=\"flex items-center space-x-1 px-3 py-1.5 bg-yellow-500/20 rounded-full\">\n                  <Star className=\"w-4 h-4 text-yellow-400 fill-current\" />\n                  <span className=\"text-yellow-400 font-bold\">{formatRating(content.imdbRating)}</span>\n                </div>\n                {content.imdbVotes && (\n                  <span className=\"text-gray-400 text-sm\">({content.imdbVotes} votes)</span>\n                )}\n              </div>\n            )}\n\n            {content.popularity && (\n              <div className=\"flex items-center space-x-2\">\n                <TrendingUp className=\"w-4 h-4 text-green-400\" />\n                <span className=\"text-green-400 font-medium text-sm\">\n                  {Math.round(content.popularity)} popularity\n                </span>\n              </div>\n            )}\n          </div>\n\n          {/* Action Buttons */}\n          <div className=\"flex flex-wrap items-center gap-4 pt-4\">\n            <button className=\"flex items-center space-x-2 px-4 py-2 bg-red-500 hover:bg-red-600 text-white font-semibold rounded-xl transition-colors duration-200\">\n              <Play className=\"w-4 h-4 fill-current\" />\n              <span>Watch Now</span>\n            </button>\n            \n            <button className=\"flex items-center space-x-2 px-4 py-2 bg-white/10 hover:bg-white/20 text-white font-semibold rounded-xl transition-colors duration-200\">\n              <Bookmark className=\"w-4 h-4\" />\n              <span>Watchlist</span>\n            </button>\n            \n            <button className=\"flex items-center space-x-2 px-4 py-2 bg-white/10 hover:bg-white/20 text-white font-semibold rounded-xl transition-colors duration-200\">\n              <Heart className=\"w-4 h-4\" />\n              <span>Like</span>\n            </button>\n            \n            <button className=\"flex items-center space-x-2 px-4 py-2 bg-white/10 hover:bg-white/20 text-white font-semibold rounded-xl transition-colors duration-200\">\n              <Share2 className=\"w-4 h-4\" />\n              <span>Share</span>\n            </button>\n          </div>\n        </motion.div>\n      </motion.div>\n    </div>\n  );\n};\n\nexport default ContentInfo;\n"], "names": [], "mappings": ";;;;AAGA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AANA;;;;;;AA2BA,MAAM,cAA0C,CAAC,EAAE,OAAO,EAAE;IAC1D,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;YACT,SAAS;gBAAE,SAAS;gBAAG,GAAG;YAAG;YAC7B,SAAS;gBAAE,SAAS;gBAAG,GAAG;YAAE;YAC5B,YAAY;gBAAE,UAAU;YAAI;YAC5B,WAAU;;8BAGV,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,WAAU;oBACV,SAAS;wBAAE,SAAS;wBAAG,GAAG,CAAC;oBAAG;oBAC9B,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAE;oBAC5B,YAAY;wBAAE,UAAU;wBAAK,OAAO;oBAAI;8BAExC,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,6HAAA,CAAA,UAAK;gCACJ,KAAK,CAAA,GAAA,mHAAA,CAAA,cAAW,AAAD,EAAE,QAAQ,SAAS;gCAClC,KAAK,QAAQ,KAAK;gCAClB,IAAI;gCACJ,WAAU;gCACV,OAAM;;;;;;0CAIR,8OAAC;gCAAI,WAAU;;;;;;0CAGf,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC,kMAAA,CAAA,OAAI;wCAAC,WAAU;;;;;;;;;;;;;;;;4BAKnB,QAAQ,UAAU,kBACjB,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,kMAAA,CAAA,OAAI;wCAAC,WAAU;;;;;;kDAChB,8OAAC;wCAAK,WAAU;kDAAgC,CAAA,GAAA,mHAAA,CAAA,eAAY,AAAD,EAAE,QAAQ,UAAU;;;;;;;;;;;;;;;;;;;;;;;8BAOvF,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,WAAU;oBACV,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC7B,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAE;oBAC5B,YAAY;wBAAE,UAAU;wBAAK,OAAO;oBAAI;;sCAGxC,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CACX,QAAQ,KAAK;;;;;;gCAEf,QAAQ,IAAI,kBACX,8OAAC;oCAAE,WAAU;8CAAqC,QAAQ,IAAI;;;;;;;;;;;;sCAKlE,8OAAC;4BAAI,WAAU;;gCACZ,QAAQ,MAAM,kBACb,8OAAC;oCAAK,WAAU;8CACb,QAAQ,MAAM;;;;;;gCAGlB,QAAQ,OAAO,kBACd,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,oMAAA,CAAA,QAAK;4CAAC,WAAU;;;;;;sDACjB,8OAAC;4CAAK,WAAU;sDAAuB,QAAQ,OAAO;;;;;;;;;;;;gCAGzD,QAAQ,QAAQ,kBACf,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,oMAAA,CAAA,QAAK;4CAAC,WAAU;;;;;;sDACjB,8OAAC;4CAAK,WAAU;sDAAuB,QAAQ,QAAQ;;;;;;;;;;;;;;;;;;wBAM5D,QAAQ,MAAM,IAAI,QAAQ,MAAM,CAAC,MAAM,GAAG,mBACzC,8OAAC;4BAAI,WAAU;sCACZ,QAAQ,MAAM,CAAC,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,OAAO,sBACtC,8OAAC;oCAEC,WAAU;8CAET;mCAHI;;;;;;;;;;wBAUZ,QAAQ,WAAW,kBAClB,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAA+B;;;;;;8CAC7C,8OAAC;oCAAE,WAAU;8CACV,QAAQ,WAAW;;;;;;;;;;;;sCAM1B,8OAAC;4BAAI,WAAU;;gCACZ,QAAQ,QAAQ,kBACf,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAG,WAAU;sDAAwD;;;;;;sDACtE,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,kMAAA,CAAA,OAAI;oDAAC,WAAU;;;;;;8DAChB,8OAAC;oDAAK,WAAU;8DAA6B,QAAQ,QAAQ;;;;;;;;;;;;;;;;;;gCAKlE,QAAQ,IAAI,IAAI,QAAQ,IAAI,CAAC,MAAM,GAAG,mBACrC,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAG,WAAU;sDAAwD;;;;;;sDACtE,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,oMAAA,CAAA,QAAK;oDAAC,WAAU;;;;;;8DACjB,8OAAC;oDAAI,WAAU;;wDACZ,QAAQ,IAAI,CAAC,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,OAAO,sBACpC,8OAAC;gEAAiB,WAAU;;oEACzB;oEAAO,QAAQ,KAAK,GAAG,CAAC,QAAQ,IAAI,CAAE,MAAM,GAAG,GAAG,KAAK,MAAM;;+DADrD;;;;;wDAIZ,QAAQ,IAAI,CAAC,MAAM,GAAG,mBACrB,8OAAC;4DAAK,WAAU;;gEAAgB;gEAAE,QAAQ,IAAI,CAAC,MAAM,GAAG;gEAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAStE,8OAAC;4BAAI,WAAU;;gCACZ,QAAQ,UAAU,kBACjB,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,kMAAA,CAAA,OAAI;oDAAC,WAAU;;;;;;8DAChB,8OAAC;oDAAK,WAAU;8DAA6B,CAAA,GAAA,mHAAA,CAAA,eAAY,AAAD,EAAE,QAAQ,UAAU;;;;;;;;;;;;wCAE7E,QAAQ,SAAS,kBAChB,8OAAC;4CAAK,WAAU;;gDAAwB;gDAAE,QAAQ,SAAS;gDAAC;;;;;;;;;;;;;gCAKjE,QAAQ,UAAU,kBACjB,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,kNAAA,CAAA,aAAU;4CAAC,WAAU;;;;;;sDACtB,8OAAC;4CAAK,WAAU;;gDACb,KAAK,KAAK,CAAC,QAAQ,UAAU;gDAAE;;;;;;;;;;;;;;;;;;;sCAOxC,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAO,WAAU;;sDAChB,8OAAC,kMAAA,CAAA,OAAI;4CAAC,WAAU;;;;;;sDAChB,8OAAC;sDAAK;;;;;;;;;;;;8CAGR,8OAAC;oCAAO,WAAU;;sDAChB,8OAAC,0MAAA,CAAA,WAAQ;4CAAC,WAAU;;;;;;sDACpB,8OAAC;sDAAK;;;;;;;;;;;;8CAGR,8OAAC;oCAAO,WAAU;;sDAChB,8OAAC,oMAAA,CAAA,QAAK;4CAAC,WAAU;;;;;;sDACjB,8OAAC;sDAAK;;;;;;;;;;;;8CAGR,8OAAC;oCAAO,WAAU;;sDAChB,8OAAC,0MAAA,CAAA,SAAM;4CAAC,WAAU;;;;;;sDAClB,8OAAC;sDAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOpB;uCAEe", "debugId": null}}, {"offset": {"line": 1618, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/New%20folder%20%2825%29/streamzen/src/components/EpisodeSelector.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\nimport { useRouter, useSearchParams } from 'next/navigation';\nimport { Episode } from '@/types';\n\ninterface EpisodeSelectorProps {\n  seriesId: string;\n  episodes: Episode[];\n  currentSeason: number;\n  currentEpisode: number;\n}\n\nexport default function EpisodeSelector({\n  seriesId,\n  episodes,\n  currentSeason,\n  currentEpisode\n}: EpisodeSelectorProps) {\n  const router = useRouter();\n  const searchParams = useSearchParams();\n  const [selectedSeason, setSelectedSeason] = useState(currentSeason);\n\n  // Group episodes by season\n  const episodesBySeason = episodes.reduce((acc, episode) => {\n    if (!acc[episode.season]) {\n      acc[episode.season] = [];\n    }\n    acc[episode.season].push(episode);\n    return acc;\n  }, {} as Record<number, Episode[]>);\n\n  // Get available seasons\n  const availableSeasons = Object.keys(episodesBySeason)\n    .map(Number)\n    .sort((a, b) => a - b);\n\n  // Get episodes for selected season\n  const currentSeasonEpisodes = episodesBySeason[selectedSeason] || [];\n\n  const handleSeasonChange = (season: number) => {\n    setSelectedSeason(season);\n    // Navigate to first episode of the selected season\n    const firstEpisode = episodesBySeason[season]?.[0];\n    if (firstEpisode) {\n      router.push(`/watch/series/${seriesId}?season=${season}&episode=${firstEpisode.episode}`);\n    }\n  };\n\n  const handleEpisodeChange = (episode: number) => {\n    router.push(`/watch/series/${seriesId}?season=${selectedSeason}&episode=${episode}`);\n  };\n\n  return (\n    <div className=\"glass-elevated p-6 rounded-2xl border border-gray-700/50\">\n      <h3 className=\"text-xl font-bold text-white mb-4\">Episodes</h3>\n      \n      {/* Season Selector */}\n      {availableSeasons.length > 1 && (\n        <div className=\"mb-6\">\n          <label className=\"block text-sm font-medium text-gray-300 mb-2\">\n            Season\n          </label>\n          <select\n            value={selectedSeason}\n            onChange={(e) => handleSeasonChange(Number(e.target.value))}\n            className=\"w-full bg-gray-800/50 border border-gray-600 rounded-lg px-3 py-2 text-white focus:outline-none focus:ring-2 focus:ring-blue-500\"\n          >\n            {availableSeasons.map((season) => (\n              <option key={season} value={season}>\n                Season {season}\n              </option>\n            ))}\n          </select>\n        </div>\n      )}\n\n      {/* Episode Grid */}\n      <div className=\"grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 gap-3\">\n        {currentSeasonEpisodes\n          .sort((a, b) => a.episode - b.episode)\n          .map((episode) => {\n            const isActive = episode.season === currentSeason && episode.episode === currentEpisode;\n            \n            return (\n              <button\n                key={`${episode.season}-${episode.episode}`}\n                onClick={() => handleEpisodeChange(episode.episode)}\n                className={`\n                  relative p-3 rounded-lg border transition-all duration-200 text-left\n                  ${isActive \n                    ? 'bg-blue-600 border-blue-500 text-white' \n                    : 'bg-gray-800/50 border-gray-600 text-gray-300 hover:bg-gray-700/50 hover:border-gray-500'\n                  }\n                `}\n              >\n                <div className=\"font-medium text-sm\">\n                  Episode {episode.episode}\n                </div>\n                {episode.episodeTitle && (\n                  <div className={`text-xs mt-1 line-clamp-2 ${isActive ? 'text-blue-100' : 'text-gray-400'}`}>\n                    {episode.episodeTitle}\n                  </div>\n                )}\n                {episode.runtime && (\n                  <div className={`text-xs mt-1 ${isActive ? 'text-blue-200' : 'text-gray-500'}`}>\n                    {episode.runtime}\n                  </div>\n                )}\n                \n                {/* Active indicator */}\n                {isActive && (\n                  <div className=\"absolute top-1 right-1\">\n                    <div className=\"w-2 h-2 bg-white rounded-full\"></div>\n                  </div>\n                )}\n              </button>\n            );\n          })}\n      </div>\n\n      {/* Episode count info */}\n      <div className=\"mt-4 text-sm text-gray-400\">\n        {currentSeasonEpisodes.length} episode{currentSeasonEpisodes.length !== 1 ? 's' : ''} in Season {selectedSeason}\n      </div>\n\n      {/* Navigation buttons */}\n      <div className=\"flex justify-between mt-6\">\n        <button\n          onClick={() => {\n            const prevEpisode = currentSeasonEpisodes.find(ep => ep.episode === currentEpisode - 1);\n            if (prevEpisode) {\n              handleEpisodeChange(prevEpisode.episode);\n            }\n          }}\n          disabled={!currentSeasonEpisodes.find(ep => ep.episode === currentEpisode - 1)}\n          className=\"px-4 py-2 bg-gray-700 text-white rounded-lg disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-600 transition-colors\"\n        >\n          ← Previous\n        </button>\n        \n        <button\n          onClick={() => {\n            const nextEpisode = currentSeasonEpisodes.find(ep => ep.episode === currentEpisode + 1);\n            if (nextEpisode) {\n              handleEpisodeChange(nextEpisode.episode);\n            }\n          }}\n          disabled={!currentSeasonEpisodes.find(ep => ep.episode === currentEpisode + 1)}\n          className=\"px-4 py-2 bg-blue-600 text-white rounded-lg disabled:opacity-50 disabled:cursor-not-allowed hover:bg-blue-500 transition-colors\"\n        >\n          Next →\n        </button>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAHA;;;;AAae,SAAS,gBAAgB,EACtC,QAAQ,EACR,QAAQ,EACR,aAAa,EACb,cAAc,EACO;IACrB,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,eAAe,CAAA,GAAA,kIAAA,CAAA,kBAAe,AAAD;IACnC,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAErD,2BAA2B;IAC3B,MAAM,mBAAmB,SAAS,MAAM,CAAC,CAAC,KAAK;QAC7C,IAAI,CAAC,GAAG,CAAC,QAAQ,MAAM,CAAC,EAAE;YACxB,GAAG,CAAC,QAAQ,MAAM,CAAC,GAAG,EAAE;QAC1B;QACA,GAAG,CAAC,QAAQ,MAAM,CAAC,CAAC,IAAI,CAAC;QACzB,OAAO;IACT,GAAG,CAAC;IAEJ,wBAAwB;IACxB,MAAM,mBAAmB,OAAO,IAAI,CAAC,kBAClC,GAAG,CAAC,QACJ,IAAI,CAAC,CAAC,GAAG,IAAM,IAAI;IAEtB,mCAAmC;IACnC,MAAM,wBAAwB,gBAAgB,CAAC,eAAe,IAAI,EAAE;IAEpE,MAAM,qBAAqB,CAAC;QAC1B,kBAAkB;QAClB,mDAAmD;QACnD,MAAM,eAAe,gBAAgB,CAAC,OAAO,EAAE,CAAC,EAAE;QAClD,IAAI,cAAc;YAChB,OAAO,IAAI,CAAC,CAAC,cAAc,EAAE,SAAS,QAAQ,EAAE,OAAO,SAAS,EAAE,aAAa,OAAO,EAAE;QAC1F;IACF;IAEA,MAAM,sBAAsB,CAAC;QAC3B,OAAO,IAAI,CAAC,CAAC,cAAc,EAAE,SAAS,QAAQ,EAAE,eAAe,SAAS,EAAE,SAAS;IACrF;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAG,WAAU;0BAAoC;;;;;;YAGjD,iBAAiB,MAAM,GAAG,mBACzB,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAM,WAAU;kCAA+C;;;;;;kCAGhE,8OAAC;wBACC,OAAO;wBACP,UAAU,CAAC,IAAM,mBAAmB,OAAO,EAAE,MAAM,CAAC,KAAK;wBACzD,WAAU;kCAET,iBAAiB,GAAG,CAAC,CAAC,uBACrB,8OAAC;gCAAoB,OAAO;;oCAAQ;oCAC1B;;+BADG;;;;;;;;;;;;;;;;0BASrB,8OAAC;gBAAI,WAAU;0BACZ,sBACE,IAAI,CAAC,CAAC,GAAG,IAAM,EAAE,OAAO,GAAG,EAAE,OAAO,EACpC,GAAG,CAAC,CAAC;oBACJ,MAAM,WAAW,QAAQ,MAAM,KAAK,iBAAiB,QAAQ,OAAO,KAAK;oBAEzE,qBACE,8OAAC;wBAEC,SAAS,IAAM,oBAAoB,QAAQ,OAAO;wBAClD,WAAW,CAAC;;kBAEV,EAAE,WACE,2CACA,0FACH;gBACH,CAAC;;0CAED,8OAAC;gCAAI,WAAU;;oCAAsB;oCAC1B,QAAQ,OAAO;;;;;;;4BAEzB,QAAQ,YAAY,kBACnB,8OAAC;gCAAI,WAAW,CAAC,0BAA0B,EAAE,WAAW,kBAAkB,iBAAiB;0CACxF,QAAQ,YAAY;;;;;;4BAGxB,QAAQ,OAAO,kBACd,8OAAC;gCAAI,WAAW,CAAC,aAAa,EAAE,WAAW,kBAAkB,iBAAiB;0CAC3E,QAAQ,OAAO;;;;;;4BAKnB,0BACC,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAU;;;;;;;;;;;;uBA3Bd,GAAG,QAAQ,MAAM,CAAC,CAAC,EAAE,QAAQ,OAAO,EAAE;;;;;gBAgCjD;;;;;;0BAIJ,8OAAC;gBAAI,WAAU;;oBACZ,sBAAsB,MAAM;oBAAC;oBAAS,sBAAsB,MAAM,KAAK,IAAI,MAAM;oBAAG;oBAAY;;;;;;;0BAInG,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBACC,SAAS;4BACP,MAAM,cAAc,sBAAsB,IAAI,CAAC,CAAA,KAAM,GAAG,OAAO,KAAK,iBAAiB;4BACrF,IAAI,aAAa;gCACf,oBAAoB,YAAY,OAAO;4BACzC;wBACF;wBACA,UAAU,CAAC,sBAAsB,IAAI,CAAC,CAAA,KAAM,GAAG,OAAO,KAAK,iBAAiB;wBAC5E,WAAU;kCACX;;;;;;kCAID,8OAAC;wBACC,SAAS;4BACP,MAAM,cAAc,sBAAsB,IAAI,CAAC,CAAA,KAAM,GAAG,OAAO,KAAK,iBAAiB;4BACrF,IAAI,aAAa;gCACf,oBAAoB,YAAY,OAAO;4BACzC;wBACF;wBACA,UAAU,CAAC,sBAAsB,IAAI,CAAC,CAAA,KAAM,GAAG,OAAO,KAAK,iBAAiB;wBAC5E,WAAU;kCACX;;;;;;;;;;;;;;;;;;AAMT", "debugId": null}}]}