'use client';

import React, { useRef } from 'react';
import <PERSON> from 'next/link';
import { Github, Twitter, Instagram, Youtube, Mail, Heart, Zap, Play, Sparkles, TrendingUp, Star, Globe } from 'lucide-react';
import { motion, useInView } from 'framer-motion';

const Footer: React.FC = () => {
  const currentYear = new Date().getFullYear();
  const footerRef = useRef<HTMLElement>(null);
  const isInView = useInView(footerRef, { once: true, margin: "-100px" });

  const footerLinks = {
    platform: [
      { label: 'Movies', href: '/movies' },
      { label: 'TV Series', href: '/series' },
      { label: 'Latest Episodes', href: '/episodes' },
      { label: 'Search', href: '/search' }
    ],
    genres: [
      { label: 'Action Movies', href: '/movies?genre=Action' },
      { label: 'Comedy Series', href: '/series?genre=Comedy' },
      { label: 'Horror Movies', href: '/movies?genre=Horror' },
      { label: 'Drama Series', href: '/series?genre=Drama' }
    ],
    popular: [
      { label: 'Latest Movies', href: '/movies?sortBy=createdAt&sortOrder=desc' },
      { label: 'Top Rated', href: '/movies?sortBy=imdbRating&sortOrder=desc' },
      { label: 'New Series', href: '/series?sortBy=createdAt&sortOrder=desc' },
      { label: 'Trending Now', href: '/movies?sortBy=popularity&sortOrder=desc' }
    ]
  };

  const socialLinks = [
    { icon: Github, href: 'https://github.com', label: 'GitHub' },
    { icon: Twitter, href: 'https://twitter.com', label: 'Twitter' },
    { icon: Instagram, href: 'https://instagram.com', label: 'Instagram' },
    { icon: Youtube, href: 'https://youtube.com', label: 'YouTube' }
  ];

  return (
    <motion.footer
      ref={footerRef}
      initial={{ opacity: 0 }}
      animate={isInView ? { opacity: 1 } : { opacity: 0 }}
      transition={{ duration: 0.8 }}
      className="relative bg-gradient-to-b from-black via-gray-900/50 to-black border-t border-white/10"
    >
      {/* Enhanced Background Effects */}
      <div className="absolute inset-0 overflow-hidden">
        <motion.div
          className="absolute -top-40 -left-40 w-80 h-80 bg-blue-600/10 rounded-full blur-3xl"
          animate={{
            scale: [1, 1.2, 1],
            opacity: [0.1, 0.2, 0.1]
          }}
          transition={{ duration: 8, repeat: Infinity, ease: "easeInOut" }}
        />
        <motion.div
          className="absolute -top-40 -right-40 w-80 h-80 bg-purple-600/10 rounded-full blur-3xl"
          animate={{
            scale: [1.2, 1, 1.2],
            opacity: [0.2, 0.1, 0.2]
          }}
          transition={{ duration: 10, repeat: Infinity, ease: "easeInOut" }}
        />
        <motion.div
          className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-96 h-96 bg-red-600/5 rounded-full blur-3xl"
          animate={{
            rotate: [0, 360],
            scale: [1, 1.1, 1]
          }}
          transition={{ duration: 20, repeat: Infinity, ease: "linear" }}
        />

        {/* Floating particles */}
        {[...Array(12)].map((_, i) => (
          <motion.div
            key={i}
            className="absolute w-1 h-1 bg-white/20 rounded-full"
            style={{
              left: `${Math.random() * 100}%`,
              top: `${Math.random() * 100}%`,
            }}
            animate={{
              y: [0, -20, 0],
              opacity: [0, 1, 0],
            }}
            transition={{
              duration: 3 + Math.random() * 2,
              repeat: Infinity,
              delay: Math.random() * 2,
            }}
          />
        ))}
      </div>

      <div className="relative max-w-[2560px] mx-auto px-6 lg:px-12">
        {/* Enhanced Main Footer Content */}
        <div className="py-20">
          <div className="grid grid-cols-1 lg:grid-cols-4 gap-16">
            {/* Enhanced Brand Section */}
            <motion.div
              className="lg:col-span-1"
              initial={{ opacity: 0, y: 30 }}
              animate={isInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 30 }}
              transition={{ duration: 0.6, delay: 0.2 }}
            >
              <div className="flex items-center space-x-4 mb-8">
                <motion.div
                  className="relative group"
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                >
                  <div className="w-14 h-14 bg-gradient-to-br from-red-500 to-red-600 rounded-2xl flex items-center justify-center shadow-2xl">
                    <Play size={28} className="text-white fill-current ml-1" />
                  </div>
                  <div className="absolute -inset-1 bg-gradient-to-br from-red-500/30 to-red-600/30 rounded-2xl blur opacity-0 group-hover:opacity-100 transition-all duration-300" />

                  {/* Floating sparkles */}
                  <motion.div
                    className="absolute -top-1 -right-1 w-3 h-3"
                    animate={{
                      rotate: [0, 360],
                      scale: [1, 1.2, 1]
                    }}
                    transition={{
                      duration: 3,
                      repeat: Infinity,
                      ease: "easeInOut"
                    }}
                  >
                    <Sparkles size={12} className="text-yellow-400" />
                  </motion.div>
                </motion.div>
                <div>
                  <motion.h3
                    className="text-white text-3xl font-black tracking-tight"
                    whileHover={{ scale: 1.02 }}
                  >
                    free<span className="text-red-500">Movies</span>WatchNow
                  </motion.h3>
                  <p className="text-gray-400 text-sm font-medium flex items-center space-x-1">
                    <span>Premium Entertainment</span>
                    <TrendingUp size={12} className="text-green-400" />
                  </p>
                </div>
              </div>
              
              <motion.p
                className="text-gray-400 text-lg leading-relaxed mb-8"
                initial={{ opacity: 0 }}
                animate={isInView ? { opacity: 1 } : { opacity: 0 }}
                transition={{ duration: 0.6, delay: 0.4 }}
              >
                Experience the future of streaming with our premium platform. Unlimited movies, series, and episodes at your fingertips with crystal-clear quality and lightning-fast loading.
              </motion.p>

              {/* Enhanced Social Links */}
              <motion.div
                className="flex items-center space-x-3"
                initial={{ opacity: 0, y: 20 }}
                animate={isInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 20 }}
                transition={{ duration: 0.6, delay: 0.6 }}
              >
                {socialLinks.map((social, index) => {
                  const Icon = social.icon;
                  return (
                    <motion.a
                      key={social.label}
                      href={social.href}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="w-12 h-12 bg-gray-900/50 backdrop-blur-sm border border-gray-700/50 rounded-2xl flex items-center justify-center text-gray-400 hover:text-white hover:bg-white/10 hover:border-white/20 transition-all duration-300 focus-ring group"
                      whileHover={{ scale: 1.1, y: -2 }}
                      whileTap={{ scale: 0.95 }}
                      initial={{ opacity: 0, y: 20 }}
                      animate={isInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 20 }}
                      transition={{ duration: 0.3, delay: 0.7 + index * 0.1 }}
                    >
                      <Icon size={20} className="group-hover:scale-110 transition-transform duration-300" />
                    </motion.a>
                  );
                })}
              </motion.div>
            </motion.div>

            {/* Links Sections */}
            <div className="lg:col-span-3">
              <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
                {/* Platform Links */}
                <div>
                  <h4 className="text-white text-xl font-bold mb-6 flex items-center">
                    <Zap size={20} className="mr-2 text-blue-400" />
                    Browse
                  </h4>
                  <ul className="space-y-4">
                    {footerLinks.platform.map((link) => (
                      <li key={link.href}>
                        <Link
                          href={link.href}
                          className="text-gray-400 hover:text-white transition-colors duration-300 text-lg font-medium hover:translate-x-1 transform transition-transform"
                        >
                          {link.label}
                        </Link>
                      </li>
                    ))}
                  </ul>
                </div>

                {/* Genres Links */}
                <div>
                  <h4 className="text-white text-xl font-bold mb-6">Popular Genres</h4>
                  <ul className="space-y-4">
                    {footerLinks.genres.map((link) => (
                      <li key={link.href}>
                        <Link
                          href={link.href}
                          className="text-gray-400 hover:text-white transition-colors duration-300 text-lg font-medium hover:translate-x-1 transform transition-transform"
                        >
                          {link.label}
                        </Link>
                      </li>
                    ))}
                  </ul>
                </div>

                {/* Popular Content Links */}
                <div>
                  <h4 className="text-white text-xl font-bold mb-6">Trending</h4>
                  <ul className="space-y-4">
                    {footerLinks.popular.map((link) => (
                      <li key={link.href}>
                        <Link
                          href={link.href}
                          className="text-gray-400 hover:text-white transition-colors duration-300 text-lg font-medium hover:translate-x-1 transform transition-transform"
                        >
                          {link.label}
                        </Link>
                      </li>
                    ))}
                  </ul>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* SEO Content Section */}
        <div className="py-12 border-t border-white/10">
          <div className="text-center">
            <h4 className="text-white text-2xl font-bold mb-4">Watch Movies and TV Series Online Free</h4>
            <p className="text-gray-400 text-lg max-w-4xl mx-auto leading-relaxed">
              freeMoviesWatchNow is your ultimate destination for watching movies and TV series online free in HD quality.
              Discover thousands of movies, binge-watch your favorite series, and catch up on the latest episodes.
              Our platform offers a premium streaming experience with multiple sources and high-quality content
              from various genres including action, comedy, drama, horror, thriller, romance, and more.
            </p>
          </div>
        </div>

        {/* Bottom Bar */}
        <div className="py-8 border-t border-white/10">
          <div className="flex flex-col lg:flex-row items-center justify-between space-y-4 lg:space-y-0">
            <div className="flex items-center space-x-2 text-gray-400 text-lg">
              <span>© {currentYear} freeMoviesWatchNow. Made with</span>
              <Heart size={16} className="text-red-500 animate-pulse" />
              <span>for entertainment lovers.</span>
            </div>
            
            <div className="flex items-center space-x-8 text-gray-400">
              <span className="text-lg">All rights reserved</span>
              <div className="flex items-center space-x-2">
                <div className="w-2 h-2 bg-green-400 rounded-full animate-pulse" />
                <span className="text-lg font-medium">All systems operational</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </footer>
  );
};

export default Footer;
