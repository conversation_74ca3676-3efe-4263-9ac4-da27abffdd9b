'use client';

import React from 'react';
import Link from 'next/link';
import { Github, Twitter, Instagram, Youtube, Mail, Heart, Zap, Play } from 'lucide-react';

const Footer: React.FC = () => {
  const currentYear = new Date().getFullYear();

  const footerLinks = {
    platform: [
      { label: 'Movies', href: '/movies' },
      { label: 'TV Series', href: '/series' },
      { label: 'Latest Episodes', href: '/episodes' },
      { label: 'Search', href: '/search' }
    ],
    genres: [
      { label: 'Action Movies', href: '/movies?genre=Action' },
      { label: 'Comedy Series', href: '/series?genre=Comedy' },
      { label: 'Horror Movies', href: '/movies?genre=Horror' },
      { label: 'Drama Series', href: '/series?genre=Drama' }
    ],
    popular: [
      { label: 'Latest Movies', href: '/movies?sortBy=createdAt&sortOrder=desc' },
      { label: 'Top Rated', href: '/movies?sortBy=imdbRating&sortOrder=desc' },
      { label: 'New Series', href: '/series?sortBy=createdAt&sortOrder=desc' },
      { label: 'Trending Now', href: '/movies?sortBy=popularity&sortOrder=desc' }
    ]
  };

  const socialLinks = [
    { icon: Github, href: 'https://github.com', label: 'GitHub' },
    { icon: Twitter, href: 'https://twitter.com', label: 'Twitter' },
    { icon: Instagram, href: 'https://instagram.com', label: 'Instagram' },
    { icon: Youtube, href: 'https://youtube.com', label: 'YouTube' }
  ];

  return (
    <footer className="relative bg-black border-t border-white/5">
      {/* Premium Background Effects */}
      <div className="absolute inset-0 overflow-hidden">
        <div className="absolute -top-40 -left-40 w-80 h-80 bg-gray-800/15 rounded-full blur-3xl" />
        <div className="absolute -top-40 -right-40 w-80 h-80 bg-gray-700/15 rounded-full blur-3xl" />
        <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-96 h-96 bg-gray-600/10 rounded-full blur-3xl" />
      </div>

      <div className="relative max-w-[1920px] mx-auto px-8 lg:px-16">
        {/* Main Footer Content */}
        <div className="py-16">
          <div className="grid grid-cols-1 lg:grid-cols-4 gap-12">
            {/* Brand Section */}
            <div className="lg:col-span-1">
              <div className="flex items-center space-x-4 mb-6">
                <div className="relative group">
                  <div className="w-12 h-12 bg-white rounded-2xl flex items-center justify-center shadow-2xl">
                    <Play size={24} className="text-black fill-current ml-1" />
                  </div>
                  <div className="absolute -inset-1 bg-white/20 rounded-2xl blur opacity-75" />
                </div>
                <div>
                  <h3 className="text-white text-2xl font-black tracking-tight">Stream<span className="text-red-600">Zen</span></h3>
                  <p className="text-gray-400 text-sm font-medium">Premium Entertainment</p>
                </div>
              </div>
              
              <p className="text-gray-400 text-lg leading-relaxed mb-8">
                Experience the future of streaming with our premium platform. Unlimited movies, series, and episodes at your fingertips.
              </p>

              {/* Social Links */}
              <div className="flex items-center space-x-4">
                {socialLinks.map((social) => {
                  const Icon = social.icon;
                  return (
                    <a
                      key={social.label}
                      href={social.href}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="w-12 h-12 glass-elevated rounded-2xl flex items-center justify-center text-gray-400 hover:text-white hover:bg-white/10 transition-all duration-300 focus-ring group"
                    >
                      <Icon size={20} className="group-hover:scale-110 transition-transform duration-300" />
                    </a>
                  );
                })}
              </div>
            </div>

            {/* Links Sections */}
            <div className="lg:col-span-3">
              <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
                {/* Platform Links */}
                <div>
                  <h4 className="text-white text-xl font-bold mb-6 flex items-center">
                    <Zap size={20} className="mr-2 text-blue-400" />
                    Browse
                  </h4>
                  <ul className="space-y-4">
                    {footerLinks.platform.map((link) => (
                      <li key={link.href}>
                        <Link
                          href={link.href}
                          className="text-gray-400 hover:text-white transition-colors duration-300 text-lg font-medium hover:translate-x-1 transform transition-transform"
                        >
                          {link.label}
                        </Link>
                      </li>
                    ))}
                  </ul>
                </div>

                {/* Genres Links */}
                <div>
                  <h4 className="text-white text-xl font-bold mb-6">Popular Genres</h4>
                  <ul className="space-y-4">
                    {footerLinks.genres.map((link) => (
                      <li key={link.href}>
                        <Link
                          href={link.href}
                          className="text-gray-400 hover:text-white transition-colors duration-300 text-lg font-medium hover:translate-x-1 transform transition-transform"
                        >
                          {link.label}
                        </Link>
                      </li>
                    ))}
                  </ul>
                </div>

                {/* Popular Content Links */}
                <div>
                  <h4 className="text-white text-xl font-bold mb-6">Trending</h4>
                  <ul className="space-y-4">
                    {footerLinks.popular.map((link) => (
                      <li key={link.href}>
                        <Link
                          href={link.href}
                          className="text-gray-400 hover:text-white transition-colors duration-300 text-lg font-medium hover:translate-x-1 transform transition-transform"
                        >
                          {link.label}
                        </Link>
                      </li>
                    ))}
                  </ul>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* SEO Content Section */}
        <div className="py-12 border-t border-white/10">
          <div className="text-center">
            <h4 className="text-white text-2xl font-bold mb-4">Watch Movies and TV Series Online Free</h4>
            <p className="text-gray-400 text-lg max-w-4xl mx-auto leading-relaxed">
              freeMoviesWatchNow is your ultimate destination for watching movies and TV series online free in HD quality.
              Discover thousands of movies, binge-watch your favorite series, and catch up on the latest episodes.
              Our platform offers a premium streaming experience with multiple sources and high-quality content
              from various genres including action, comedy, drama, horror, thriller, romance, and more.
            </p>
          </div>
        </div>

        {/* Bottom Bar */}
        <div className="py-8 border-t border-white/10">
          <div className="flex flex-col lg:flex-row items-center justify-between space-y-4 lg:space-y-0">
            <div className="flex items-center space-x-2 text-gray-400 text-lg">
              <span>© {currentYear} freeMoviesWatchNow. Made with</span>
              <Heart size={16} className="text-red-500 animate-pulse" />
              <span>for entertainment lovers.</span>
            </div>
            
            <div className="flex items-center space-x-8 text-gray-400">
              <span className="text-lg">All rights reserved</span>
              <div className="flex items-center space-x-2">
                <div className="w-2 h-2 bg-green-400 rounded-full animate-pulse" />
                <span className="text-lg font-medium">All systems operational</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </footer>
  );
};

export default Footer;
