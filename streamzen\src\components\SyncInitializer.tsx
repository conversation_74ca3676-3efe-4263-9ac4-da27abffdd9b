'use client';

import { useEffect } from 'react';

const SyncInitializer: React.FC = () => {
  useEffect(() => {
    // Initialize VidSrc sync service on client side
    const initializeSync = async () => {
      try {
        // Import dynamically to avoid SSR issues
        const { default: VidSrcSyncService } = await import('@/lib/vidsrcSync');
        const syncService = VidSrcSyncService.getInstance();
        await syncService.initialize();
        console.log('✅ VidSrc Sync Service initialized successfully');
      } catch (error) {
        console.error('❌ Failed to initialize VidSrc Sync Service:', error);
      }
    };

    initializeSync();

    // Cleanup on unmount
    return () => {
      import('@/lib/vidsrcSync').then(({ default: VidSrcSyncService }) => {
        const syncService = VidSrcSyncService.getInstance();
        syncService.destroy();
      }).catch(console.error);
    };
  }, []);

  return null; // This component doesn't render anything
};

export default SyncInitializer;
