'use client';

import { useEffect } from 'react';

const SyncInitializer: React.FC = () => {
  useEffect(() => {
    // Initialize VidSrc sync service via API call instead of direct import
    const initializeSync = async () => {
      try {
        // Call the sync API endpoint instead of importing server-side code
        console.log('🔄 Initializing VidSrc Sync Service...');

        // Only run in development or when explicitly needed
        if (process.env.NODE_ENV === 'development') {
          const response = await fetch('/api/admin/sync-episodes', {
            method: 'POST',
          });

          if (response.ok) {
            const result = await response.json();
            console.log('✅ VidSrc Sync Service initialized successfully:', result);
          } else {
            console.warn('⚠️ VidSrc Sync Service initialization failed:', response.status);
          }
        } else {
          console.log('ℹ️ VidSrc Sync Service skipped in production (use manual trigger)');
        }
      } catch (error) {
        console.error('❌ Failed to initialize VidSrc Sync Service:', error);
      }
    };

    initializeSync();
  }, []);

  return null; // This component doesn't render anything
};

export default SyncInitializer;
